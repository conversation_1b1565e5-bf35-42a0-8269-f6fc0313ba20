{"targetSuggestions": ["transport-layer-specifications.md", "observability-monitoring-framework.md"], "contentBlock": "# Messaging Backplanes Technical Insights\n\n## Latency Performance Data\n\n### NATS Benchmarks\n- **Core NATS**: Up to 3+ million msgs/sec, microseconds to low milliseconds latency\n- **JetStream (persistent)**: ~200k msgs/sec due to fsync overhead\n- **P99 Latency**: Low milliseconds even under thousands msgs/sec load\n- **Failure Mode**: Publishers can drop messages if server can't fsync fast enough under heavy load\n\n### Comparative Latencies\n- **Kafka**: P99 ~5ms at 200MB/s throughput, 2-10ms baseline (batching/fsync)\n- **RabbitMQ**: Sub-millisecond at low load, degrades to hundreds/thousands ms P99 under pressure\n- **Pulsar**: P99 40x better than NATS JetStream, 300x better than RabbitMQ at scale\n- **Redis PubSub**: <1ms typical, stays low until CPU/network saturation\n- **gRPC/WebSocket**: ~0.2-0.5ms RTT in same datacenter (lowest latency potential)\n\n## Durability Patterns\n\n### At-Least-Once Delivery\n- **NATS JetStream**: Acknowledgments, replay capability, exactly-once processing options\n- **Kafka/Pulsar**: Persist everything, configurable retention, replay from any offset\n- **RabbitMQ**: Manual acks required, quorum queues for replication (Raft-based)\n- **Redis Streams**: Durable until trimmed, consumer groups with pending message tracking\n\n### Fire-and-Forget\n- **Core NATS**: At-most-once, no broker ACK, messages lost if subscriber down\n- **Redis PubSub**: No durability, messages vanish if subscriber not connected\n- **gRPC/WebSocket**: Direct streams, no automatic store, must implement app-level ACKs\n\n## Subject Naming Conventions (NATS)\n\n### Hierarchical Patterns\n- `task.*` - Wildcard subscription for all task types\n- `task.agent42` - Specific agent task routing\n- `agent.{id}.status` - Agent status updates\n- `cmd.{type}.{target}` - Command routing by type and target\n\n### NATS Features\n- Wildcard subscriptions supported (`*` single token, `>` multiple tokens)\n- Request-reply patterns native (built-in correlation)\n- Order preserved per publisher connection\n- Subjects are simple strings (no complex broker configs)\n\n## Back-Pressure Tactics\n\n### Connection Management\n- **NATS**: Drops slow subscriber connections to prevent memory buildup\n- **Redis**: Disconnects clients exceeding output buffer (32MB default)\n- **RabbitMQ**: Flow control, consumer prefetch limits\n\n### Flow Control\n- **gRPC**: HTTP/2 flow control windows, sender blocks if receiver overwhelmed\n- **Kafka**: Backpressure via consumer lag, producers can be throttled\n- **NATS JetStream**: Pull consumers for controlled consumption rate\n\n## Monitoring Metrics Patterns\n\n### Key Observability Points\n```\n# Consumer Health\n- consumer_lag_ms\n- pending_message_count\n- ack_rate_per_second\n- reconnection_count\n\n# Broker Performance  \n- fsync_latency_p99\n- disk_write_queue_depth\n- memory_buffer_utilization\n- active_connection_count\n\n# Message Flow\n- publish_rate_per_topic\n- fan_out_multiplier\n- message_size_p95\n- throughput_mb_per_second\n\n# System Health\n- broker_cpu_percentage\n- jvm_gc_pause_ms (Kafka)\n- network_retransmits\n- partition_leader_changes\n```\n\n### Critical Thresholds\n- **NATS JetStream**: Monitor when fsync can't keep up (~50M msgs/day with 20MB messages)\n- **RabbitMQ**: Watch for queue depths > 10k messages (performance degradation)\n- **Redis**: Output buffer approaching limits (client disconnect risk)\n- **Kafka**: Consumer lag > 1000 messages or > 30 seconds\n\n## Architectural Recommendations\n\n### Low-Latency Patterns\n1. Use NATS core for ephemeral control messages\n2. Direct gRPC/WebSocket for streaming outputs\n3. Avoid persistence for real-time telemetry\n\n### Durability Requirements\n1. Kafka/Pulsar for audit trails and replay needs\n2. NATS JetStream for balanced performance/durability\n3. RabbitMQ quorum queues for work queue patterns\n\n### Scale Considerations\n- **<100k msgs/sec**: NATS, RabbitMQ, Redis viable\n- **>1M msgs/sec**: Kafka, Pulsar, or sharded NATS\n- **Burst handling**: Pre-allocate buffers, use pull consumers"}