## Dev-Container & Local Setup

* **devcontainer.json**: use a Dockerfile with multi-stage Rust build.  E.g. include `Dockerfile` with build and runtime stages. Use BuildKit cache mounts for Cargo (speeds up rebuilds).  Example snippet:

```json
{
  "name": "Rust MultiAgent Dev",
  "build": { "dockerfile": "Dockerfile" },
  "postCreateCommand": "cargo build --release",
  "extensions": ["rust-lang.rust", "ms-vscode-remote.remote-containers"]
}
```

* **Dockerfile** (in `.devcontainer/`):

```dockerfile
# syntax=docker/dockerfile:1.3-labs
FROM rust:1.72 AS builder
WORKDIR /app
COPY Cargo.toml Cargo.lock ./
RUN --mount=type=cache,target=/usr/local/cargo/registry cargo build --release
COPY . .
RUN --mount=type=cache,target=/usr/local/cargo/registry cargo build --release
FROM debian:bookworm-slim AS runtime
COPY --from=builder /app/target/release/app /usr/local/bin/app
CMD ["app"]
```

*(Use `--mount=type=cache` on `/usr/local/cargo/registry` in each `cargo build` for caching.)*

* **VS Code tasks.json**: define tasks to launch NATS and orchestrator. Example:

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start NATS & Orchestrator",
      "type": "shell",
      "command": "docker-compose up -d nats orchestrator",
      "problemMatcher": []
    }
  ]
}
```

* **docker-compose.yml** (local cluster): use Compose for fast single-node dev (iterate services quickly). Example:

```yaml
version: '3.8'
services:
  nats:
    image: nats:latest
    ports:
      - "4222:4222"    # NATS client port
      - "8222:8222"    # NATS monitoring port
  orchestrator:
    build: .
    depends_on: [nats]
    command: cargo run --release
```

*(For local dev, Docker Compose “really is the clear choice” due to speed; use KinD (Kubernetes) when you need true k8s behavior like multi-node tests.)*

## Kubernetes Deployments & Autoscaling

* **Orchestrator Deployment (stateless Rust)**:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata: {name: orchestrator}
spec:
  replicas: 3
  selector: {matchLabels: {app: orchestrator}}
  template:
    metadata: {labels: {app: orchestrator}}
    spec:
      containers:
      - name: orchestrator
        image: myregistry/orchestrator:latest
        resources:
          requests: {cpu: "500m", memory: "512Mi"}
          limits:   {cpu: "1",   memory: "512Mi"}
```

*Memory requests equal limits (for stability).  CPU request \~50% of limit (to allow bursting).  Set Tokio threads ≈2×CPUs+1.*

* **NATS StatefulSet (JetStream + core)**:

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata: {name: nats-server}
spec:
  serviceName: nats
  replicas: 3
  selector: {matchLabels: {app: nats}}
  template:
    metadata: {labels: {app: nats}}
    spec:
      containers:
      - name: nats-server
        image: nats:latest
        args: ["-js"]  # enable JetStream
        ports:
          - containerPort: 4222
          - containerPort: 8222
        resources:
          requests: {cpu: "200m", memory: "256Mi"}
          limits:   {cpu: "500m", memory: "512Mi"}
```

*(Scale NATS on message backlog/throughput.  Core NATS can run with small CPU (200m); JetStream may need more memory (\~1Gi+) for streams.  Set memory req=lim.)*

* **HorizontalPodAutoscaler (Orchestrator)**:

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata: {name: orchestrator-hpa}
spec:
  scaleTargetRef: {apiVersion: apps/v1, kind: Deployment, name: orchestrator}
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
```

*(Target \~75% CPU to balance cost vs latency – avoids thrashing.  Consider adding custom metrics (e.g. NATS queue length) as needed.)*

## Sizing Heuristics & Ratios

| Component      | CPU Req |  CPU Limit |   Memory  | Notes                                                                              |
| :------------- | :-----: | :--------: | :-------: | :--------------------------------------------------------------------------------- |
| Orchestrator   |   500m  |    1 CPU   |   512Mi   | Rust async (threads ≈2×CPUs+1)                                                     |
| NATS Core      |   200m  |    500m    |   256Mi   | Lightweight NATS server                                                            |
| NATS JetStream |   500m  |    1 CPU   |   \~1Gi   | Durable streaming (GC tuning via GOMEMLIMIT)                                       |
| Claude Agent   |   250m  | 500m-1 CPU | 256-512Mi | \~2 agents per vCPU (0.5 CPU each) **Rust async tasks use \~1/20 memory per task** |

* **Agents/vCPU**: with 250–500m request, expect \~2 agents per core for bursty CPU workloads.
* **Threads**: configure Tokio runtime threads ≈2×CPUs+1.
* **Memory per agent**: assume \~256Mi; actual Rust async services use minimal memory (e.g. 1/20th of comparable thread-based tasks).
* **Cost vs Latency**: provision some headroom (\~70–80% utilization) to meet p95 latency targets.  Observe burst vs steady load; use HPA to handle bursts while optimizing steady-state cost.
* **Case Study**: Apollo Router (GraphQL in Rust) ran on 8 vCPUs and handled \~12,000 RPS (P95 latency \~1–3ms) – showing linear scaling of a Rust async service under load.

## CI/CD Pipeline (GitOps)

* **GitHub Actions**: example workflow `.github/workflows/ci.yml`:

```yaml
name: CI
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build & Test
        run: cargo test --release
      - name: Build & Push Docker
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          tags: ghcr.io/${{github.repository}}/orchestrator:latest
      - name: Deploy to Cluster
        run: |
          echo "$KUBE_CONFIG" > kubeconfig
          kubectl --kubeconfig=kubeconfig apply -f k8s/
        env:
          KUBE_CONFIG: ${{ secrets.KUBE_CONFIG }}
```

* **GitLab CI** (mirror example `.gitlab-ci.yml`):

```yaml
stages: [build, test, deploy]
build:
  script: cargo build --release
test:
  script: cargo test --release
deploy:
  script:
    - docker build -t registry/myproj/orchestrator:latest .
    - docker push registry/myproj/orchestrator:latest
    - kubectl apply -f k8s/
  only: [main]
```

*(Use GitHub Actions for primary CI/CD; GitLab CI can be a future mirror for redundancy.)*