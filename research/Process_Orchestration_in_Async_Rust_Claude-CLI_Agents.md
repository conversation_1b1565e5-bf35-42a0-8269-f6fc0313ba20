# Process Orchestration in Async Rust (Claude-CLI Agents)

## Process Launch & Supervision Patterns

* **Tokio Command Spawn:** Use `tokio::process::Command` for async process management. It integrates with the Tokio runtime and uses efficient OS spawns (e.g. `posix_spawn` on modern Linux). Example micro-pattern: spawn with piped I/O and enable kill-on-drop for safety:

  ```rust
  use tokio::process::Command;
  use std::process::Stdio;
  let mut child = Command::new("claude-cli")
      .arg("--json-stream")
      .stdout(Stdio::piped())
      .stderr(Stdio::piped())
      .kill_on_drop(true)  // ensure SIGKILL if handle is dropped
      .spawn()?;
  ```

  Tokio will reap zombie processes on a *best-effort* basis, but it’s recommended to `.await` the child exit for prompt cleanup. Each agent runs in its own OS process; on macOS dev and Linux prod, the spawn behavior is consistent (POSIX). Tokio’s approach has minimal overhead per agent (no dedicated thread unless needed), leveraging the async reactor for I/O readiness.

* **Duct Crate:** The `duct` library provides a high-level API for piping processes and merging outputs. Internally, **duct** spawns worker threads to handle I/O and uses a POSIX fast-path for spawning. This makes it as safe as std Command and thread-safe. Duct’s convenience comes at the cost of one or more threads per pipeline (each agent likely spawns at least one thread to read its output). This can increase memory usage (thread stacks) and context-switch overhead if dozens of agents run, but ensures the child’s stdout/stderr are drained without blocking the async runtime. **Use Case:** if you need simple sync code integration or cross-thread process control. **Downside:** Not Tokio-native; mixing with async requires using blocking handles (or `duct::reader()` and wrapping with `tokio::task::spawn_blocking`).

* **Expectrl vs PTY (Interactive CLI):** If Claude-CLI were truly interactive (requiring a TTY for line-buffering or sending signals like Ctrl-C), a PTY approach may be needed. **Expectrl** provides an Expect-like API to automate interactive apps via a pseudo-terminal. It supports async (with `features=["async"]`) and pattern matching on outputs. **pty-rs/portable-pty:** Lower-level crates to open a PTY device pair for the child. These require manual read/write handling but give full control. In general, if the CLI outputs newline-JSON on stdout without requiring terminal emulation, **a PTY is unnecessary** – a regular piped stdout is simpler and avoids extra overhead. Expectrl shines for truly interactive workflows (sending commands and awaiting regex), but adds overhead (internally using regex and possibly polling). For our case (streaming JSON logs), using direct pipes is more efficient and avoids potential PTY quirks (like needing `read()` loops to avoid blocking on EOF on Windows ConPTY). Only use a PTY if the CLI behaves differently on non-TTY stdout (e.g. buffering too much). On Unix, line-buffering can be forced by setting STDOUT to line buffered mode in the child or by using a PTY (which typically causes line-buffering by default).

* **Bastion & Supervision Frameworks:** Bastion is a fault-tolerant actor runtime with built-in supervisors. It can restart crashed “children” automatically (like Erlang’s one-for-one strategy). **However, Bastion uses a heavy model:** each actor runs in a separate thread pool thread with isolated crashes. This isolation is robust but incurs per-agent thread overhead and crossbeam-channel messaging for communication. For spawning external processes, you’d still need to spawn the OS process from within the actor. **Pros:** Turnkey restart policies (permanent vs transient actors, etc.), hierarchical supervision. **Cons:** Additional complexity, and not strictly Tokio-only (Bastion uses its own scheduler under the hood). Given the “Tokio only” constraint (runtime consistency), rolling a custom supervisor loop is preferable. For example: an async task can monitor a child process and if it exits unexpectedly, decide to restart after a delay. This gives fine-grained control over restart timing (e.g. exponential backoff).

* **Supervisor Restart Loop Pattern:** Implement a restart loop per agent using Tokio tasks. Pseudocode:

  ```rust
  async fn run_agent_supervised(cmd: Command) {
      let mut attempt = 0;
      let mut delay = 100; // initial delay ms
      loop {
          attempt += 1;
          let status = cmd.status().await?;
          if status.success() { 
              break; // normal exit
          }
          eprintln!("Agent exited (code {:?}), restarting in {}ms", status.code(), delay);
          tokio::time::sleep(Duration::from_millis(delay)).await;
          delay = (delay * 2).min(5000); // exponential backoff up to 5s
          // (optional jitter: delay = delay * (1.0 + random(0..0.1)))
          continue;
      }
  }
  ```

  This will continuously respawn the agent process if it crashes, with increasing delay to avoid rapid spin loops. A **one-for-one** strategy (each agent handled independently) prevents one agent’s failure from affecting others. The backoff parameters can be tuned; ensure to cap the delay to avoid excessively long wait (and possibly reset backoff after a long stable run). Monitor the number of restarts – if an agent crashes repeatedly (e.g. 5 times in a short span), you might escalate (stop trying or alert) instead of looping infinitely. This manual approach, while more code, avoids introducing an entire actor framework.

## I/O Capture & Streaming Design

* **Stdout/Stderr Handling:** Claude-CLI emits *newline-delimited JSON* on STDOUT (each log entry is a JSON object terminated by `\n`). We treat each newline as a message boundary – **no additional framing is needed**, preserving the JSON as-is. It’s best to capture STDOUT and STDERR separately to avoid corrupting the JSON stream with interleaved error messages. For example:

  ```rust
  let stdout = child.stdout.take().unwrap();
  let stderr = child.stderr.take().unwrap();
  let mut out_lines = FramedRead::new(stdout, LinesCodec::new());
  let mut err_lines = FramedRead::new(stderr, LinesCodec::new());
  loop {
      tokio::select! {
          Some(line) = out_lines.next() => {
              let line = line?; // a JSON log line from stdout
              // publish or send through channel
          },
          Some(line) = err_lines.next() => {
              let line = line?; // an stderr line (non-JSON text)
              // handle error line (e.g., log to separate subject or tag it)
          },
          else => break, // both streams EOF
      }
  }
  ```

  Here we use `tokio_util::codec::LinesCodec` for line demarcation (ensures we get whole lines). **Merging vs Splitting Streams:** Merging stdout & stderr into one stream (e.g. `stderr_to_stdout`) can simplify pipeline if all output were homogeneous, but in this case, stdout is structured JSON and stderr may contain irregular logs or errors. It’s safer to keep them separate and possibly publish to different NATS subjects (e.g. `agent.{id}.stdout` vs `agent.{id}.stderr`) or include a field indicating stream. If needed, you can merge after reading by tagging each line with its source.

* **Stream Message Schema:** To send logs into JetStream, wrap each JSON line with minimal metadata. Example schema (as Rust struct and equivalent JSON):

  ```rust
  struct LogLine<'a> { 
      time: u64,           // timestamp (epoch ms) 
      agent_id: String,    // which agent 
      line: &'a str        // raw JSON text from Claude-CLI 
  }
  ```

  If using NATS, you might publish `LogLine` as a JSON. For example:

  ```json
  {"time": 1698945600000, "agent_id": "agent-7", "line": {"event":"answer","content":"..."}}
  ```

  In practice, to avoid double-serializing the inner JSON, one can send the line as a raw bytes field (or as the NATS message body with timestamp in header). Since the requirement is to keep Claude’s JSON intact, **do not modify the content** – just transmit it. If the consumers of JetStream data know that each message payload is a JSON, an alternative is to publish the JSON text directly and rely on NATS message metadata (timestamp when received, or a separate side-channel for timing). However, including a high-resolution timestamp at ingestion (within the orchestrator, e.g. using `tokio::time::Instant` or OS clock) helps measure end-to-end latency.

* **Back-Pressure & Buffering:** The log pipeline must handle bursts without exceeding memory or blocking the CLI process. We set up a bounded channel between the reading task and the JetStream publishing task. For instance:

  ```rust
  let (tx, rx) = tokio::sync::mpsc::channel(100); // capacity 100 lines per agent
  ```

  The reader task `out_lines` `.send()`s each JSON line into this channel. The consumer task pulls from `rx` and does the actual NATS publishing. **Bounded channel rationale:** If JetStream or network is slow, the channel provides a buffer up to 100 messages. If it fills, `.send().await` will yield (apply back-pressure to the reader task). This back-pressure will ultimately propagate to the child process: once the OS pipe buffer (\~64 KiB) fills up, the CLI will block on write. We must decide how to handle this:

  * *Option 1: Block/Back-pressure (drop-new):* Let the sender await until space is free. This slows down log reading (and the CLI if buffer fills) but guarantees no logs are lost. However, it could violate the sub-5ms propagation if the pipeline is saturated.
  * *Option 2: Drop Newest Messages:* Use a try-send pattern and if the channel is full, skip the new line (log a warning or increment a drop counter). This keeps pipeline latency low at the cost of losing some output under extreme load. Tokio’s MPSC `try_send` returns error if full, and in that case the message is dropped. Example:

    ```rust
    if tx.try_send(log_line).is_err() {
        // channel full - drop this line to relieve pressure
    }
    ```
  * *Option 3: Drop Oldest (Overflow):* Implement a ring buffer or use a limited-capacity queue where pushing a new item evicts the oldest if full. Tokio’s channel doesn’t do this by default. A manual approach is to call `rx.recv()` to make space then send, but that complicates single-producer logic. Alternatively, use `tokio::sync::broadcast` with a buffer – it keeps only a fixed number of most recent messages and older ones are dropped for slow subscribers. But broadcast is more for multi-consumer; here we typically have one consumer. A custom bounded queue (e.g. using `VecDeque`) protected by a Mutex could also implement drop-old behavior.
  * **Recommendation:** *Block/back-pressure by default*, to favor completeness of logs, given that each agent’s output is critical. However, if maintaining generation speed is more important than logging every intermediate step, choose drop-new to ensure the pipeline never stalls (the orchestrator can still send a signal to slow down the agent if needed). Since JetStream is quite fast, properly sized buffers (and perhaps batching publishes) should normally keep up.

* **JetStream Publish:** Use the async NATS client (`async_nats`) to publish messages to JetStream. This is typically a straightforward `.publish().await` call. JetStream publish is a request-reply under the hood (for ack). To reduce latency impact, consider publishing with `expect_ack=false` if possible (fire-and-forget, relying on stream clustering to not lose data) – but if acknowledgment is required, the round-trip will add latency. Locally (same machine), JetStream ack latency is \~**0.5–1 ms** on average (sub-millisecond in best cases). With replication and disk persistence, this can rise a bit (each message ack might be a few ms). It’s important to include this in benchmarks: measure timestamp when the line is read vs when the ack is received to compute end-to-end. If \~5ms is the target, ensure network latency to NATS and ack overhead are within \~3–4ms budget. If not, you might publish asynchronously (don’t await each ack sequentially: instead, send and allow multiple in flight, using the library’s internal backpressure or acks aggregation).

## Graceful Shutdown & Signals

* **Termination Strategy:** When it’s time to stop an agent, prefer a graceful stop over a hard kill. First, attempt to send a friendly signal (e.g. SIGINT or SIGTERM) to allow the Claude-CLI to clean up (if it handles those). Tokio’s `Child::kill()` by default sends SIGKILL on Unix (immediate termination). For a gentler approach, you can obtain the child’s OS PID (`child.id()`) and use `nix::sys::signal::kill(Pid::from_raw(pid), Signal::SIGINT)` for example, or on Windows use `job.kill()` if applicable. After sending SIGTERM/INT, wait for the child to exit with a timeout:

  ```rust
  use tokio::time::timeout;
  let pid = child.id();
  unsafe { libc::kill(pid as i32, libc::SIGTERM) };  // or use nix crate
  if let Ok(exit_status) = timeout(Duration::from_secs(2), child.wait()).await {
      println!("Agent exited: {:?}", exit_status);
  } else {
      child.kill().await?; // force kill if not exiting
  }
  ```

  This sequence tries gentle kill then uses `Child::kill()` (SIGKILL) if the process hangs. **Orphan Reaping:** In case the orchestrator itself is shutting down, ensure all child processes are killed so none become orphan zombies. Setting `kill_on_drop(true)` as shown helps if the process is simply dropped without waiting. But in a controlled shutdown, iterate through children and explicitly terminate them as above. Remember that on Unix, a child that exits but isn’t waited on becomes a zombie until the parent reaps it. Tokio will eventually clean it up, but doing it immediately (awaiting `child.wait()`) is better.

* **Deadlocks / Dead TTYs:** A “dead-TTY” scenario can occur if the CLI is stuck waiting on input or flow control. For example, if Claude-CLI expects more input on stdin and none is given, it may not produce output or terminate. To avoid this, open the child’s stdin and close it if not used (so EOF is sent). For instance:

  ```rust
  child.stdin.take(); // drop stdin handle to send EOF if not writing
  ```

  This ensures the CLI knows no further input is coming. Another case: if using a PTY, the child might be paused via flow control (XOFF) – not common unless the CLI or terminal state does so. If an agent produces *no output for a long time* and seems hung, a supervisor could decide to restart it (consider a watchdog timeout for no logs in X seconds). Additionally, if the output reading task crashes or is slow, the pipe/PTY buffer could fill and block the child – another reason to monitor and apply backpressure or drop strategy as discussed.

* **Zombie Processes:** As noted, dropping a `Child` without waiting can lead to zombies. Always wait on child termination. If using `kill_on_drop`, Tokio will send a kill but still needs to reap. It’s safer to manage explicitly. Also, if the orchestrator process is killed (SIGKILL), children will become orphans (init will adopt them on Linux). To handle abnormal orchestrator termination, consider running agents in a process group or use a supervisor (like `systemd` or a watch-dog shell script) that can kill all child PIDs if the parent dies. This might be beyond the scope, but worth noting for complete failure cleanup.

* **Partial Output & Pipe Closure:** Be prepared for partial messages if a process is killed mid-log. Using line-based framing, if a JSON message is cut off, your `LinesCodec` will yield an error or EOF. You might detect an incomplete JSON and drop it or attempt to recover if possible (likely just drop, since on kill it’s incomplete anyway). When the child exits, ensure to flush any remaining buffered data from the reader. The `FramedRead`/`lines()` stream will yield `None` when EOF, so at that point, all complete lines have been delivered. Any unterminated bytes at EOF can be handled by checking `reader.buffer()` if using a `BufReader`.

## Performance Benchmarks (CPU & Latency)

**Benchmark Setup:** Simulate 10+ agents producing logs at X lines/sec, measure CPU usage and end-to-end latency (timestamp at log write in agent vs log received in JetStream). Below is a comparative summary of spawn strategies and their impact:

| Spawn Strategy                   | Complexity (implementation) | Per-Agent Overhead (CPU & Mem)                                                                                                                                             | Log Latency (μs) (write→NATS)                                                                                                                           | Notes                                                                                                                                                                          |
| -------------------------------- | --------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **Tokio `Command` + Async I/O**  | Low – native in Tokio       | **CPU:** \~ negligible when idle; wakes on data via reactor. **Mem:** \~ tens of KB (OS pipe buffers, task stack).                                                         | **Latency:** *Baseline*, often \~100-500µs for pipe read + task switch. Should stay <1ms local.                                                         | **Recommended:** Efficient and scales well. Ensure to `.await` child or use `kill_on_drop` to avoid zombies.                                                                   |
| **Tokio + PTY (Expectrl/pty)**   | Medium – extra PTY layer    | **CPU:** Slightly higher if line-oriented output wasn’t flushing without TTY. PTY adds cost of pseudo-terminal handling. **Mem:** \~similar to pipes, plus PTY fd.         | **Latency:** \~1-2ms. PTY might force line buffering (reducing wait) but introduces its own minor overhead.                                             | Use only if needed for CLI behavior. Expectrl’s async regex matching can add \~100µs per expect check (avoid if not needed).                                                   |
| **Duct (threads for I/O)**       | Low – very simple API       | **CPU:** \~ Idle threads block on read (low CPU). Waking threads incur context switch per line (\~tens of µs). **Mem:** \~2 MB stack per thread (default), can be lowered. | **Latency:** \~200µs typical; could see tail-latency in ms if OS scheduler is busy (thread wake might be slightly slower than event). Generally <1-2ms. | Scales to dozens of processes, but 50 agents = 50+ threads which can increase memory. Good for sync contexts; in async Tokio prefer native approach to avoid thread explosion. |
| **Bastion (actors)**             | High – uses actor model     | **CPU:** \~ Moderate overhead for message passing and supervision logic. Each agent actor on its own thread/pool. **Mem:** thread stacks + Bastion runtime (\~MBs).        | **Latency:** \~500µs+; messages forwarded through actor mailbox add latency. Possibly >1ms worst-case due to indirection.                               | Overkill for pure log streaming. Only consider if needing dynamic scaling, clustering or complex recovery logic. Tokio tasks are lighter for our use case.                     |
| **Manual Threads (std::thread)** | Medium – manual sync code   | **CPU:** Similar to duct (one thread per stream read). **Mem:** \~2MB stack per thread (unless tuned).                                                                     | **Latency:** \~200-1000µs; (dependent on scheduling).                                                                                                   | Not recommended under Tokio: mixing blocking threads for I/O is fine, but easier to use Tokio’s async. Only use if Tokio runtime is saturated and offloading I/O is needed.    |

*(All numbers are approximate; actual performance depends on hardware and load. JetStream publish ack (\~<1ms local) is included in latency figures.)*

To ensure sub-5ms end-to-end, the **Tokio async approach with direct piped I/O** is optimal. It avoids unnecessary context switches and integrates the JetStream publish in the same runtime. JetStream itself, as noted, typically adds under 1ms latency for the publish ACK on a local network. With dozens of agents, ensure the Tokio runtime has sufficient worker threads (consider using a **multi-threaded Tokio runtime** with threads = num\_cpus or more) so that simultaneous I/O and publish tasks don’t contend on one core. Each log line is small (a few KB at most); at say 1KB/line, 1000 lines/sec = \~1MB/s per agent. Tokio can handle this throughput on pipes easily. NATS JetStream can handle thousands of msgs/sec; to reach that, batch if needed (e.g., use NATS publish without await and flush periodically), but given the <5ms requirement, sending immediately is fine.

## Supervisor Policy Decision Matrix

Finally, decide on the supervision and I/O strategy using the following matrix of criteria:

* **Simplicity vs. Features:** If you need just to run processes and stream output, use Tokio’s built-in process handling and implement minimal restart logic. If you needed complex orchestration (dynamic scaling, inter-agent messaging, stateful actors), then an actor framework (Bastion) might be considered, but here it’s not necessary. **Decision:** *Tokio `process`* for simplicity and reliability.

* **Interactive TTY Needed?:** If Claude-CLI requires a TTY for proper operation (e.g., to disable output buffering or handle interactive prompts), then use Expectrl or a PTY. If not (CLI already provides JSON streaming mode), avoid PTY. **Decision:** *No PTY* unless proven needed – use piped I/O.

* **Backpressure vs. Loss:** If every log must be preserved, choose blocking back-pressure (risking slowdown of the agent if overwhelmed). If maintaining real-time responsiveness is paramount and occasional log loss is acceptable, use drop-on-full. **Decision:** *Back-pressure with bounded channel* by default, monitor if agents slow down; if so, possibly increase buffer or switch to drop-new policy with metrics on drops.

* **Graceful Shutdown:** Always attempt graceful stop to allow the CLI to wrap up. Only force-kill as a last resort. Use kill-on-drop to avoid lingering processes if the supervisor task is aborted unexpectedly. Also ensure to capture termination signals in the orchestrator (Ctrl+C) to forward to children or handle cleanup.

By following these guidelines – using Tokio for process and I/O management, structuring output as newline JSON messages, and carefully controlling back-pressure – you can achieve a robust multi-agent orchestration with end-to-end log latency in the low milliseconds, even under load. This design aligns with the constraints: **macOS/Linux portability**, \~**250MB per agent** (threads and tasks overhead are minimal in this budget), inclusion of **JetStream publish latency** in the pipeline, preservation of **Claude-CLI JSON format**, and a **pure Tokio** async environment for all components.