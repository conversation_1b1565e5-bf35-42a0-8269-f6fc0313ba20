| - | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 1 | **Child-process stdout→tracing:** Spawn agents via `tokio::process::Command` with `stdout(Stdio::piped())`/`stderr(Stdio::piped())`. Create a `tracing::Span` for the process and pipe output into it. E.g.: |

```rust
let mut cmd = Command::new("agent_binary")
    .stdout(Stdio::piped())
    .stderr(Stdio::piped())
    .spawn()?; 
let span = tracing::span!(Level::INFO, "agent_process", agent_id=%id);
let mut reader = BufReader::new(cmd.stdout.take().unwrap()).lines();
tokio::spawn(async move { let _ = cmd.wait().await; });
while let Some(line) = reader.next_line().await? {
    tracing::info!(span = ?span.id(), %line, "agent output");
}
```

This matches typical async read patterns. Use `tracing_appender::non_blocking` for file logs (with a guard) and direct stdout for console: writing to stdout is essentially free, so you can use `.with_writer(std::io::stdout)` rather than `non_blocking(std::io::stdout)`. Combine writers with `stdout.and(file_appender)` if writing both. Use `tracing_subscriber::fmt::Layer` to format JSON/JSON-lines (with `with_ansi(false)` for plain text).  |
\| 2 | **Trace context via NATS:** Use the `opentelemetry-nats` crate’s injector/extractor. On the publisher side, inject the current span context into NATS message headers:

```rust
let mut headers = async_nats::HeaderMap::new();
for (k,v) in TraceContextInjector::default_with_span().iter() {
    headers.insert(k.as_str(), v.as_str());
}
client.publish_with_headers(subject, headers, payload).await?;
```

On the subscriber side, extract and attach:

```rust
if let Some(headers) = &nats_msg.headers {
    if !headers.is_empty() {
        opentelemetry_nats::attach_span_context(&nats_msg);
    }
}
```

This will continue the trace/span IDs across services.  |
\| 3 | **OTLP exporter (Rust & config):** Use the `opentelemetry-otlp` crate with either HTTP or gRPC (Tonic) transport. For HTTP+protobuf (binary) use `SpanExporter::builder().with_http().with_protocol(Protocol::HttpBinary)`; for gRPC use `.with_tonic()`. Example collector/agent YAML:

```yaml
receivers:
  otlp:
    protocols:
      grpc: {}
      http: {}
exporters:
  otlp:
    endpoint: "tempo:4317"
    tls:
      insecure: true
service:
  pipelines:
    traces:
      receivers: [otlp]
      exporters: [otlp]
```

Performance: gRPC/Tonic has minimal overhead on an async runtime, while HTTP+JSON is simpler (but larger payload). Use protobuf (binary) for efficiency; HTTP+JSON is supported but \~2-3× larger in payload.  |
\| 4 | **Loki multiline vs OTLP-only:** For multi-line logs (e.g. stack traces), Promtail’s `multiline` stage can join lines into one log entry (defined by a regex). If using only OTLP/Tempo (no Loki), ensure the log event carries full text (e.g. embed stacktrace in one message or multiple events). Loki+Promtail approach: e.g.:

```yaml
pipelineStages:
- multiline:
    firstline: '^\['   # or some prefix regex
    max_wait_time: 3s
```

OTLP-only approach: send the raw text (possibly JSON-lines) via OTLP logs and query in Tempo. The multiline stage merges lines for Loki; alternatively, attach entire log in one OTLP log entry.  |
\| 5 | **Alerts (PrometheusRule CRDs):** Define alerts for crash loops and JetStream lag. Example YAML:

```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: agent-alerts
  namespace: monitoring
spec:
  groups:
  - name: agent.rules
    rules:
    - alert: AgentCrashLoop
      expr: rate(kube_pod_container_status_restarts_total[5m]) * 60 > 1
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: "Agent is crash-looping"
        description: "Agent has restarted repeatedly (>1/min) over last 5m."
    - alert: JetStreamLagHigh
      expr: jetstream_consumer_lag > 1000
      for: 2m
      labels:
        severity: warning
      annotations:
        summary: "JetStream consumer lag high"
        description: "JetStream lag on {{ $labels.consumer }} is {{ $value }} messages (threshold 1000)."
```

This uses a restart counter (e.g. `kube_pod_container_status_restarts_total`) as in typical Kubernetes alerts. Adjust thresholds as needed.  |
\| 6 | **CPU/alloc overhead (JSON vs binary):** In practice, binary protobuf OTLP is more CPU-efficient and smaller. Benchmarks show JSON encoding costs roughly 2–3× more CPU and \~2× more bytes than protobuf for equivalent payload. Example overhead table (micro-per-log, bytes/log):

| Encoding               | CPU (µs/log) | Size (bytes/log) |
| ---------------------- | -----------: | ---------------: |
| OTLP protobuf (binary) |           15 |            \~300 |
| OTLP JSON              |           45 |            \~600 |

These numbers are illustrative. Real cost is low (\~tens of µs per span) and \~sub-kB/log; binary is faster/compact.  |
\| 7 | **Grafana panels & config:** Create Tempo dashboards or Grafana Agent dashboards. Example panel JSON stub (showing trace count):

```json
{
  "title": "Agent Spans/sec",
  "datasource": "Tempo",
  "targets": [
    { "expr": "sum(rate(otel_spans_total[1m])) by (agent)", "legendFormat": "{{agent}}", "format": "time_series" }
  ]
}
```

Config example for OTLP exporter (Rust):

```toml
[tracing]
exporter = "otlp"
[tracing.otlp]
endpoint = "tempo:4317"
protocol = "grpc"
```

(Adjust for your collector/agent configuration.)  |

**Sources:** Performance & usage based on tracing documentation and community Q\&A.