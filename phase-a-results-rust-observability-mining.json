{"targetSuggestions": ["observability-monitoring-framework.md"], "contentBlock": "# Rust Observability Layer Mining Results\n\n## Trace Layers Pattern\n\n### Child Process Tracing\n```rust\n// Spawn agents with stdout/stderr piping\nlet mut cmd = Command::new(\"agent_binary\")\n    .stdout(Stdio::piped())\n    .stderr(Stdio::piped())\n    .spawn()?;\n\n// Create tracing span for process\nlet span = tracing::span!(Level::INFO, \"agent_process\", agent_id=%id);\n\n// Pipe output into tracing\nlet mut reader = BufReader::new(cmd.stdout.take().unwrap()).lines();\nwhile let Some(line) = reader.next_line().await? {\n    tracing::info!(span = ?span.id(), %line, \"agent output\");\n}\n```\n\n### Trace Context Propagation (NATS)\n```rust\n// Publisher side - inject context\nlet mut headers = async_nats::HeaderMap::new();\nfor (k,v) in TraceContextInjector::default_with_span().iter() {\n    headers.insert(k.as_str(), v.as_str());\n}\nclient.publish_with_headers(subject, headers, payload).await?;\n\n// Subscriber side - extract and attach\nif let Some(headers) = &nats_msg.headers {\n    if !headers.is_empty() {\n        opentelemetry_nats::attach_span_context(&nats_msg);\n    }\n}\n```\n\n## OTLP Configuration\n\n### Rust OTLP Exporter Setup\n```rust\n// Binary protobuf (recommended)\nSpanExporter::builder()\n    .with_http()\n    .with_protocol(Protocol::HttpBinary)\n\n// gRPC with Tonic\nSpanExporter::builder()\n    .with_tonic()\n```\n\n### Collector Configuration\n```yaml\nreceivers:\n  otlp:\n    protocols:\n      grpc: {}\n      http: {}\nexporters:\n  otlp:\n    endpoint: \"tempo:4317\"\n    tls:\n      insecure: true\nservice:\n  pipelines:\n    traces:\n      receivers: [otlp]\n      exporters: [otlp]\n```\n\n### Configuration TOML\n```toml\n[tracing]\nexporter = \"otlp\"\n[tracing.otlp]\nendpoint = \"tempo:4317\"\nprotocol = \"grpc\"\n```\n\n## Prometheus Patterns\n\n### Alert Rules (PrometheusRule CRD)\n```yaml\napiVersion: monitoring.coreos.com/v1\nkind: PrometheusRule\nmetadata:\n  name: agent-alerts\n  namespace: monitoring\nspec:\n  groups:\n  - name: agent.rules\n    rules:\n    - alert: AgentCrashLoop\n      expr: rate(kube_pod_container_status_restarts_total[5m]) * 60 > 1\n      for: 2m\n      labels:\n        severity: critical\n      annotations:\n        summary: \"Agent is crash-looping\"\n        description: \"Agent has restarted repeatedly (>1/min) over last 5m.\"\n    - alert: JetStreamLagHigh\n      expr: jetstream_consumer_lag > 1000\n      for: 2m\n      labels:\n        severity: warning\n      annotations:\n        summary: \"JetStream consumer lag high\"\n        description: \"JetStream lag on {{ $labels.consumer }} is {{ $value }} messages.\"\n```\n\n## Grafana Integration\n```json\n{\n  \"title\": \"Agent Spans/sec\",\n  \"datasource\": \"Tempo\",\n  \"targets\": [\n    {\n      \"expr\": \"sum(rate(otel_spans_total[1m])) by (agent)\",\n      \"legendFormat\": \"{{agent}}\",\n      \"format\": \"time_series\"\n    }\n  ]\n}\n```\n\n## Performance Insights\n\n| Encoding               | CPU (µs/log) | Size (bytes/log) |\n| ---------------------- | -----------: | ---------------: |\n| OTLP protobuf (binary) |           15 |            ~300  |\n| OTLP JSON              |           45 |            ~600  |\n\n**Key Recommendations:**\n- Use binary protobuf for 2-3× better CPU efficiency\n- Implement gRPC/Tonic for minimal async runtime overhead\n- Use `tracing_appender::non_blocking` for file logs\n- Direct stdout writing is essentially free\n\n## Multiline Log Handling\n\n### Loki/Promtail Approach\n```yaml\npipelineStages:\n- multiline:\n    firstline: '^\\['   # prefix regex\n    max_wait_time: 3s\n```\n\n### OTLP-Only Approach\n- Send complete text as single OTLP log entry\n- Use JSON-lines format for structured logging\n- Query directly in Tempo for trace analysis"}