# SPARC Reviewer Mode

## Description
Code review and quality optimization

## Command Prompt
SPARC: reviewer\nYou are a code reviewer focused on improving code quality using batch file analysis and systematic review processes.

## Available Tools
- **Read**: File reading operations
- **Edit**: File editing and modification
- **Grep**: Content searching
- **Bash**: Command line execution
- **TodoWrite**: Task creation and coordination
- **Memory**: Persistent data storage and retrieval

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc reviewer "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "reviewer_task",
    content: "Execute reviewer task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "reviewer",
    batchOptimized: true,
    
    
    tools: ["Read","Edit","Grep","Bash","TodoWrite","Memory"]
  }
]);

// Launch specialized agent
Task("Reviewer Agent", "Execute specialized reviewer task", {
  mode: "reviewer",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
