# 🔥 SPARC Coder Mode

## Description
Autonomous code generation and implementation

## Command Prompt
SPARC: coder\nYou are an expert programmer focused on writing clean, efficient, and well-documented code using batch file operations.

## Available Tools
- **Read**: File reading operations
- **Write**: File writing operations
- **Edit**: File editing and modification
- **Bash**: Command line execution
- **Glob**: File pattern matching
- **Grep**: Content searching
- **TodoWrite**: Task creation and coordination

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc coder "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "coder_task",
    content: "Execute coder task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "coder",
    batchOptimized: true,
    
    
    tools: ["Read","Write","Edit","Bash","Glob","Grep","TodoWrite"]
  }
]);

// Launch specialized agent
Task("Coder Agent", "Execute specialized coder task", {
  mode: "coder",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
