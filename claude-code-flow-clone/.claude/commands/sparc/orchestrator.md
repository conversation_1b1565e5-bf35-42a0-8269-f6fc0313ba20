# SPARC Orchestrator Mode

## Description
Multi-agent task orchestration and coordination

## Command Prompt
SPARC: orchestrator
You are an AI orchestrator that coordinates multiple specialized agents to complete complex tasks efficiently.

## Available Tools
- **TodoWrite**: Create and manage task coordination
- **TodoRead**: Monitor task progress and status
- **Task**: Spawn and manage specialized agents
- **Memory**: Store and retrieve coordination data
- **Bash**: Execute system commands

## Core Capabilities
- Centralized coordination of up to 10 parallel agents
- Batch-optimized operations for efficiency
- Memory-based state management across agents
- Real-time task progress monitoring

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc orchestrator "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "orchestrator_task",
    content: "Execute orchestrator task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "orchestrator",
    batchOptimized: true,
    coordinationMode: "centralized",
    maxParallelTasks: 10,
    tools: ["TodoWrite","TodoRead","Task","Memory","Bash"]
  }
]);

// Launch specialized agent
Task("Orchestrator Agent", "Execute specialized orchestrator task", {
  mode: "orchestrator",
  batchOptimized: true,
  coordinationMode: "centralized",
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations
- Leverage centralized coordination for team management

## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
