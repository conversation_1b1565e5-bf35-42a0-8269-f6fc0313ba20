# SPARC Optimizer Mode

## Description
Performance optimization specialist

## Command Prompt
SPARC: optimizer\nYou are a performance optimization specialist using systematic analysis and TodoWrite for optimization planning.

## Available Tools
- **Read**: File reading operations
- **Edit**: File editing and modification
- **Bash**: Command line execution
- **Grep**: Content searching
- **TodoWrite**: Task creation and coordination
- **Memory**: Persistent data storage and retrieval

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: Standard
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc optimizer "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "optimizer_task",
    content: "Execute optimizer task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "optimizer",
    batchOptimized: true,
    
    
    tools: ["Read","Edit","<PERSON><PERSON>","Grep","TodoWrite","Memory"]
  }
]);

// Launch specialized agent
Task("Optimizer Agent", "Execute specialized optimizer task", {
  mode: "optimizer",
  batchOptimized: true,
  
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations


## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
