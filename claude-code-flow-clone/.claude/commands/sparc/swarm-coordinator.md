# SPARC Swarm coordinator Mode

## Description
Swarm coordination and management

## Command Prompt
SPARC: swarm-coordinator\nYou coordinate swarms of AI agents using TodoWrite for task management, Task for agent launching, and Memory for coordination.

## Available Tools
- **TodoWrite**: Task creation and coordination
- **TodoRead**: Task status and progress reading
- **Task**: Agent spawning and management
- **Memory**: Persistent data storage and retrieval
- **Bash**: Command line execution

## Configuration
- **Batch Optimized**: Yes
- **Coordination Mode**: hierarchical
- **Max Parallel Tasks**: Unlimited

## Usage Examples

### Basic Usage
```bash
./claude-flow sparc swarm-coordinator "Your task description here"
```

### Advanced Usage with Coordination
```javascript
// Use TodoWrite for task coordination
TodoWrite([
  {
    id: "swarm-coordinator_task",
    content: "Execute swarm-coordinator task with batch optimization",
    status: "pending",
    priority: "high",
    mode: "swarm-coordinator",
    batchOptimized: true,
    coordinationMode: "hierarchical",
    
    tools: ["TodoWrite","TodoRead","Task","Memory","Bash"]
  }
]);

// Launch specialized agent
Task("Swarm coordinator Agent", "Execute specialized swarm-coordinator task", {
  mode: "swarm-coordinator",
  batchOptimized: true,
  coordinationMode: "hierarchical",
  memoryIntegration: true
});
```

## Best Practices
- Use batch operations when working with multiple files
- Store intermediate results in Memory for coordination
- Enable parallel execution for independent tasks
- Monitor resource usage during intensive operations
- Leverage hierarchical coordination for team management

## Integration
This mode integrates with:
- Memory system for state persistence
- TodoWrite/TodoRead for task coordination
- Task tool for agent spawning
- Batch file operations for efficiency
- Real-time monitoring and metrics

## Troubleshooting
- Ensure proper tool permissions are set
- Check Memory keys for coordination data
- Monitor resource usage during batch operations
- Validate input parameters and constraints
- Use verbose logging for debugging

---
Generated by Claude-Flow SPARC Integration System
