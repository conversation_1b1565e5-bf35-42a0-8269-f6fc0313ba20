# Testing Swarm Command

## Usage
```bash
claude-flow swarm "Test application" --strategy testing --mode mesh
```

## Description
Comprehensive testing coordination with distributed validation.

## Strategy Features
- Test planning and strategy
- Test case generation
- Parallel test execution
- Results aggregation and reporting

## Best Practices
- Use mesh mode for distributed testing
- Enable parallel execution for test suites
- Set appropriate timeout for comprehensive testing
- Monitor results with --monitor flag
