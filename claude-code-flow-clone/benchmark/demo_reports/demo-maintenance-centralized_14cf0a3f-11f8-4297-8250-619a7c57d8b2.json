{"id": "14cf0a3f-11f8-4297-8250-619a7c57d8b2", "name": "demo-maintenance-centralized", "description": "Demo: Maintenance Strategy - Centralized", "status": "completed", "config": {"name": "demo-maintenance-centralized", "description": "Demo: Maintenance Strategy - Centralized", "strategy": "maintenance", "mode": "centralized", "max_agents": 2, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./demo_reports", "verbose": true}, "tasks": [{"id": "75a15250-fbcd-4e88-b87d-50b7ea71d0ec", "objective": "Update documentation and refactor legacy code components", "description": "Benchmark task: Update documentation and refactor legacy code components", "strategy": "maintenance", "mode": "centralized", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T16:40:50.072274", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "47f0ae2d-5a51-4816-bcf5-191b60bca7a0", "task_id": "75a15250-fbcd-4e88-b87d-50b7ea71d0ec", "agent_id": "maintenance-agent", "status": "success", "output": {"maintenance_results": "Maintenance completed for: Update documentation and refactor legacy code components", "actions_performed": ["cleanup", "updates", "documentation"], "status": "system healthy"}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.140289, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 22.0, "memory_mb": 180, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T16:40:50.212666", "started_at": "2025-06-14T16:40:50.072311", "completed_at": "2025-06-14T16:40:50.212626", "duration": 0.140315}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.140289, "total_execution_time": 0.140289, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T16:40:50.072293", "started_at": "2025-06-14T16:40:50.072296", "completed_at": "2025-06-14T16:40:50.212691", "duration": 0.140395, "error_log": [], "metadata": {}}