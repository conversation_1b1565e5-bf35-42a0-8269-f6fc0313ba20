{"id": "893141e1-1783-4f6b-892a-754a40d9005e", "name": "comparison-research-distributed", "description": "Performance comparison benchmark", "status": "completed", "config": {"name": "comparison-research-distributed", "description": "Performance comparison benchmark", "strategy": "research", "mode": "distributed", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 30, "max_retries": 2, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "5d0dc21f-2b91-48c3-8ec3-3fa0d5b1dc56", "objective": "Analyze market trends for AI startups", "description": "Benchmark task: Analyze market trends for AI startups", "strategy": "research", "mode": "distributed", "parameters": {}, "timeout": 30, "max_retries": 2, "priority": 1, "status": "pending", "created_at": "2025-06-17T16:57:29.442127", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "fc7be565-f57c-47f2-b124-7173b02e6506", "task_id": "5d0dc21f-2b91-48c3-8ec3-3fa0d5b1dc56", "agent_id": "research-agent", "status": "success", "output": {"research_findings": "Research completed for: Analyze market trends for AI startups", "sources": ["academic papers", "documentation", "best practices"], "methodology": "comprehensive analysis"}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.10024, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 15.0, "memory_mb": 128, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-17T16:57:29.542436", "started_at": "2025-06-17T16:57:29.442138", "completed_at": "2025-06-17T16:57:29.542401", "duration": 0.100263}], "metrics": {"total_tasks": 1, "completed_tasks": 0, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.10024, "total_execution_time": 0.10024, "success_rate": 0.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-17T16:57:29.442107", "started_at": "2025-06-17T16:57:29.442111", "completed_at": "2025-06-17T16:57:29.542487", "duration": 0.100376, "error_log": [], "metadata": {}}