{"id": "12f5df02-e130-4d98-b9e4-038b95ac2a10", "name": "benchmark-auto-centralized", "description": "Benchmark: Test auto task", "status": "completed", "config": {"name": "benchmark-auto-centralized", "description": "Benchmark: Test auto task", "strategy": "auto", "mode": "centralized", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": false, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "c7bc0f99-5634-4d69-bd3b-ad0a46ea47f1", "objective": "Test auto task", "description": "Benchmark task: Test auto task", "strategy": "auto", "mode": "centralized", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T16:35:17.399123", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "00906d92-c825-4eb8-9410-7345fe913fe0", "task_id": "c7bc0f99-5634-4d69-bd3b-ad0a46ea47f1", "agent_id": "testing-agent", "status": "success", "output": {"test_results": "Testing completed for: Test auto task", "tests_run": 25, "tests_passed": 24, "coverage": 0.92}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.120286, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 18.0, "memory_mb": 160, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T16:35:17.520625", "started_at": "2025-06-14T16:35:17.400253", "completed_at": "2025-06-14T16:35:17.520572", "duration": 0.120319}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.120286, "total_execution_time": 0.120286, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T16:35:17.399160", "started_at": "2025-06-14T16:35:17.399164", "completed_at": "2025-06-14T16:35:17.520668", "duration": 0.121504, "error_log": [], "metadata": {}}