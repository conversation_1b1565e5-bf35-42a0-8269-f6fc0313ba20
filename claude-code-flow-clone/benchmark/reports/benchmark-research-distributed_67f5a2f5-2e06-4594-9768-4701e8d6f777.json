{"id": "67f5a2f5-2e06-4594-9768-4701e8d6f777", "name": "benchmark-research-distributed", "description": "Benchmark: Test distributed coordination", "status": "completed", "config": {"name": "benchmark-research-distributed", "description": "Benchmark: Test distributed coordination", "strategy": "research", "mode": "distributed", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": false, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "6c0851ec-2d3a-4cc3-a16d-43840c7dcdd0", "objective": "Test distributed coordination", "description": "Benchmark task: Test distributed coordination", "strategy": "research", "mode": "distributed", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T16:39:13.682904", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "6719dc56-d263-4da1-8a6a-27343e0f29ba", "task_id": "6c0851ec-2d3a-4cc3-a16d-43840c7dcdd0", "agent_id": "research-agent", "status": "success", "output": {"research_findings": "Research completed for: Test distributed coordination", "sources": ["academic papers", "documentation", "best practices"], "methodology": "comprehensive analysis"}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.100451, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 15.0, "memory_mb": 128, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T16:39:13.783483", "started_at": "2025-06-14T16:39:13.682946", "completed_at": "2025-06-14T16:39:13.783432", "duration": 0.100486}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.100451, "total_execution_time": 0.100451, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T16:39:13.682932", "started_at": "2025-06-14T16:39:13.682937", "completed_at": "2025-06-14T16:39:13.783519", "duration": 0.100582, "error_log": [], "metadata": {}}