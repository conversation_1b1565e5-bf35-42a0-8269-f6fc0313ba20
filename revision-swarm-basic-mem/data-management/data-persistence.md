---
title: revised-data-persistence
type: note
permalink: revision-swarm/data-management/revised-data-persistence
tags:
- '#revised-document #data-persistence #foundation-focus'
---

# Data Persistence & Memory Management Architecture
## Foundation Patterns Guide

> **Canonical Reference**: See `tech-framework.md` for authoritative technology stack specifications

## Executive Summary

This document defines foundational data persistence and memory management patterns using PostgreSQL 15 with SQLx 0.7 as the primary data layer, complemented by JetStream KV for distributed state management. Focus is on teachable patterns and basic architectural principles.

## 1. Basic Storage Architecture

### 1.1 Storage Pattern Overview

```pseudocode
DEFINE StorageLayer ENUM {
    MEMORY_CACHE,     // In-process cache
    DISTRIBUTED_KV,   // JetStream KV
    RELATIONAL_DB     // PostgreSQL
}

INTERFACE DataStorage {
    FUNCTION store(key: String, value: Object) -> Result
    FUNCTION retrieve(key: String) -> Result<Object>
    FUNCTION remove(key: String) -> Result
}
```

### 1.2 Data Categories

```pseudocode
DEFINE DataType ENUM {
    SESSION_DATA,     // Temporary user sessions
    AGENT_STATE,      // Agent runtime state
    TASK_INFO,        // Task metadata
    MESSAGE_LOG       // Communication history
}

CLASS DataRouter {
    FUNCTION selectStorage(dataType: DataType) -> StorageLayer {
        SWITCH dataType {
            CASE SESSION_DATA:
                RETURN MEMORY_CACHE
            CASE AGENT_STATE:
                RETURN DISTRIBUTED_KV
            CASE TASK_INFO:
                RETURN RELATIONAL_DB
            CASE MESSAGE_LOG:
                RETURN RELATIONAL_DB
        }
    }
}
```

## 2. PostgreSQL Schema Patterns

### 2.1 Basic Schema Design

```pseudocode
-- Core schema organization
CREATE SCHEMA agents;
CREATE SCHEMA tasks;
CREATE SCHEMA messages;

-- Simple agent registry
CREATE TABLE agents.registry (
    agent_id UUID PRIMARY KEY,
    agent_type VARCHAR(50),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Task tracking
CREATE TABLE tasks.queue (
    task_id UUID PRIMARY KEY,
    task_type VARCHAR(50),
    payload JSONB,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 2.2 Basic State Management

```pseudocode
-- Agent state tracking
CREATE TABLE agents.state_log (
    agent_id UUID,
    state_data JSONB,
    recorded_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (agent_id, recorded_at)
);

-- Simple indexing
CREATE INDEX idx_agent_state_time 
    ON agents.state_log(agent_id, recorded_at DESC);
```

## 3. JetStream KV Patterns

### 3.1 Key-Value Store Setup

```pseudocode
CLASS KVStoreManager {
    FUNCTION createBucket(name: String) -> Bucket {
        config = {
            bucket: name,
            ttl: Duration.minutes(30),
            replicas: 1
        }
        RETURN JetStream.KeyValue.Create(config)
    }
}
```

### 3.2 Basic State Operations

```pseudocode
CLASS StateManager {
    PRIVATE kv_bucket: Bucket
    
    FUNCTION saveState(key: String, value: Object) -> Result {
        serialized = JSON.stringify(value)
        RETURN kv_bucket.put(key, serialized)
    }
    
    FUNCTION loadState(key: String) -> Result<Object> {
        entry = kv_bucket.get(key)
        IF entry.exists() THEN
            RETURN JSON.parse(entry.value)
        ELSE
            RETURN NotFound()
        END IF
    }
}
```

## 4. Common Patterns

### 4.1 Repository Pattern

```pseudocode
INTERFACE Repository<T> {
    FUNCTION save(entity: T) -> Result<T>
    FUNCTION find(id: UUID) -> Result<T>
    FUNCTION update(entity: T) -> Result<T>
    FUNCTION delete(id: UUID) -> Result
}

CLASS AgentRepository IMPLEMENTS Repository<Agent> {
    PRIVATE db: DatabaseConnection
    
    FUNCTION save(agent: Agent) -> Result<Agent> {
        query = "INSERT INTO agents.registry 
                 (agent_id, agent_type, status) 
                 VALUES ($1, $2, $3)"
        
        result = db.execute(query, [
            agent.id, 
            agent.type, 
            agent.status
        ])
        
        RETURN result.map(agent)
    }
    
    FUNCTION find(id: UUID) -> Result<Agent> {
        query = "SELECT * FROM agents.registry 
                 WHERE agent_id = $1"
        
        row = db.queryOne(query, [id])
        RETURN row.map(Agent.fromRow)
    }
}
```

### 4.2 Event Storage Pattern

```pseudocode
CLASS EventStore {
    PRIVATE db: DatabaseConnection
    
    FUNCTION appendEvent(event: Event) -> Result {
        query = "INSERT INTO events 
                 (event_id, aggregate_id, event_type, data, created_at)
                 VALUES ($1, $2, $3, $4, $5)"
        
        RETURN db.execute(query, [
            event.id,
            event.aggregateId,
            event.type,
            event.data,
            event.timestamp
        ])
    }
    
    FUNCTION getEvents(aggregateId: UUID) -> List<Event> {
        query = "SELECT * FROM events 
                 WHERE aggregate_id = $1 
                 ORDER BY created_at"
        
        rows = db.query(query, [aggregateId])
        RETURN rows.map(Event.fromRow)
    }
}
```

### 4.3 Basic Caching Pattern

```pseudocode
CLASS CachedRepository {
    PRIVATE repository: Repository
    PRIVATE cache: Map<UUID, CacheEntry>
    
    FUNCTION find(id: UUID) -> Result<Entity> {
        // Check cache first
        IF cache.contains(id) THEN
            entry = cache.get(id)
            IF entry.isValid() THEN
                RETURN Success(entry.value)
            END IF
        END IF
        
        // Load from repository
        result = repository.find(id)
        IF result.isSuccess() THEN
            cache.put(id, CacheEntry(result.value))
        END IF
        
        RETURN result
    }
}
```

## 5. Connection Management

### 5.1 Database Connection Pool

```pseudocode
CLASS ConnectionPool {
    PRIVATE pool: Pool
    
    FUNCTION initialize(config: DatabaseConfig) {
        pool = createPool({
            database_url: config.url,
            max_connections: 10,
            min_connections: 2
        })
    }
    
    FUNCTION getConnection() -> Connection {
        RETURN pool.acquire()
    }
}
```

### 5.2 Transaction Patterns

```pseudocode
CLASS TransactionManager {
    FUNCTION executeInTransaction(operations: List<Operation>) -> Result {
        connection = pool.getConnection()
        transaction = connection.beginTransaction()
        
        TRY {
            FOR operation IN operations {
                operation.execute(transaction)
            }
            transaction.commit()
            RETURN Success()
        } CATCH (error) {
            transaction.rollback()
            RETURN Failure(error)
        }
    }
}
```

## 6. Error Handling Patterns

### 6.1 Basic Error Types

```pseudocode
ENUM DataError {
    NOT_FOUND,
    DUPLICATE_KEY,
    CONNECTION_FAILED,
    SERIALIZATION_ERROR
}

CLASS DataResult<T> {
    value: T?
    error: DataError?
    
    FUNCTION isSuccess() -> Boolean
    FUNCTION getValue() -> T
    FUNCTION getError() -> DataError
}
```

### 6.2 Retry Logic

```pseudocode
CLASS RetryHandler {
    FUNCTION withRetry(operation: Function, maxAttempts: Integer) -> Result {
        attempts = 0
        
        WHILE attempts < maxAttempts {
            result = operation()
            IF result.isSuccess() THEN
                RETURN result
            END IF
            
            attempts += 1
            IF attempts < maxAttempts THEN
                sleep(exponentialBackoff(attempts))
            END IF
        }
        
        RETURN Failure("Max retry attempts reached")
    }
}
```

## 7. Basic Monitoring

### 7.1 Simple Metrics

```pseudocode
CLASS MetricsCollector {
    PRIVATE counters: Map<String, Integer>
    
    FUNCTION incrementCounter(metric: String) {
        counters[metric] = counters.getOrDefault(metric, 0) + 1
    }
    
    FUNCTION recordLatency(operation: String, duration: Duration) {
        // Record operation timing
        log("Operation: " + operation + " took " + duration + "ms")
    }
}
```

### 7.2 Health Checks

```pseudocode
CLASS HealthChecker {
    FUNCTION checkDatabase() -> HealthStatus {
        TRY {
            db.execute("SELECT 1")
            RETURN HealthStatus.HEALTHY
        } CATCH (error) {
            RETURN HealthStatus.UNHEALTHY
        }
    }
    
    FUNCTION checkJetStream() -> HealthStatus {
        TRY {
            jetstream.ping()
            RETURN HealthStatus.HEALTHY
        } CATCH (error) {
            RETURN HealthStatus.UNHEALTHY
        }
    }
}
```

## Summary

This document provides foundational data persistence patterns focusing on:

1. **Basic storage layers** - Simple categorization of data types
2. **PostgreSQL patterns** - Essential schema design and queries
3. **JetStream KV usage** - Distributed state management basics
4. **Common patterns** - Repository, event storage, and caching
5. **Connection management** - Pooling and transactions
6. **Error handling** - Basic retry and error types
7. **Simple monitoring** - Health checks and metrics

These patterns form the foundation for understanding data management in distributed systems without the complexity of enterprise features or premature optimization.