{"targetSuggestions": ["security-framework.md", "transport-layer-specifications.md"], "contentBlock": "# NATS Multi-Tenant Security Patterns\n\n## Account-Based Tenant Isolation\n\n```yaml\n# NATS account isolation pattern\naccount_isolation:\n  principle: \"Each tenant gets isolated NATS account\"\n  benefits:\n    - Complete namespace separation\n    - No subject prefix complexity\n    - Built-in multi-tenancy support\n  implementation: |\n    nsc add account --name tenantA\n    nsc edit account --name tenantA \\\n      --js-mem-storage 512M \\\n      --js-disk-storage 1G \\\n      --js-streams 10 \\\n      --js-consumer 50\n```\n\n## Fine-Grained ACL Configuration\n\n```json\n// Per-user permission model\n{\n  \"users\": [\n    {\n      \"user\": \"admin\",\n      \"permissions\": {\n        \"publish\": [ \">\" ],     // Full access\n        \"subscribe\": [ \">\" ]\n      }\n    },\n    {\n      \"user\": \"tenantA_bot\",\n      \"permissions\": {\n        \"publish\": { \"allow\": [\"tenantA.>\"] },\n        \"subscribe\": { \"allow\": [\"tenantA.>\"] }\n      }\n    }\n  ]\n}\n```\n\n## mTLS Authentication Pattern\n\n```hocon\n# NATS server mTLS configuration\ntls {\n  cert_file: \"./certs/nats-server.crt\"\n  key_file:  \"./certs/nats-server.key\"\n  ca_file:   \"./certs/ca.crt\"\n  verify: true  # Enforce client certificates\n}\n\ncluster {\n  tls {\n    cert_file: \"./certs/cluster.crt\"\n    key_file:  \"./certs/cluster.key\"\n    ca_file:   \"./certs/ca.crt\"\n  }\n}\n```\n\n## Resource Quota Enforcement\n\n```yaml\n# Per-account resource limits\njetstream_limits:\n  per_account:\n    max_memory: 512M\n    max_disk: 1G\n    max_streams: 10\n    max_consumers: 100\n  per_stream:\n    max_bytes: configurable\n    max_msgs: configurable\n    max_age: configurable\n    discard_policy: old_on_full\n  connection_limits:\n    max_connections: 100\n    max_subscriptions: 1000\n    max_payload_size: 1MB\n```\n\n## Secret Management Patterns\n\n```rust\n// Client certificate setup\nuse async_nats::ConnectOptions;\n\nlet nc = ConnectOptions::new()\n    .require_tls(true)\n    .add_root_certificates(\"ca.crt\".into())\n    .add_client_certificate(\"client.crt\".into(), \"client.key\".into())\n    .connect(\"tls://nats.example.com:4222\")\n    .await?;\n```\n\n## Zero-Downtime Key Rotation\n\n```rust\n// State machine for key rotation\nenum KeyRotationState {\n    KeyAActive,\n    StagingNewKey,\n    ReloadingConfig,\n    KeyBActive,\n}\n\n// Signal handler for hot reload\ntokio::signal::unix::signal(SIGHUP)?.recv().await;\n// Atomically swap API keys in memory\n```\n\n## Security Enforcement Matrix\n\n| Control | Implementation |\n|---------|---------------|\n| **Tenant Isolation** | NATS accounts with separate namespaces |\n| **Access Control** | Subject-based ACLs with allow/deny rules |\n| **Transport Security** | mTLS with CA-signed certificates |\n| **Resource Limits** | Per-account quotas for memory/disk/connections |\n| **Secret Storage** | K8s Secrets with optional SealedSecrets/SOPS |\n| **Key Rotation** | SIGHUP-triggered hot reload without downtime |\n\n## Foundation Security Pseudocode\n\n```rust\n// Initialize secure NATS connection\nasync fn init_secure_nats() -> Result<Connection> {\n    // Load certificates from mounted secrets\n    let ca_cert = fs::read(\"/secrets/ca.crt\")?;\n    let client_cert = fs::read(\"/secrets/client.crt\")?;\n    let client_key = fs::read(\"/secrets/client.key\")?;\n    \n    // Configure with tenant-specific ACLs\n    let options = ConnectOptions::new()\n        .require_tls(true)\n        .add_root_certificates(ca_cert)\n        .add_client_certificate(client_cert, client_key)\n        .name(format!(\"tenant_{}_agent\", TENANT_ID))\n        .connect(NATS_URL).await?;\n    \n    Ok(options)\n}\n\n// Apply rate limiting and backpressure\nfn configure_limits(opts: &mut ConnectOptions) {\n    opts.subscription_capacity(1000)  // Bounded buffer\n        .ping_interval(Duration::from_secs(10))\n        .reconnect_buffer_size(8 * 1024 * 1024);  // 8MB\n}\n```\n\n## Critical Security Patterns\n\n1. **Never share accounts between tenants** - Use NATS accounts for true isolation\n2. **Always enforce mTLS** - Both client and cluster connections must verify certificates\n3. **Apply least privilege** - Restrict subjects to minimum required patterns\n4. **Set resource quotas** - Prevent any tenant from exhausting cluster resources\n5. **Rotate secrets regularly** - Use SIGHUP for zero-downtime key rotation\n6. **Monitor wildcard usage** - Detect and prevent unauthorized subject access"}