# Troubleshooting Guide - RUST-SS Optimization Patterns

## Overview
Common issues, root causes, and solutions for optimization pattern problems in production systems.

---

# Circuit Breaker Issues

## Issue: Circuit Breaker Stuck Open

### Symptoms
- All requests failing immediately
- No recovery attempts visible
- Circuit remains open beyond reset timeout

### Root Causes
1. **Health check failures**: Target genuinely unhealthy
2. **Clock issues**: System time drift
3. **Configuration error**: Reset timeout too long
4. **State corruption**: Concurrent modification bug

### Solutions
```rust
// 1. Manual reset with verification
pub async fn force_reset_circuit_breaker(breaker: &CircuitBreaker) {
    // First verify target health
    if breaker.health_check().await.is_ok() {
        breaker.force_close().await;
        log::info!("Circuit breaker manually reset after health verification");
    } else {
        log::error!("Cannot reset - target still unhealthy");
    }
}

// 2. Add automatic recovery validation
impl CircuitBreaker {
    async fn validate_recovery(&self) -> bool {
        let test_results = self.execute_health_probes(3).await;
        test_results.success_rate() > 0.66
    }
}
```

### Prevention
- Implement active health checking
- Add jitter to recovery timing
- Monitor clock synchronization
- Use atomic state transitions

---

## Issue: Circuit Breaker Too Sensitive

### Symptoms
- Frequent false positives
- Opening on transient errors
- Healthy services marked as down

### Root Causes
1. **Low thresholds**: Failure threshold too aggressive
2. **Short time windows**: Not accounting for variance
3. **Error classification**: Treating all errors equally
4. **Network jitter**: Transient network issues

### Solutions
```yaml
# 1. Adjust thresholds based on service type
circuit_breakers:
  batch_processor:
    failure_threshold: 20  # Higher for batch
    sample_window_seconds: 300  # Longer window
    
  real_time_api:
    failure_threshold: 5
    sample_window_seconds: 60
    
# 2. Implement error classification
error_classification:
  critical:  # Open immediately
    - connection_refused
    - authentication_failed
  transient:  # Don't count toward threshold
    - timeout
    - rate_limited
```

### Prevention
- Profile normal error rates
- Classify errors appropriately
- Use percentile-based thresholds
- Implement gradual degradation

---

# Rate Limiting Issues

## Issue: Bursty Traffic Rejection

### Symptoms
- Legitimate burst traffic rejected
- Uneven request distribution
- Client retry storms

### Root Causes
1. **Small burst allowance**: Token bucket too small
2. **Clock granularity**: Coarse time measurements
3. **Synchronization**: All clients retry together
4. **Configuration**: Rate too restrictive

### Solutions
```rust
// 1. Implement adaptive burst sizing
pub struct AdaptiveBurstLimiter {
    base_rate: u64,
    burst_multiplier: f64,
    
    async fn calculate_burst_size(&self) -> u64 {
        let recent_variance = self.measure_traffic_variance().await;
        let burst_size = self.base_rate * self.burst_multiplier * (1.0 + recent_variance);
        burst_size.min(self.base_rate * 10) // Cap at 10x
    }
}

// 2. Add request queueing with priority
pub struct QueuedRateLimiter {
    limiter: TokenBucket,
    priority_queue: PriorityQueue<Request>,
    
    async fn acquire_with_queue(&self, priority: Priority) -> Result<Permit> {
        match self.limiter.try_acquire().await {
            Ok(permit) => Ok(permit),
            Err(_) => {
                self.priority_queue.push(request, priority).await;
                self.wait_for_tokens().await
            }
        }
    }
}
```

### Prevention
- Size burst for expected variance
- Implement token reservation
- Add jitter to client retries
- Use sliding windows for smoothing

---

## Issue: Distributed Rate Limit Drift

### Symptoms
- Inconsistent rate enforcement
- Some nodes over/under limiting
- Global rate exceeded

### Root Causes
1. **Clock skew**: Nodes have different times
2. **Network partitions**: Sync failures
3. **Algorithm choice**: Eventually consistent
4. **Configuration drift**: Nodes have different configs

### Solutions
```rust
// 1. Implement distributed token bucket with gossip
pub struct DistributedRateLimiter {
    local_bucket: TokenBucket,
    cluster_state: ClusterState,
    
    async fn sync_with_peers(&self) {
        let local_rate = self.local_bucket.consumption_rate();
        let global_rate = self.cluster_state.aggregate_rate().await;
        
        if global_rate > self.config.global_limit {
            // Reduce local rate proportionally
            let adjustment = self.config.global_limit / global_rate;
            self.local_bucket.adjust_rate(adjustment).await;
        }
    }
}

// 2. Use hybrid local/global limiting
pub struct HybridRateLimiter {
    local_limit: TokenBucket,    // 80% of fair share
    global_limit: RedisRateLimiter, // 20% from global pool
    
    async fn acquire(&self) -> Result<Permit> {
        // Try local first
        if let Ok(permit) = self.local_limit.try_acquire().await {
            return Ok(permit);
        }
        // Fall back to global
        self.global_limit.acquire().await
    }
}
```

### Prevention
- Use NTP synchronization
- Implement gossip protocols
- Add global coordination layer
- Monitor rate accuracy

---

# Connection Pool Issues

## Issue: Connection Pool Exhaustion

### Symptoms
- Timeout acquiring connections
- Requests queuing indefinitely
- Memory growth from queued requests

### Root Causes
1. **Undersized pool**: Max connections too low
2. **Connection leaks**: Not returning connections
3. **Slow queries**: Connections held too long
4. **No timeout**: Infinite wait for connections

### Solutions
```rust
// 1. Implement connection leak detection
pub struct LeakDetectingPool {
    async fn acquire(&self) -> Result<TrackedConnection> {
        let conn = self.inner_pool.acquire().await?;
        let acquisition_id = Uuid::new_v4();
        
        // Track acquisition
        self.active_acquisitions.insert(acquisition_id, AcquisitionInfo {
            timestamp: Instant::now(),
            stack_trace: backtrace::Backtrace::new(),
        });
        
        Ok(TrackedConnection {
            inner: conn,
            id: acquisition_id,
            pool: self.clone(),
        })
    }
    
    async fn detect_leaks(&self) {
        let now = Instant::now();
        for (id, info) in self.active_acquisitions.iter() {
            if now.duration_since(info.timestamp) > Duration::from_secs(300) {
                log::error!("Potential connection leak: {}\n{:?}", id, info.stack_trace);
            }
        }
    }
}

// 2. Add dynamic pool sizing
pub struct DynamicPool {
    async fn adjust_pool_size(&self) {
        let metrics = self.collect_metrics().await;
        
        if metrics.wait_time_p95 > Duration::from_millis(100) {
            self.increase_pool_size(5).await;
        } else if metrics.idle_ratio > 0.5 {
            self.decrease_pool_size(5).await;
        }
    }
}
```

### Prevention
- Right-size pools using Little's Law
- Implement connection timeouts
- Add leak detection
- Monitor pool metrics

---

## Issue: Stale Connection Errors

### Symptoms
- First query after idle fails
- "Connection reset by peer" errors
- Sporadic connection failures

### Root Causes
1. **Idle timeout**: Server closes idle connections
2. **Network equipment**: Firewall/proxy timeouts
3. **No validation**: Using stale connections
4. **Keep-alive disabled**: TCP timeouts

### Solutions
```yaml
# 1. Configure aggressive validation
connection_pool:
  validation:
    test_on_checkout: true
    test_while_idle: true
    time_between_eviction_runs: 30s
    validation_query: "SELECT 1"
    
  keep_alive:
    enabled: true
    interval: 30s
    probes: 3
    
# 2. Implement pre-warming
startup:
  pre_warm_connections: true
  min_connections: 10
  warm_up_query: "SELECT 1"
```

### Prevention
- Enable connection validation
- Configure TCP keep-alive
- Set appropriate idle timeouts
- Implement connection refresh

---

# Load Balancer Issues

## Issue: Uneven Load Distribution

### Symptoms
- Some targets overloaded
- Others underutilized
- High response time variance

### Root Causes
1. **Algorithm choice**: Round-robin with different capacities
2. **Health check lag**: Slow to detect issues
3. **Connection persistence**: Long-lived connections
4. **Geographic skew**: Request origin patterns

### Solutions
```rust
// 1. Implement adaptive weight adjustment
pub struct AdaptiveLoadBalancer {
    async fn adjust_weights(&self) {
        let metrics = self.collect_target_metrics().await;
        
        for target in self.targets.iter_mut() {
            let latency_ratio = target.avg_latency / metrics.global_avg_latency;
            let cpu_ratio = target.cpu_usage / metrics.avg_cpu_usage;
            
            // Reduce weight for overloaded targets
            if latency_ratio > 1.2 || cpu_ratio > 1.2 {
                target.weight *= 0.9;
            } else if latency_ratio < 0.8 && cpu_ratio < 0.8 {
                target.weight *= 1.1;
            }
        }
    }
}

// 2. Use power of N choices with load feedback
pub struct PowerOfNLoadBalancer {
    n_choices: usize, // e.g., 3
    
    async fn select_target(&self) -> &Target {
        let candidates = self.random_sample(self.n_choices);
        
        candidates.iter()
            .min_by_key(|t| {
                let load_score = t.active_connections * 1000 + 
                               (t.avg_latency_ms as usize) * 10 +
                               (t.cpu_percent as usize);
                load_score
            })
            .unwrap()
    }
}
```

### Prevention
- Choose appropriate algorithm
- Monitor distribution metrics
- Implement feedback loops
- Regular weight calibration

---

# Integration Issues

## Issue: Cascading Pattern Failures

### Symptoms
- One pattern failure triggers others
- System-wide degradation
- Recovery takes very long

### Root Causes
1. **Tight coupling**: Patterns too interdependent
2. **No backpressure**: Failures propagate
3. **Synchronized recovery**: All recover together
4. **Shared resources**: Resource contention

### Solutions
```rust
// 1. Implement circuit breaker hierarchy
pub struct HierarchicalBreakers {
    service_breaker: CircuitBreaker,    // Service level
    endpoint_breakers: HashMap<String, CircuitBreaker>, // Endpoint level
    
    async fn should_allow(&self, endpoint: &str) -> Result<Permit> {
        // Check service level first
        let service_permit = self.service_breaker.should_allow().await?;
        
        // Then endpoint level
        if let Some(endpoint_breaker) = self.endpoint_breakers.get(endpoint) {
            let endpoint_permit = endpoint_breaker.should_allow().await?;
            Ok(CombinedPermit::new(service_permit, endpoint_permit))
        } else {
            Ok(service_permit)
        }
    }
}

// 2. Add pattern isolation
pub struct IsolatedPatterns {
    async fn execute_with_isolation<F, T>(&self, f: F) -> Result<T>
    where F: Future<Output = Result<T>>
    {
        // Isolated execution context
        let isolated_context = IsolationContext {
            cpu_quota: CpuQuota::Percent(10),
            memory_limit: MemoryLimit::Megabytes(100),
            timeout: Duration::from_secs(5),
        };
        
        isolated_context.execute(f).await
    }
}
```

### Prevention
- Design for loose coupling
- Implement backpressure
- Stagger recovery timing
- Isolate resource usage

---

# Performance Issues

## Issue: High Pattern Overhead

### Symptoms
- Increased latency
- High CPU usage
- Memory growth

### Root Causes
1. **Lock contention**: Synchronized state access
2. **Metric collection**: Too frequent/detailed
3. **Memory allocations**: In hot path
4. **Algorithm complexity**: O(n) operations

### Solutions
```rust
// 1. Implement lock-free patterns
pub struct LockFreeCircuitBreaker {
    state: AtomicU64, // Packed state representation
    
    fn should_allow(&self) -> bool {
        let state = self.state.load(Ordering::Acquire);
        let unpacked = State::from_u64(state);
        
        match unpacked.state_type {
            StateType::Closed => true,
            StateType::Open => {
                if unpacked.should_attempt_reset() {
                    self.try_half_open_transition()
                } else {
                    false
                }
            }
            StateType::HalfOpen => unpacked.has_capacity()
        }
    }
}

// 2. Batch metric updates
pub struct BatchedMetrics {
    local_buffer: ThreadLocal<MetricBuffer>,
    
    fn record(&self, metric: Metric) {
        self.local_buffer.with(|buffer| {
            buffer.push(metric);
            
            if buffer.len() >= 1000 {
                self.flush_to_global(buffer);
            }
        });
    }
}
```

### Prevention
- Use lock-free data structures
- Batch operations
- Profile hot paths
- Pre-allocate memory

---

# Debugging Tools and Techniques

## Enable Debug Logging
```yaml
logging:
  optimization_patterns:
    circuit_breaker: debug
    rate_limiter: debug
    connection_pool: trace
    load_balancer: debug
```

## Metrics to Monitor
```prometheus
# Circuit breaker state
circuit_breaker_state{service="api",state="open"} 1

# Rate limiter rejections
rate_limiter_rejected_total{limiter="global"} 12453

# Pool exhaustion
connection_pool_exhausted_total{pool="database"} 23

# Load distribution
load_balancer_requests_total{target="server1"} 495823
```

## Testing Commands
```bash
# Simulate failures
curl -X POST /debug/simulate-failure -d '{"type":"timeout","rate":0.1}'

# Dump pattern state
curl /debug/patterns/state > pattern_state.json

# Enable detailed tracing
curl -X PUT /debug/tracing -d '{"enabled":true,"level":"detailed"}'
```

## Common Resolution Patterns

1. **Start with logs**: Check error patterns
2. **Review metrics**: Look for anomalies
3. **Check configuration**: Verify recent changes
4. **Test in isolation**: Disable patterns individually
5. **Gradual recovery**: Re-enable with lower thresholds
6. **Monitor closely**: Watch for recurrence