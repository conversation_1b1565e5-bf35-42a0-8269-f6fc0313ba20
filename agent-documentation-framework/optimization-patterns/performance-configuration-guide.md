# Performance Configuration Guide

## Overview
Comprehensive configuration reference for all RUST-SS optimization patterns, including circuit breakers, rate limiting, connection pooling, and load balancing.

---

# Circuit Breaker Configuration

## Basic Circuit Breaker Settings

### Agent-to-Agent Communication
```yaml
# config/agent_circuit_breakers.yaml
circuit_breakers:
  # Default settings for all agent communication
  defaults:
    failure_threshold: 5
    failure_rate_threshold: 0.5
    sample_window_seconds: 60
    reset_timeout_seconds: 30
    half_open_max_requests: 3
    half_open_success_threshold: 2
    
  # Specific configurations per agent type
  agent_types:
    data_processor:
      failure_threshold: 10        # More tolerant for batch processing
      reset_timeout_seconds: 60    # Longer recovery time
      latency_threshold_ms: 5000   # 5 second timeout
      
    real_time_analyzer:
      failure_threshold: 3         # Strict for real-time
      reset_timeout_seconds: 10    # Quick recovery attempts
      latency_threshold_ms: 100    # 100ms max latency
      
    coordinator:
      failure_threshold: 20        # Very tolerant
      failure_rate_threshold: 0.8  # Allow higher failure rate
      reset_timeout_seconds: 5     # Rapid recovery
```

### External Service Integration
```yaml
# config/external_services.yaml
external_services:
  database:
    primary:
      breaker:
        failure_threshold: 3
        reset_timeout_seconds: 60
        error_classification:
          critical:
            - "connection_refused"
            - "authentication_failed"
          recoverable:
            - "timeout"
            - "connection_reset"
          transient:
            - "duplicate_key"
            - "constraint_violation"
            
    read_replica:
      breaker:
        failure_threshold: 5
        reset_timeout_seconds: 30
        # Read replicas can tolerate more failures
        failure_rate_threshold: 0.7
        
  cache:
    redis_cluster:
      breaker:
        failure_threshold: 10
        reset_timeout_seconds: 10
        # Cache can fail gracefully
        half_open_max_requests: 10
```

### Environment-Specific Settings
```yaml
# config/env/development.yaml
circuit_breakers:
  # More lenient in development
  global_defaults:
    failure_threshold: 20
    reset_timeout_seconds: 5
    log_level: "debug"
    metrics_enabled: true
    
---
# config/env/staging.yaml
circuit_breakers:
  # Balanced for testing
  global_defaults:
    failure_threshold: 10
    reset_timeout_seconds: 15
    log_level: "info"
    metrics_enabled: true
    
---
# config/env/production.yaml
circuit_breakers:
  # Strict for production
  global_defaults:
    failure_threshold: 5
    reset_timeout_seconds: 30
    log_level: "warn"
    metrics_enabled: true
    distributed_state_sync: true
```

---

# Rate Limiting Configuration

## Token Bucket Rate Limiting
```yaml
# config/rate_limiting.yaml
rate_limiters:
  global:
    algorithm: "token_bucket"
    rate_per_second: 10000
    burst_size: 50000
    refill_interval_ms: 100
    
  per_agent:
    algorithm: "token_bucket"
    rate_per_second: 1000
    burst_size: 5000
    overflow_strategy: "drop_oldest"
    
  per_tenant:
    default:
      rate_per_second: 100
      burst_size: 500
      priority_queues:
        high: { rate_multiplier: 2.0 }
        normal: { rate_multiplier: 1.0 }
        low: { rate_multiplier: 0.5 }
```

## Sliding Window Rate Limiting
```yaml
# config/sliding_window_limiters.yaml
sliding_window_limiters:
  api_endpoints:
    window_size_seconds: 60
    max_requests: 1000
    window_resolution_seconds: 1
    
  agent_coordination:
    window_size_seconds: 10
    max_requests: 100
    sliding_interval_ms: 100
    
  batch_processing:
    window_size_seconds: 300
    max_requests: 10000
    burst_allowance: 2.0
```

## Adaptive Rate Limiting
```yaml
# config/adaptive_rate_limiting.yaml
adaptive_limiters:
  load_based:
    base_rate: 1000
    cpu_threshold: 0.8
    memory_threshold: 0.9
    adjustment_factors:
      low_load: 1.5
      normal_load: 1.0
      high_load: 0.7
      critical_load: 0.3
      
  latency_based:
    base_rate: 1000
    latency_p99_threshold_ms: 100
    adjustment_curve:
      - latency_ms: 50
        rate_multiplier: 1.2
      - latency_ms: 100
        rate_multiplier: 1.0
      - latency_ms: 200
        rate_multiplier: 0.8
      - latency_ms: 500
        rate_multiplier: 0.5
```

---

# Connection Pool Configuration

## Database Connection Pools
```yaml
# config/database_pools.yaml
database_pools:
  postgresql:
    primary:
      min_connections: 10
      max_connections: 100
      connection_timeout_seconds: 5
      idle_timeout_seconds: 600
      max_lifetime_seconds: 3600
      health_check_interval_seconds: 30
      connection_string: "***********************************/db"
      
    read_replicas:
      - endpoint: "************************************/db"
        weight: 1.0
        max_connections: 50
      - endpoint: "************************************/db"
        weight: 1.0
        max_connections: 50
        
  redis:
    cluster:
      nodes:
        - "redis://redis1:6379"
        - "redis://redis2:6379"
        - "redis://redis3:6379"
      min_connections: 5
      max_connections: 50
      connection_timeout_seconds: 2
      command_timeout_seconds: 1
      retry_attempts: 3
```

## HTTP Connection Pools
```yaml
# config/http_pools.yaml
http_pools:
  external_apis:
    default_pool:
      min_connections: 5
      max_connections: 100
      keep_alive_timeout_seconds: 30
      connection_timeout_seconds: 10
      request_timeout_seconds: 30
      http2_enabled: true
      
    high_volume_api:
      min_connections: 20
      max_connections: 200
      keep_alive_timeout_seconds: 60
      connection_timeout_seconds: 5
      request_timeout_seconds: 15
      pipeline_depth: 10
      
  microservices:
    service_mesh:
      connection_pool_per_host: 50
      h2_max_concurrent_streams: 100
      circuit_breaker_integration: true
      load_balancing_policy: "round_robin"
```

## Message Queue Pools
```yaml
# config/message_queue_pools.yaml
message_pools:
  nats:
    cluster_urls:
      - "nats://nats1:4222"
      - "nats://nats2:4222"
      - "nats://nats3:4222"
    connection_pool:
      min_connections: 3
      max_connections: 30
      reconnect_wait_seconds: 2
      max_reconnect_attempts: 10
      ping_interval_seconds: 30
      
  kafka:
    bootstrap_servers:
      - "kafka1:9092"
      - "kafka2:9092"
      - "kafka3:9092"
    producer_pool:
      min_connections: 5
      max_connections: 25
      batch_size: 1000
      linger_ms: 10
      
    consumer_pool:
      min_connections: 3
      max_connections: 15
      fetch_min_bytes: 1024
      fetch_max_wait_ms: 500
```

---

# Load Balancer Configuration

## Round Robin Load Balancing
```yaml
# config/load_balancers.yaml
load_balancers:
  agent_communication:
    algorithm: "round_robin"
    targets:
      - id: "agent1"
        endpoint: "grpc://agent1:8080"
        weight: 1.0
        health_check:
          enabled: true
          interval_seconds: 10
          timeout_seconds: 3
          path: "/health"
          
      - id: "agent2"
        endpoint: "grpc://agent2:8080"
        weight: 1.0
        health_check:
          enabled: true
          interval_seconds: 10
          timeout_seconds: 3
          path: "/health"
```

## Weighted Load Balancing
```yaml
# config/weighted_load_balancer.yaml
weighted_balancers:
  compute_agents:
    algorithm: "weighted_round_robin"
    targets:
      - id: "high_capacity_agent"
        endpoint: "grpc://high-cap:8080"
        weight: 3.0
        capacity: "high"
        
      - id: "medium_capacity_agent"
        endpoint: "grpc://med-cap:8080"
        weight: 2.0
        capacity: "medium"
        
      - id: "low_capacity_agent"
        endpoint: "grpc://low-cap:8080"
        weight: 1.0
        capacity: "low"
```

## Least Connections Load Balancing
```yaml
# config/least_connections_balancer.yaml
least_connections_balancers:
  database_proxies:
    algorithm: "least_connections"
    connection_tracking: true
    targets:
      - id: "db_proxy1"
        endpoint: "tcp://proxy1:5432"
        max_connections: 1000
        
      - id: "db_proxy2"
        endpoint: "tcp://proxy2:5432"
        max_connections: 1000
        
    health_checks:
      interval_seconds: 5
      timeout_seconds: 2
      unhealthy_threshold: 3
      healthy_threshold: 2
```

## Power of Two Load Balancing
```yaml
# config/power_of_two_balancer.yaml
power_of_two_balancers:
  high_throughput_service:
    algorithm: "power_of_two"
    sample_size: 2
    latency_based_selection: true
    targets:
      - id: "service1"
        endpoint: "http://service1:8080"
        region: "us-east-1"
        
      - id: "service2"
        endpoint: "http://service2:8080"
        region: "us-east-1"
        
      - id: "service3"
        endpoint: "http://service3:8080"
        region: "us-west-2"
        geographic_preference: 0.8
```

---

# Integrated Configuration

## Resilience + Resource Optimization
```yaml
# config/integrated_optimization.yaml
integrated_patterns:
  circuit_breaker_rate_limiter:
    coordination:
      # Reduce rate limit when breaker is half-open
      half_open_rate_reduction: 0.5
      # Increase breaker sensitivity when rate limited
      rate_limited_failure_weight: 2.0
      # Emergency throttling on breaker open
      breaker_open_rate_multiplier: 0.1
      
  connection_pool_load_balancer:
    coordination:
      # Consider pool capacity in target selection
      pool_capacity_weight: 0.3
      # Remove targets with no available connections
      exclude_no_capacity_targets: true
      # Prefer targets with more available connections
      capacity_preference_factor: 1.5
      
  comprehensive_resilience:
    # Stack all patterns together
    enable_circuit_breaker: true
    enable_rate_limiting: true
    enable_connection_pooling: true
    enable_load_balancing: true
    
    # Coordination settings
    pattern_coordination:
      failure_escalation:
        # 1. Rate limit violations
        # 2. Connection pool exhaustion  
        # 3. Circuit breaker opens
        # 4. Load balancer removes target
        escalation_chain: ["rate_limit", "pool_exhaustion", "circuit_break", "remove_target"]
        
      recovery_coordination:
        # Coordinate recovery across all patterns
        synchronized_recovery: true
        recovery_delay_seconds: 10
        gradual_traffic_increase: true
```

## Multi-Tenant Configuration
```yaml
# config/multi_tenant.yaml
multi_tenant:
  tenants:
    premium_tenant:
      circuit_breaker:
        failure_threshold: 20
        reset_timeout_seconds: 10
      rate_limiter:
        rate_per_second: 10000
        burst_size: 50000
      connection_pool:
        max_connections: 200
      load_balancer:
        dedicated_targets: true
        
    standard_tenant:
      circuit_breaker:
        failure_threshold: 10
        reset_timeout_seconds: 30
      rate_limiter:
        rate_per_second: 1000
        burst_size: 5000
      connection_pool:
        max_connections: 50
      load_balancer:
        shared_targets: true
        
    basic_tenant:
      circuit_breaker:
        failure_threshold: 5
        reset_timeout_seconds: 60
      rate_limiter:
        rate_per_second: 100
        burst_size: 500
      connection_pool:
        max_connections: 10
      load_balancer:
        shared_targets: true
        low_priority: true
```

---

# Monitoring and Alerting Configuration

## Metrics Collection
```yaml
# config/monitoring.yaml
monitoring:
  circuit_breakers:
    metrics:
      enabled: true
      collection_interval_seconds: 10
      detailed_metrics:
        - state_transitions
        - request_outcomes
        - latency_percentiles
        - error_classifications
        
  rate_limiters:
    metrics:
      enabled: true
      collection_interval_seconds: 5
      detailed_metrics:
        - rate_limit_hits
        - token_consumption
        - queue_lengths
        - rejection_rates
        
  connection_pools:
    metrics:
      enabled: true
      collection_interval_seconds: 15
      detailed_metrics:
        - pool_utilization
        - connection_lifetimes
        - acquisition_times
        - health_check_results
        
  load_balancers:
    metrics:
      enabled: true
      collection_interval_seconds: 5
      detailed_metrics:
        - target_selection_times
        - target_health_status
        - request_distribution
        - failover_events
```

## Alert Configuration
```yaml
# config/alerts.yaml
alerts:
  circuit_breaker_alerts:
    breaker_open:
      condition: "circuit_breaker_state == 'open'"
      severity: "warning"
      channels: ["slack", "email"]
      
    multiple_breakers_open:
      condition: "count(circuit_breaker_state == 'open') > 3"
      severity: "critical"
      channels: ["pagerduty"]
      
  rate_limiter_alerts:
    high_rejection_rate:
      condition: "rate_limit_rejection_rate > 0.1"
      duration_minutes: 5
      severity: "warning"
      
  connection_pool_alerts:
    pool_exhaustion:
      condition: "connection_pool_utilization > 0.95"
      duration_minutes: 2
      severity: "critical"
      
  load_balancer_alerts:
    no_healthy_targets:
      condition: "healthy_target_count == 0"
      severity: "critical"
      immediate: true
```

---

# Performance Tuning Configuration

## High-Performance Settings
```yaml
# config/high_performance.yaml
high_performance:
  # Optimize for maximum throughput
  circuit_breakers:
    state_cache_duration_ms: 10
    batch_metric_updates: true
    lock_free_state_checks: true
    
  rate_limiters:
    local_token_cache_size: 10000
    batch_token_refills: true
    numa_aware_allocation: true
    
  connection_pools:
    preallocate_connections: true
    connection_validation_fast_path: true
    zero_copy_buffers: true
    
  load_balancers:
    target_cache_ttl_ms: 100
    async_health_checks: true
    power_of_two_sampling: true
```

## Low-Latency Settings
```yaml
# config/low_latency.yaml
low_latency:
  # Optimize for minimum latency
  circuit_breakers:
    state_check_timeout_ns: 50
    inline_metric_recording: true
    
  rate_limiters:
    token_check_timeout_ns: 25
    lockless_token_bucket: true
    
  connection_pools:
    connection_acquisition_timeout_ms: 1
    keep_connections_warm: true
    
  load_balancers:
    target_selection_timeout_us: 50
    cached_target_health: true
```
