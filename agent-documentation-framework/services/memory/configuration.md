# Memory Service - Configuration Guide

## Core Configuration Structure

The memory service supports multiple storage backends with flexible caching, indexing, and namespace configurations.

### Main Configuration Schema

```json
{
  "memory": {
    "backend": {
      "type": "hybrid",
      "primary": "sqlite",
      "cache": "redis",
      "archive": "postgresql"
    },
    "storage": {
      "path": "./data/memory",
      "options": {
        "journal_mode": "wal",
        "synchronous": "normal",
        "cache_size": 10000,
        "mmap_size": 268435456,
        "max_connections": 100,
        "pragma_optimize": true,
        "wal_checkpoint_interval": 30000
      }
    },
    "cache": {
      "enabled": true,
      "max_size": 134217728,
      "ttl": 3600000,
      "strategy": "lru"
    },
    "indexing": {
      "enabled": true,
      "vector_search": true,
      "full_text_search": true,
      "rebuild_interval": "24h"
    }
  }
}
```

### Namespace Configuration

```json
{
  "namespaces": {
    "enabled": true,
    "default_namespace": "default",
    "isolation_level": "strict",
    "permissions": {
      "project_alpha": {
        "read": ["alice", "bob", "system"],
        "write": ["alice", "system"],
        "admin": ["alice"]
      },
      "shared": {
        "read": ["*"],
        "write": ["authenticated"],
        "admin": ["system"]
      }
    },
    "quotas": {
      "project_alpha": {
        "max_items": 10000,
        "max_storage": "100MB",
        "max_operations_per_second": 1000
      }
    }
  }
}
```

## Environment-Specific Configurations

### Development Configuration

```json
{
  "memory": {
    "backend": {
      "type": "sqlite",
      "in_memory": true
    },
    "storage": {
      "path": ":memory:",
      "options": {
        "journal_mode": "memory",
        "synchronous": "off"
      }
    },
    "cache": {
      "enabled": false
    },
    "indexing": {
      "enabled": false
    },
    "logging": {
      "level": "debug",
      "enable_query_profiling": true,
      "enable_slow_query_log": true,
      "slow_query_threshold": 100
    }
  }
}
```

### Production Configuration

```json
{
  "memory": {
    "backend": {
      "type": "hybrid",
      "primary": "sqlite",
      "cache": "redis",
      "archive": "postgresql"
    },
    "storage": {
      "path": "/var/lib/memory",
      "options": {
        "journal_mode": "wal",
        "synchronous": "normal",
        "cache_size": 50000,
        "mmap_size": 1073741824,
        "max_connections": 500,
        "pragma_optimize": true
      }
    },
    "cache": {
      "enabled": true,
      "max_size": 1073741824,
      "ttl": 3600000,
      "strategy": "lfu"
    },
    "indexing": {
      "enabled": true,
      "vector_search": true,
      "full_text_search": true,
      "background_indexing": true
    },
    "optimization": {
      "auto_compress": true,
      "compression_threshold": 1024,
      "auto_cleanup": true,
      "cleanup_interval": "1h",
      "cleanup_threshold": "30d"
    }
  }
}
```

## Configuration Validation Rules

### Required Fields
- `memory.backend.type` must be one of: sqlite, redis, postgresql, hybrid
- `memory.storage.path` must be a valid file system path
- `memory.cache.strategy` must be one of: lru, lfu, fifo

### Storage Configuration
- `cache_size` must be between 1000 and 1000000
- `max_connections` must be between 1 and 1000
- `mmap_size` must be a power of 2 between 64MB and 4GB
- `journal_mode` must be one of: delete, truncate, persist, memory, wal, off

### Cache Configuration
- `max_size` must be between 1MB and 10GB
- `ttl` must be between 1 second and 1 day
- `strategy` must be one of: lru, lfu, fifo, random

## Default Values and Required Fields

### Default Values
```rust
impl Default for MemoryConfig {
    fn default() -> Self {
        Self {
            backend: BackendConfig {
                backend_type: BackendType::Sqlite,
                primary: None,
                cache: None,
                archive: None,
            },
            storage: StorageConfig {
                path: "./data/memory".to_string(),
                options: StorageOptions {
                    journal_mode: "wal".to_string(),
                    synchronous: "normal".to_string(),
                    cache_size: 10000,
                    mmap_size: 268435456, // 256MB
                    max_connections: 100,
                    pragma_optimize: true,
                    wal_checkpoint_interval: 30000,
                },
            },
            cache: CacheConfig {
                enabled: true,
                max_size: 134217728, // 128MB
                ttl: 3600000, // 1 hour
                strategy: CacheStrategy::Lru,
            },
            indexing: IndexingConfig {
                enabled: true,
                vector_search: false,
                full_text_search: true,
                rebuild_interval: Duration::from_secs(86400), // 24 hours
            },
        }
    }
}
```

### Required Fields
- `memory` (root object)
- `memory.backend.type`
- `memory.storage.path`

### Optional Fields with Defaults
- `cache`: Enabled with LRU strategy and 128MB size
- `indexing`: Full-text search enabled, vector search disabled
- `namespaces`: Disabled by default
- `optimization`: All optimization features disabled
- `logging`: Info level with basic logging

## Advanced Configuration Options

### Performance Tuning
```json
{
  "performance": {
    "read_ahead_size": 65536,
    "write_buffer_size": 4194304,
    "compression": {
      "enabled": true,
      "algorithm": "zstd",
      "level": 3
    },
    "connection_pooling": {
      "min_connections": 5,
      "max_connections": 100,
      "idle_timeout": "5m",
      "max_lifetime": "1h"
    }
  }
}
```

### Security Configuration
```json
{
  "security": {
    "encryption": {
      "at_rest": true,
      "algorithm": "AES-256-GCM",
      "key_management": "auto"
    },
    "access_control": {
      "authentication_required": true,
      "rbac_enabled": true,
      "audit_logging": true
    },
    "data_protection": {
      "automatic_wiping": true,
      "retention_policies": {
        "default": "90d",
        "sensitive": "30d",
        "audit": "7y"
      }
    }
  }
}
```

### Backup Configuration
```json
{
  "backup": {
    "enabled": true,
    "schedule": "0 2 * * *",
    "retention": "30d",
    "compression": true,
    "encryption": true,
    "destinations": [
      {
        "type": "local",
        "path": "/backups/memory"
      },
      {
        "type": "s3",
        "bucket": "memory-backups",
        "prefix": "production/"
      }
    ]
  }
}
```

### Monitoring Configuration
```json
{
  "monitoring": {
    "metrics": {
      "enabled": true,
      "export_interval": "30s",
      "exporters": ["prometheus"]
    },
    "logging": {
      "level": "info",
      "format": "json",
      "enable_query_profiling": false,
      "enable_slow_query_log": true,
      "slow_query_threshold": 1000,
      "enable_memory_profiling": false
    },
    "alerts": {
      "memory_usage_threshold": 0.8,
      "query_latency_threshold": "5s",
      "error_rate_threshold": 0.01
    }
  }
}
```

### Workflow Automation
```json
{
  "workflows": {
    "cleanup_old_memories": {
      "triggers": ["schedule:daily", "memory_usage:high"],
      "steps": [
        {
          "action": "query_old_items",
          "conditions": ["older_than:30d", "not_accessed:7d"],
          "parameters": {
            "batch_size": 1000
          }
        },
        {
          "action": "archive_items",
          "conditions": ["size_greater_than:1MB"],
          "parameters": {
            "destination": "archive_storage"
          }
        }
      ]
    }
  }
}
```