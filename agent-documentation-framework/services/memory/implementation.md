# Memory Service - Implementation Details

## Technical Architecture

The memory service implements a distributed, multi-layered storage system with CRDT-based conflict resolution and intelligent caching strategies.

### Core Components

```rust
pub struct MemoryService {
    storage_manager: StorageManager,
    cache_manager: CacheManager,
    index_manager: IndexManager,
    transaction_manager: TransactionManager,
    namespace_manager: NamespaceManager,
    event_publisher: EventPublisher,
    config: MemoryConfig,
}

struct StorageManager {
    primary_storage: Box<dyn Storage>,
    cache_storage: Option<Box<dyn CacheStorage>>,
    archive_storage: Option<Box<dyn ArchiveStorage>>,
    replication_manager: ReplicationManager,
}

struct CacheManager {
    l1_cache: LruCache<String, MemoryItem>,
    l2_cache: Option<RedisCache>,
    cache_stats: CacheStatistics,
    eviction_policy: EvictionPolicy,
}
```

## Key Data Structures

### Memory Item Structure

```rust
#[derive(<PERSON>lone, Debug, Serialize, Deserialize)]
struct MemoryItem {
    id: String,
    category: String,
    key: Option<String>,
    content: Value,
    tags: Vec<String>,
    namespace: Option<String>,
    metadata: HashMap<String, Value>,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
    accessed_at: Option<DateTime<Utc>>,
    expires_at: Option<DateTime<Utc>>,
    version: u64,
    checksum: String,
}

impl MemoryItem {
    fn new(category: String, content: Value) -> Self {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();
        let content_bytes = serde_json::to_vec(&content).unwrap();
        let checksum = sha256::digest(&content_bytes);
        
        Self {
            id,
            category,
            key: None,
            content,
            tags: Vec::new(),
            namespace: None,
            metadata: HashMap::new(),
            created_at: now,
            updated_at: now,
            accessed_at: None,
            expires_at: None,
            version: 1,
            checksum,
        }
    }
    
    fn update_content(&mut self, content: Value) -> Result<()> {
        let content_bytes = serde_json::to_vec(&content)?;
        let new_checksum = sha256::digest(&content_bytes);
        
        self.content = content;
        self.checksum = new_checksum;
        self.updated_at = Utc::now();
        self.version += 1;
        
        Ok(())
    }
}
```

### Query Structure

```rust
#[derive(Clone, Debug)]
struct MemoryQuery {
    category: Option<String>,
    categories: Option<Vec<String>>,
    key: Option<String>,
    namespace: Option<String>,
    namespaces: Option<Vec<String>>,
    tags: Option<Vec<String>>,
    tags_any: Option<Vec<String>>,
    full_text: Option<String>,
    key_pattern: Option<Regex>,
    value_search: Option<String>,
    date_range: Option<DateRange>,
    metadata: Option<HashMap<String, Value>>,
    limit: Option<u32>,
    offset: Option<u32>,
    sort_by: Option<SortField>,
    sort_order: Option<SortOrder>,
}

impl MemoryQuery {
    fn matches(&self, item: &MemoryItem) -> bool {
        // Category filter
        if let Some(ref category) = self.category {
            if &item.category != category {
                return false;
            }
        }
        
        // Tags filter (must have all)
        if let Some(ref tags) = self.tags {
            if !tags.iter().all(|tag| item.tags.contains(tag)) {
                return false;
            }
        }
        
        // Tags any filter (must have at least one)
        if let Some(ref tags_any) = self.tags_any {
            if !tags_any.iter().any(|tag| item.tags.contains(tag)) {
                return false;
            }
        }
        
        // Namespace filter
        if let Some(ref namespace) = self.namespace {
            if item.namespace.as_ref() != Some(namespace) {
                return false;
            }
        }
        
        // Date range filter
        if let Some(ref date_range) = self.date_range {
            let target_date = match date_range.field {
                DateField::Created => item.created_at,
                DateField::Updated => item.updated_at,
                DateField::Accessed => item.accessed_at.unwrap_or(item.created_at),
            };
            
            if let Some(start) = date_range.start {
                if target_date < start {
                    return false;
                }
            }
            
            if let Some(end) = date_range.end {
                if target_date > end {
                    return false;
                }
            }
        }
        
        true
    }
}
```

## Core Algorithms

### Storage Operation Implementation

```rust
impl MemoryService {
    async fn store(&mut self, mut item: MemoryItem) -> Result<MemoryItem> {
        // 1. Validate item
        self.validate_item(&item)?;
        
        // 2. Apply namespace
        if item.namespace.is_none() {
            item.namespace = Some(self.namespace_manager.default_namespace().clone());
        }
        
        // 3. Check permissions
        self.namespace_manager.check_write_permission(&item.namespace, &self.current_user)?;
        
        // 4. Generate ID if needed
        if item.id.is_empty() {
            item.id = Uuid::new_v4().to_string();
        }
        
        // 5. Update timestamps
        let now = Utc::now();
        item.created_at = now;
        item.updated_at = now;
        item.version = 1;
        
        // 6. Store in primary storage
        self.storage_manager.store(&item).await?;
        
        // 7. Update cache
        self.cache_manager.put(item.id.clone(), item.clone()).await?;
        
        // 8. Update indexes
        self.index_manager.index_item(&item).await?;
        
        // 9. Replicate if configured
        if self.config.replication.enabled {
            self.storage_manager.replicate(&item).await?;
        }
        
        // 10. Publish event
        self.event_publisher.publish(MemoryEvent::Stored {
            item_id: item.id.clone(),
            namespace: item.namespace.clone(),
            category: item.category.clone(),
        }).await?;
        
        Ok(item)
    }
    
    async fn retrieve(&mut self, id: &str, namespace: Option<&str>) -> Result<Option<MemoryItem>> {
        // 1. Check cache first
        if let Some(item) = self.cache_manager.get(id).await? {
            // Update access time
            self.update_access_time(&item.id).await?;
            return Ok(Some(item));
        }
        
        // 2. Check permissions
        if let Some(ns) = namespace {
            self.namespace_manager.check_read_permission(&Some(ns.to_string()), &self.current_user)?;
        }
        
        // 3. Retrieve from storage
        let item = self.storage_manager.retrieve(id, namespace).await?;
        
        // 4. Update cache if found
        if let Some(ref item) = item {
            self.cache_manager.put(id.to_string(), item.clone()).await?;
            self.update_access_time(&item.id).await?;
        }
        
        Ok(item)
    }
    
    async fn query(&self, query: MemoryQuery) -> Result<Vec<MemoryItem>> {
        // 1. Check namespace permissions
        if let Some(ref namespace) = query.namespace {
            self.namespace_manager.check_read_permission(&Some(namespace.clone()), &self.current_user)?;
        }
        
        // 2. Optimize query using indexes
        let optimized_query = self.index_manager.optimize_query(&query)?;
        
        // 3. Check if query can be satisfied by cache
        if self.cache_manager.can_satisfy_query(&optimized_query) {
            return self.cache_manager.query(&optimized_query).await;
        }
        
        // 4. Execute query against storage
        let results = self.storage_manager.query(&optimized_query).await?;
        
        // 5. Filter results in memory for complex conditions
        let filtered_results: Vec<MemoryItem> = results
            .into_iter()
            .filter(|item| query.matches(item))
            .collect();
        
        // 6. Apply sorting
        let sorted_results = self.apply_sorting(filtered_results, &query)?;
        
        // 7. Apply pagination
        let paginated_results = self.apply_pagination(sorted_results, &query);
        
        Ok(paginated_results)
    }
}
```

### Conflict Resolution Algorithm

```rust
struct ConflictResolver {
    strategy: ConflictResolutionStrategy,
}

impl ConflictResolver {
    async fn resolve_conflict(
        &self,
        local_item: &MemoryItem,
        remote_item: &MemoryItem,
    ) -> Result<MemoryItem> {
        match self.strategy {
            ConflictResolutionStrategy::LastWriteWins => {
                if remote_item.updated_at > local_item.updated_at {
                    Ok(remote_item.clone())
                } else {
                    Ok(local_item.clone())
                }
            }
            ConflictResolutionStrategy::Crdt => {
                self.crdt_merge(local_item, remote_item).await
            }
            ConflictResolutionStrategy::Manual => {
                Err(Error::ConflictRequiresManualResolution {
                    local: local_item.clone(),
                    remote: remote_item.clone(),
                })
            }
        }
    }
    
    async fn crdt_merge(
        &self,
        local_item: &MemoryItem,
        remote_item: &MemoryItem,
    ) -> Result<MemoryItem> {
        let mut merged = local_item.clone();
        
        // Merge metadata using CRDT semantics
        for (key, remote_value) in &remote_item.metadata {
            match local_item.metadata.get(key) {
                Some(local_value) => {
                    // Use vector clock comparison for conflict resolution
                    let local_clock = self.extract_vector_clock(local_value)?;
                    let remote_clock = self.extract_vector_clock(remote_value)?;
                    
                    match local_clock.compare(&remote_clock) {
                        Ordering::Less => {
                            merged.metadata.insert(key.clone(), remote_value.clone());
                        }
                        Ordering::Greater => {
                            // Keep local value
                        }
                        Ordering::Equal => {
                            // Concurrent updates - merge values
                            let merged_value = self.merge_values(local_value, remote_value)?;
                            merged.metadata.insert(key.clone(), merged_value);
                        }
                    }
                }
                None => {
                    // New key from remote
                    merged.metadata.insert(key.clone(), remote_value.clone());
                }
            }
        }
        
        // Update version and timestamp
        merged.version = std::cmp::max(local_item.version, remote_item.version) + 1;
        merged.updated_at = Utc::now();
        
        Ok(merged)
    }
}
```

## Error Handling Patterns

### Transactional Error Handling

```rust
async fn transactional_update(
    &mut self,
    updates: Vec<(String, Value)>,
) -> Result<Vec<MemoryItem>> {
    let tx = self.transaction_manager.begin().await?;
    let mut updated_items = Vec::new();
    
    for (id, new_content) in updates {
        match self.update_with_transaction(&tx, &id, new_content).await {
            Ok(item) => updated_items.push(item),
            Err(e) => {
                // Rollback transaction on any error
                self.transaction_manager.rollback(tx).await?;
                return Err(e);
            }
        }
    }
    
    // Commit if all updates succeeded
    self.transaction_manager.commit(tx).await?;
    Ok(updated_items)
}
```

### Cache Failure Handling

```rust
async fn cache_aware_retrieve(&mut self, id: &str) -> Result<Option<MemoryItem>> {
    // Try cache first, but don't fail if cache is down
    match self.cache_manager.get(id).await {
        Ok(Some(item)) => return Ok(Some(item)),
        Ok(None) => {}, // Cache miss, continue to storage
        Err(e) => {
            warn!("Cache error, falling back to storage: {}", e);
            // Continue to storage
        }
    }
    
    // Retrieve from storage
    let item = self.storage_manager.retrieve(id, None).await?;
    
    // Try to update cache, but don't fail if cache is down
    if let Some(ref item) = item {
        if let Err(e) = self.cache_manager.put(id.to_string(), item.clone()).await {
            warn!("Failed to update cache: {}", e);
        }
    }
    
    Ok(item)
}
```

## Testing Strategies

### Unit Testing

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_memory_item_creation() {
        let item = MemoryItem::new(
            "test".to_string(),
            json!({"key": "value"})
        );
        
        assert!(!item.id.is_empty());
        assert_eq!(item.category, "test");
        assert_eq!(item.version, 1);
        assert!(!item.checksum.is_empty());
    }
    
    #[tokio::test]
    async fn test_memory_storage_and_retrieval() {
        let mut memory_service = create_test_memory_service().await;
        
        let item = MemoryItem::new(
            "test".to_string(),
            json!({"data": "test_value"})
        );
        
        let stored_item = memory_service.store(item.clone()).await.unwrap();
        assert_eq!(stored_item.id, item.id);
        
        let retrieved_item = memory_service
            .retrieve(&item.id, None)
            .await
            .unwrap()
            .unwrap();
        
        assert_eq!(retrieved_item.content, item.content);
    }
    
    #[tokio::test]
    async fn test_conflict_resolution() {
        let resolver = ConflictResolver::new(ConflictResolutionStrategy::Crdt);
        
        let mut local_item = create_test_item();
        local_item.metadata.insert("counter".to_string(), json!(5));
        
        let mut remote_item = create_test_item();
        remote_item.metadata.insert("counter".to_string(), json!(3));
        
        let resolved = resolver.resolve_conflict(&local_item, &remote_item).await.unwrap();
        
        // Should merge to higher value
        assert_eq!(resolved.metadata.get("counter"), Some(&json!(5)));
    }
}
```

### Integration Testing

```rust
#[tokio::test]
async fn test_distributed_memory_consistency() {
    let node1 = start_memory_node("node1", 8001).await;
    let node2 = start_memory_node("node2", 8002).await;
    
    // Connect nodes
    node1.connect_to_peer("localhost:8002").await.unwrap();
    
    // Store item on node1
    let item = MemoryItem::new("test".to_string(), json!({"value": 42}));
    node1.store(item.clone()).await.unwrap();
    
    // Wait for replication
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Retrieve from node2
    let retrieved = node2.retrieve(&item.id, None).await.unwrap().unwrap();
    assert_eq!(retrieved.content, item.content);
}
```

## Key Interfaces

### Core Service Traits

```rust
#[async_trait]
trait MemoryStorage {
    async fn store(&mut self, item: &MemoryItem) -> Result<()>;
    async fn retrieve(&self, id: &str, namespace: Option<&str>) -> Result<Option<MemoryItem>>;
    async fn update(&mut self, id: &str, item: &MemoryItem) -> Result<()>;
    async fn delete(&mut self, id: &str, namespace: Option<&str>) -> Result<bool>;
    async fn query(&self, query: &MemoryQuery) -> Result<Vec<MemoryItem>>;
}

#[async_trait]
trait CacheStorage {
    async fn get(&self, key: &str) -> Result<Option<MemoryItem>>;
    async fn put(&mut self, key: String, item: MemoryItem) -> Result<()>;
    async fn evict(&mut self, key: &str) -> Result<bool>;
    async fn clear(&mut self) -> Result<()>;
    fn stats(&self) -> CacheStatistics;
}

trait IndexManager {
    async fn index_item(&mut self, item: &MemoryItem) -> Result<()>;
    async fn remove_from_index(&mut self, id: &str) -> Result<()>;
    async fn search(&self, query: &str) -> Result<Vec<String>>;
    async fn rebuild_index(&mut self) -> Result<()>;
}
```