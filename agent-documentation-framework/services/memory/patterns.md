# Memory Service - Design Patterns

## Design Patterns Used

### Repository Pattern

Abstracts data access logic and provides a consistent interface:

```rust
trait MemoryRepository {
    async fn store(&mut self, item: MemoryItem) -> Result<MemoryItem>;
    async fn find_by_id(&self, id: &str) -> Result<Option<MemoryItem>>;
    async fn find_by_category(&self, category: &str) -> Result<Vec<MemoryItem>>;
    async fn update(&mut self, item: MemoryItem) -> Result<MemoryItem>;
    async fn delete(&mut self, id: &str) -> Result<bool>;
}

struct SqliteMemoryRepository {
    connection: SqliteConnection,
}

struct RedisMemoryRepository {
    client: RedisClient,
}

// Usage
impl MemoryService {
    fn new(repository: Box<dyn MemoryRepository>) -> Self {
        Self { repository }
    }
    
    async fn store_memory(&mut self, content: Value) -> Result<MemoryItem> {
        let item = MemoryItem::new("general".to_string(), content);
        self.repository.store(item).await
    }
}
```

### Cache-Aside Pattern

```rust
struct CachedMemoryService {
    repository: Box<dyn MemoryRepository>,
    cache: Box<dyn CacheStorage>,
}

impl CachedMemoryService {
    async fn get_memory(&self, id: &str) -> Result<Option<MemoryItem>> {
        // Try cache first
        if let Some(item) = self.cache.get(id).await? {
            return Ok(Some(item));
        }
        
        // Cache miss - fetch from repository
        if let Some(item) = self.repository.find_by_id(id).await? {
            // Update cache
            self.cache.put(id.to_string(), item.clone()).await?;
            Ok(Some(item))
        } else {
            Ok(None)
        }
    }
    
    async fn update_memory(&mut self, id: &str, updates: Value) -> Result<MemoryItem> {
        // Update repository
        let updated_item = self.repository.update_content(id, updates).await?;
        
        // Invalidate cache
        self.cache.evict(id).await?;
        
        Ok(updated_item)
    }
}
```

### Observer Pattern for Events

```rust
trait MemoryEventObserver {
    async fn on_memory_stored(&self, event: MemoryStoredEvent) -> Result<()>;
    async fn on_memory_updated(&self, event: MemoryUpdatedEvent) -> Result<()>;
    async fn on_memory_deleted(&self, event: MemoryDeletedEvent) -> Result<()>;
}

struct MemoryEventPublisher {
    observers: Vec<Box<dyn MemoryEventObserver>>,
}

impl MemoryEventPublisher {
    fn add_observer(&mut self, observer: Box<dyn MemoryEventObserver>) {
        self.observers.push(observer);
    }
    
    async fn notify_stored(&self, item: &MemoryItem) -> Result<()> {
        let event = MemoryStoredEvent {
            item_id: item.id.clone(),
            category: item.category.clone(),
            namespace: item.namespace.clone(),
            timestamp: Utc::now(),
        };
        
        for observer in &self.observers {
            observer.on_memory_stored(event.clone()).await?;
        }
        
        Ok(())
    }
}
```

## Architectural Decisions

### Multi-Layer Storage Strategy

```rust
enum StorageLayer {
    L1Cache,    // In-memory LRU cache
    L2Cache,    // Redis distributed cache
    Primary,    // SQLite local storage
    Archive,    // PostgreSQL long-term storage
}

struct LayeredStorage {
    layers: HashMap<StorageLayer, Box<dyn Storage>>,
}

impl LayeredStorage {
    async fn get(&self, id: &str) -> Result<Option<MemoryItem>> {
        // Try each layer in order
        for layer in [StorageLayer::L1Cache, StorageLayer::L2Cache, StorageLayer::Primary] {
            if let Some(storage) = self.layers.get(&layer) {
                if let Some(item) = storage.get(id).await? {
                    // Populate upper layers
                    self.populate_upper_layers(id, &item, layer).await?;
                    return Ok(Some(item));
                }
            }
        }
        
        Ok(None)
    }
}
```

### CRDT-Based Conflict Resolution

```rust
#[derive(Clone)]
struct CrdtMemoryItem {
    base: MemoryItem,
    vector_clock: VectorClock,
    lww_register: LwwRegister<Value>,
    pn_counter: PnCounter,
}

impl CrdtMemoryItem {
    fn merge(&self, other: &CrdtMemoryItem) -> CrdtMemoryItem {
        let mut merged = self.clone();
        
        // Merge vector clocks
        merged.vector_clock = self.vector_clock.merge(&other.vector_clock);
        
        // Merge LWW register for content
        merged.lww_register = self.lww_register.merge(&other.lww_register);
        
        // Merge PN counter for numeric values
        merged.pn_counter = self.pn_counter.merge(&other.pn_counter);
        
        merged
    }
}
```

### Namespace Isolation Strategy

```rust
struct NamespaceManager {
    namespaces: HashMap<String, NamespaceConfig>,
    access_control: AccessControlManager,
}

impl NamespaceManager {
    fn isolate_query(&self, query: &mut MemoryQuery, user: &User) -> Result<()> {
        // Apply namespace filter based on user permissions
        let accessible_namespaces = self.access_control
            .get_accessible_namespaces(user)?;
        
        match &query.namespace {
            Some(ns) if accessible_namespaces.contains(ns) => {
                // User has access to specified namespace
            }
            Some(ns) => {
                return Err(Error::NamespaceAccessDenied(ns.clone()));
            }
            None => {
                // Limit to accessible namespaces
                query.namespaces = Some(accessible_namespaces);
            }
        }
        
        Ok(())
    }
}
```

## Best Practices

### Memory Item Versioning

```rust
impl MemoryItem {
    fn create_version(&self) -> MemoryVersion {
        MemoryVersion {
            item_id: self.id.clone(),
            version: self.version,
            content_hash: self.checksum.clone(),
            timestamp: self.updated_at,
            operation: VersionOperation::Update,
        }
    }
    
    fn apply_version(&mut self, version: &MemoryVersion) -> Result<()> {
        if version.version <= self.version {
            return Err(Error::InvalidVersionSequence);
        }
        
        self.version = version.version;
        self.updated_at = version.timestamp;
        
        Ok(())
    }
}
```

### Transactional Updates

```rust
struct MemoryTransaction {
    id: Uuid,
    items: HashMap<String, MemoryItem>,
    operations: Vec<TransactionOperation>,
    isolation_level: IsolationLevel,
}

impl MemoryTransaction {
    async fn commit(&mut self, storage: &mut dyn MemoryStorage) -> Result<()> {
        // Validate all operations
        for operation in &self.operations {
            self.validate_operation(operation)?;
        }
        
        // Apply all operations atomically
        for operation in &self.operations {
            match operation {
                TransactionOperation::Store(item) => {
                    storage.store(item).await?;
                }
                TransactionOperation::Update(id, updates) => {
                    storage.update(id, updates).await?;
                }
                TransactionOperation::Delete(id) => {
                    storage.delete(id, None).await?;
                }
            }
        }
        
        Ok(())
    }
}
```

### Plugin Architecture

```rust
trait MemoryPlugin {
    fn name(&self) -> &str;
    async fn before_store(&self, item: &mut MemoryItem) -> Result<()>;
    async fn after_store(&self, item: &MemoryItem) -> Result<()>;
    async fn before_query(&self, query: &mut MemoryQuery) -> Result<()>;
    async fn after_query(&self, results: &mut Vec<MemoryItem>) -> Result<()>;
}

struct SemanticEnhancementPlugin;

impl MemoryPlugin for SemanticEnhancementPlugin {
    fn name(&self) -> &str {
        "semantic_enhancement"
    }
    
    async fn before_store(&self, item: &mut MemoryItem) -> Result<()> {
        // Extract entities and relationships
        let entities = self.extract_entities(&item.content).await?;
        item.metadata.insert("entities".to_string(), json!(entities));
        
        // Add semantic tags
        let semantic_tags = self.generate_semantic_tags(&item.content).await?;
        item.tags.extend(semantic_tags);
        
        Ok(())
    }
}
```

## Integration Patterns with Other Services

### Event-Driven Memory Updates

```rust
struct EventDrivenMemoryService {
    memory_service: MemoryService,
    event_bus: Arc<EventBus>,
}

impl EventDrivenMemoryService {
    async fn setup_event_handlers(&mut self) -> Result<()> {
        // Listen for agent task completions
        self.event_bus.subscribe("agent.task.completed", |event| {
            let memory_service = self.memory_service.clone();
            Box::pin(async move {
                if let Some(result) = event.get("result") {
                    memory_service.store(MemoryItem::new(
                        "task_result".to_string(),
                        result.clone()
                    )).await?;
                }
                Ok(())
            })
        }).await?;
        
        // Listen for coordination decisions
        self.event_bus.subscribe("coordination.decision", |event| {
            let memory_service = self.memory_service.clone();
            Box::pin(async move {
                memory_service.store(MemoryItem::new(
                    "coordination_decision".to_string(),
                    event.data.clone()
                )).await?;
                Ok(())
            })
        }).await?;
        
        Ok(())
    }
}
```

### Distributed Memory Synchronization

```rust
struct DistributedMemoryService {
    local_memory: MemoryService,
    peer_nodes: Vec<PeerNode>,
    conflict_resolver: ConflictResolver,
}

impl DistributedMemoryService {
    async fn synchronize_with_peers(&mut self) -> Result<()> {
        for peer in &self.peer_nodes {
            // Get changes since last sync
            let changes = peer.get_changes_since(self.last_sync_time).await?;
            
            for change in changes {
                match self.local_memory.retrieve(&change.id, None).await? {
                    Some(local_item) => {
                        // Resolve conflict
                        let resolved = self.conflict_resolver
                            .resolve_conflict(&local_item, &change.item).await?;
                        
                        self.local_memory.update(&change.id, resolved).await?;
                    }
                    None => {
                        // New item from peer
                        self.local_memory.store(change.item).await?;
                    }
                }
            }
        }
        
        Ok(())
    }
}
```

### Memory-Based State Management

```rust
struct StatefulMemoryService {
    memory_service: MemoryService,
    state_namespace: String,
}

impl StatefulMemoryService {
    async fn save_agent_state(&mut self, agent_id: &str, state: Value) -> Result<()> {
        let state_item = MemoryItem {
            id: format!("agent_state_{}", agent_id),
            category: "agent_state".to_string(),
            content: state,
            namespace: Some(self.state_namespace.clone()),
            tags: vec!["state".to_string(), agent_id.to_string()],
            ..Default::default()
        };
        
        self.memory_service.store(state_item).await?;
        Ok(())
    }
    
    async fn load_agent_state(&self, agent_id: &str) -> Result<Option<Value>> {
        let state_id = format!("agent_state_{}", agent_id);
        if let Some(item) = self.memory_service.retrieve(&state_id, Some(&self.state_namespace)).await? {
            Ok(Some(item.content))
        } else {
            Ok(None)
        }
    }
}
```

## Usage Examples

### Basic Memory Operations

```rust
// Create memory service
let mut memory = MemoryService::new(config).await?;

// Store a memory
let item = memory.store(MemoryItem::new(
    "research".to_string(),
    json!({
        "topic": "Rust async patterns",
        "findings": ["Use async/await", "Leverage tokio", "Avoid blocking"]
    })
)).await?;

// Retrieve by ID
let retrieved = memory.retrieve(&item.id, None).await?;

// Query by category
let research_items = memory.query(MemoryQuery {
    category: Some("research".to_string()),
    limit: Some(10),
    sort_by: Some(SortField::Updated),
    sort_order: Some(SortOrder::Desc),
    ..Default::default()
}).await?;
```

### Collaborative Memory Sharing

```rust
// Create shared namespace
memory.create_namespace("project_alpha", NamespaceConfig {
    isolation_level: IsolationLevel::Shared,
    retention_policy: RetentionPolicy::Days(90),
    access_control: AccessControl {
        read: vec!["alice".to_string(), "bob".to_string()],
        write: vec!["alice".to_string()],
        admin: vec!["alice".to_string()],
    },
}).await?;

// Store shared memory
let shared_item = MemoryItem {
    category: "shared_knowledge".to_string(),
    content: json!({"insight": "Key architectural decision"}),
    namespace: Some("project_alpha".to_string()),
    tags: vec!["architecture".to_string(), "decision".to_string()],
    ..Default::default()
};

memory.store(shared_item).await?;
```

### Vector-Based Semantic Search

```rust
// Store items with semantic content
for doc in documents {
    memory.store(MemoryItem::new(
        "documentation".to_string(),
        json!({
            "title": doc.title,
            "content": doc.content,
            "embedding": doc.vector_embedding
        })
    )).await?;
}

// Search semantically
let results = memory.vector_search(VectorQuery {
    text: "error handling patterns in Rust".to_string(),
    threshold: 0.8,
    limit: 5,
    categories: Some(vec!["documentation".to_string()]),
}).await?;

for result in results {
    println!("Found: {} (similarity: {:.2})", 
             result.item.content["title"], 
             result.similarity);
}
```