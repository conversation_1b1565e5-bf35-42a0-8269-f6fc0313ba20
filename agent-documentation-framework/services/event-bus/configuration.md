# Event Bus Service - Configuration Guide

## Core Configuration Structure

The event bus service supports flexible configuration for different operational modes, performance requirements, and business needs.

### Main Configuration Schema

```json
{
  "event_bus": {
    "transport": {
      "type": "in-memory",
      "options": {
        "max_connections": 1000,
        "connection_timeout": "30s",
        "keep_alive": true
      }
    },
    "publishing": {
      "default_delivery_mode": "at-least-once",
      "batch_size": 100,
      "batch_timeout": "10ms",
      "compression": "gzip",
      "max_message_size": "1MB"
    },
    "subscription": {
      "consumer_groups_enabled": true,
      "max_consumers_per_group": 10,
      "subscription_timeout": "60s",
      "dead_letter_queue": true,
      "max_retries": 3
    },
    "persistence": {
      "enabled": true,
      "storage_type": "disk",
      "retention_days": 7,
      "compaction_enabled": true,
      "segment_size": "100MB"
    }
  }
}
```

### Event Stream Configuration

```json
{
  "streams": {
    "default_partitions": 4,
    "replication_factor": 2,
    "segment_retention": "7d",
    "compaction_policy": "delete+compact",
    "ordering_guarantee": "per-partition",
    "stream_configs": {
      "domain_events": {
        "partitions": 8,
        "retention": "30d"
      },
      "audit_events": {
        "partitions": 2,
        "retention": "365d",
        "immutable": true
      }
    }
  }
}
```

## Environment-Specific Configurations

### Development Configuration

```json
{
  "event_bus": {
    "transport": {
      "type": "in-memory",
      "options": {
        "debug_mode": true,
        "trace_events": true
      }
    },
    "publishing": {
      "delivery_mode": "best-effort",
      "batch_size": 10,
      "compression": "none"
    },
    "persistence": {
      "enabled": false
    },
    "monitoring": {
      "metrics_enabled": true,
      "trace_sampling": 1.0
    }
  }
}
```

### Production Configuration

```json
{
  "event_bus": {
    "transport": {
      "type": "distributed",
      "options": {
        "cluster_nodes": ["node1:9092", "node2:9092", "node3:9092"],
        "max_connections": 10000,
        "connection_pool_size": 100
      }
    },
    "publishing": {
      "delivery_mode": "exactly-once",
      "batch_size": 1000,
      "batch_timeout": "100ms",
      "compression": "snappy",
      "idempotency_enabled": true
    },
    "subscription": {
      "consumer_isolation": true,
      "auto_commit_interval": "5s",
      "session_timeout": "30s"
    },
    "persistence": {
      "enabled": true,
      "storage_type": "distributed",
      "replication": 3,
      "min_in_sync_replicas": 2
    }
  }
}
```

## Configuration Validation Rules

### Required Fields
- `event_bus.transport.type` must be one of: in-memory, local, distributed
- `event_bus.publishing.delivery_mode` must be one of: best-effort, at-least-once, exactly-once
- `event_bus.persistence.storage_type` must be one of: memory, disk, distributed

### Publishing Configuration
- `batch_size` must be between 1 and 10000
- `batch_timeout` must be between 1ms and 10s
- `max_message_size` must be between 1KB and 10MB
- `compression` must be one of: none, gzip, snappy, lz4

### Subscription Configuration
- `max_consumers_per_group` must be between 1 and 100
- `max_retries` must be between 0 and 10
- `subscription_timeout` must be between 10s and 5m

## Default Values and Required Fields

### Default Values
```rust
impl Default for EventBusConfig {
    fn default() -> Self {
        Self {
            transport: TransportConfig::in_memory(),
            publishing: PublishingConfig {
                delivery_mode: DeliveryMode::AtLeastOnce,
                batch_size: 100,
                batch_timeout: Duration::from_millis(10),
                compression: CompressionType::None,
                max_message_size: 1_048_576, // 1MB
            },
            subscription: SubscriptionConfig {
                consumer_groups_enabled: true,
                max_consumers_per_group: 10,
                subscription_timeout: Duration::from_secs(60),
                dead_letter_queue: true,
                max_retries: 3,
            },
            persistence: PersistenceConfig {
                enabled: false,
                storage_type: StorageType::Memory,
                retention_days: 7,
                compaction_enabled: false,
                segment_size: 104_857_600, // 100MB
            },
        }
    }
}
```

### Required Fields
- `event_bus` (root object)
- `event_bus.transport`
- `event_bus.publishing`

### Optional Fields with Defaults
- `subscription`: Uses default subscription config
- `persistence`: Disabled by default
- `monitoring`: Basic monitoring enabled
- `security`: Default security settings

## Advanced Configuration Options

### Performance Tuning
```json
{
  "performance": {
    "thread_pool_size": 16,
    "io_threads": 8,
    "network_threads": 4,
    "max_in_flight_requests": 1000,
    "buffer_memory": "128MB",
    "send_buffer_size": "128KB",
    "receive_buffer_size": "128KB"
  }
}
```

### Security Configuration
```json
{
  "security": {
    "authentication": {
      "type": "mutual-tls",
      "cert_path": "/certs/server.crt",
      "key_path": "/certs/server.key",
      "ca_path": "/certs/ca.crt"
    },
    "authorization": {
      "enabled": true,
      "acl_file": "/config/event-bus-acl.json"
    },
    "encryption": {
      "at_rest": true,
      "in_transit": true,
      "algorithm": "AES-256-GCM"
    }
  }
}
```

### Monitoring Configuration
```json
{
  "monitoring": {
    "metrics": {
      "enabled": true,
      "export_interval": "15s",
      "exporters": ["prometheus", "datadog"]
    },
    "tracing": {
      "enabled": true,
      "sampling_rate": 0.1,
      "exporter": "jaeger",
      "endpoint": "http://jaeger:14268/api/traces"
    },
    "logging": {
      "level": "info",
      "format": "json",
      "output": "stdout"
    }
  }
}
```

### Quality of Service Configuration
```json
{
  "qos": {
    "priorities": {
      "critical": {
        "queue_size": 10000,
        "timeout": "1s",
        "retry_policy": "exponential"
      },
      "high": {
        "queue_size": 5000,
        "timeout": "5s",
        "retry_policy": "linear"
      },
      "normal": {
        "queue_size": 1000,
        "timeout": "30s",
        "retry_policy": "fixed"
      }
    },
    "rate_limiting": {
      "enabled": true,
      "global_limit": 100000,
      "per_service_limits": {
        "api-gateway": 50000,
        "workflow-engine": 20000
      }
    }
  }
}
```