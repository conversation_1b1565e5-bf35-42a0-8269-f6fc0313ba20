# Event Bus Service - Design Patterns

## Design Patterns Used

### Publish-Subscribe Pattern

The core pattern enabling decoupled service communication:

```rust
trait EventPublisher {
    async fn publish(&self, topic: &str, event: Event) -> Result<()>;
}

trait EventSubscriber {
    async fn subscribe(&self, pattern: &str, handler: EventHandler) -> Result<SubscriptionId>;
}

// Decoupled communication
impl ServiceA {
    async fn process_order(&self, order: Order) -> Result<()> {
        // Process order
        self.event_bus.publish("orders.created", OrderCreatedEvent {
            order_id: order.id,
            customer_id: order.customer_id,
            total: order.total,
        }).await?;
        Ok(())
    }
}

impl ServiceB {
    async fn setup_subscriptions(&self) -> Result<()> {
        self.event_bus.subscribe("orders.*", |event| {
            // React to order events
        }).await?;
        Ok(())
    }
}
```

### Observer Pattern with Event Streams

```rust
struct EventStream {
    observers: Vec<Box<dyn EventObserver>>,
    filters: Vec<Box<dyn EventFilter>>,
}

#[async_trait]
trait EventObserver {
    async fn on_event(&self, event: &Event) -> Result<()>;
}

impl EventStream {
    async fn notify(&self, event: Event) -> Result<()> {
        // Apply filters
        if !self.filters.iter().all(|f| f.matches(&event)) {
            return Ok(());
        }
        
        // Notify all observers
        for observer in &self.observers {
            observer.on_event(&event).await?;
        }
        
        Ok(())
    }
}
```

### Strategy Pattern for Routing

```rust
trait RoutingStrategy {
    fn route(&self, event: &Event, subscribers: &[Subscriber]) -> Vec<RouteTarget>;
}

struct TopicBasedRouting;
struct ContentBasedRouting;
struct PriorityBasedRouting;

impl EventRouter {
    fn with_strategy(strategy: Box<dyn RoutingStrategy>) -> Self {
        Self { strategy }
    }
    
    fn route(&self, event: &Event) -> Vec<RouteTarget> {
        self.strategy.route(event, &self.subscribers)
    }
}
```

## Architectural Decisions

### Event-Driven Architecture Foundation

```rust
// All service interactions through events
enum SystemEvent {
    // Domain events
    Domain(DomainEvent),
    // Integration events
    Integration(IntegrationEvent),
    // System events
    System(SystemEvent),
    // Command events
    Command(CommandEvent),
}

// Services react to events, not direct calls
impl Service {
    async fn handle_event(&self, event: SystemEvent) -> Result<()> {
        match event {
            SystemEvent::Domain(e) => self.handle_domain_event(e).await,
            SystemEvent::Integration(e) => self.handle_integration_event(e).await,
            SystemEvent::System(e) => self.handle_system_event(e).await,
            SystemEvent::Command(e) => self.handle_command_event(e).await,
        }
    }
}
```

### Semantic Event Design

```rust
// Events carry business meaning
struct OrderShippedEvent {
    order_id: OrderId,
    customer_id: CustomerId,
    tracking_number: String,
    carrier: Carrier,
    estimated_delivery: DateTime<Utc>,
    // Business context
    shipped_from_warehouse: WarehouseId,
    shipping_method: ShippingMethod,
}

// Not just data, but business semantics
impl OrderShippedEvent {
    fn requires_customer_notification(&self) -> bool {
        self.shipping_method == ShippingMethod::Express
    }
    
    fn affects_inventory(&self) -> bool {
        true
    }
}
```

### Adaptive Behavior Patterns

#### Load-Adaptive Processing

```rust
struct AdaptiveEventProcessor {
    load_monitor: LoadMonitor,
    processing_modes: HashMap<LoadLevel, ProcessingMode>,
}

impl AdaptiveEventProcessor {
    async fn process(&mut self, event: Event) -> Result<()> {
        let load_level = self.load_monitor.current_load();
        let mode = self.processing_modes.get(&load_level)
            .unwrap_or(&ProcessingMode::Normal);
        
        match mode {
            ProcessingMode::Normal => self.process_normal(event).await,
            ProcessingMode::HighThroughput => self.process_batch(event).await,
            ProcessingMode::Degraded => self.process_essential_only(event).await,
            ProcessingMode::Recovery => self.process_with_recovery(event).await,
        }
    }
}
```

#### Context-Aware Routing

```rust
struct ContextAwareRouter {
    business_context: BusinessContext,
    routing_rules: RoutingRules,
}

impl ContextAwareRouter {
    fn route(&self, event: &Event) -> Vec<RouteTarget> {
        let mut targets = Vec::new();
        
        // Business hour routing
        if self.business_context.is_business_hours() {
            targets.extend(self.routing_rules.business_hour_targets(event));
        } else {
            targets.extend(self.routing_rules.after_hours_targets(event));
        }
        
        // Priority routing
        if event.is_critical() {
            targets.retain(|t| t.supports_priority());
        }
        
        // Geographic routing
        if let Some(region) = event.source_region() {
            targets.retain(|t| t.serves_region(region));
        }
        
        targets
    }
}
```

## Best Practices

### Event Schema Evolution

```rust
// Version events for backward compatibility
#[derive(Serialize, Deserialize)]
struct EventEnvelope {
    version: u32,
    event_type: String,
    payload: Value,
}

impl EventEnvelope {
    fn deserialize_as<T: EventV1 + EventV2>(&self) -> Result<T> {
        match self.version {
            1 => T::from_v1(serde_json::from_value(self.payload.clone())?),
            2 => T::from_v2(serde_json::from_value(self.payload.clone())?),
            _ => Err(Error::UnsupportedVersion(self.version)),
        }
    }
}
```

### Idempotent Event Processing

```rust
struct IdempotentProcessor {
    processed_events: LruCache<EventId, ProcessingResult>,
}

impl IdempotentProcessor {
    async fn process(&mut self, event: Event) -> Result<ProcessingResult> {
        // Check if already processed
        if let Some(result) = self.processed_events.get(&event.id) {
            return Ok(result.clone());
        }
        
        // Process event
        let result = self.process_once(event.clone()).await?;
        
        // Cache result
        self.processed_events.put(event.id, result.clone());
        
        Ok(result)
    }
}
```

### Event Sourcing Support

```rust
struct EventSourcedAggregate {
    id: AggregateId,
    version: u64,
    events: Vec<DomainEvent>,
}

impl EventSourcedAggregate {
    fn apply_event(&mut self, event: DomainEvent) -> Result<()> {
        match event {
            DomainEvent::Created(e) => self.handle_created(e),
            DomainEvent::Updated(e) => self.handle_updated(e),
            DomainEvent::Deleted(e) => self.handle_deleted(e),
        }?;
        
        self.events.push(event);
        self.version += 1;
        Ok(())
    }
    
    fn rebuild_from_events(events: Vec<DomainEvent>) -> Result<Self> {
        let mut aggregate = Self::default();
        for event in events {
            aggregate.apply_event(event)?;
        }
        Ok(aggregate)
    }
}
```

## Integration Patterns with Other Services

### Saga Pattern Implementation

```rust
struct SagaCoordinator {
    event_bus: Arc<EventBus>,
    saga_state: HashMap<SagaId, SagaState>,
}

impl SagaCoordinator {
    async fn handle_saga_event(&mut self, event: Event) -> Result<()> {
        let saga_id = event.correlation_id
            .ok_or(Error::MissingSagaId)?;
        
        let state = self.saga_state.entry(saga_id)
            .or_insert(SagaState::new());
        
        match state.handle_event(&event) {
            SagaAction::Continue(next_events) => {
                for next_event in next_events {
                    self.event_bus.publish(next_event).await?;
                }
            }
            SagaAction::Compensate(compensation_events) => {
                for comp_event in compensation_events {
                    self.event_bus.publish(comp_event).await?;
                }
            }
            SagaAction::Complete => {
                self.saga_state.remove(&saga_id);
            }
        }
        
        Ok(())
    }
}
```

### CQRS Pattern Support

```rust
// Command side publishes events
impl CommandHandler {
    async fn handle_command(&self, command: Command) -> Result<()> {
        // Validate command
        self.validate(&command)?;
        
        // Execute business logic
        let events = self.execute(command)?;
        
        // Publish events
        for event in events {
            self.event_bus.publish("commands", event).await?;
        }
        
        Ok(())
    }
}

// Query side updates from events
impl ProjectionUpdater {
    async fn handle_event(&mut self, event: Event) -> Result<()> {
        match event {
            Event::EntityCreated(e) => self.create_projection(e),
            Event::EntityUpdated(e) => self.update_projection(e),
            Event::EntityDeleted(e) => self.delete_projection(e),
        }
    }
}
```

### External System Integration

```rust
struct ExternalSystemBridge {
    event_bus: Arc<EventBus>,
    external_client: ExternalSystemClient,
    transformer: EventTransformer,
}

impl ExternalSystemBridge {
    async fn bridge_incoming(&self) -> Result<()> {
        // Poll external system
        let external_events = self.external_client.poll_events().await?;
        
        // Transform to internal events
        for ext_event in external_events {
            let internal_event = self.transformer.to_internal(ext_event)?;
            self.event_bus.publish("external", internal_event).await?;
        }
        
        Ok(())
    }
    
    async fn bridge_outgoing(&self, event: Event) -> Result<()> {
        // Transform to external format
        let external_event = self.transformer.to_external(event)?;
        
        // Send to external system
        self.external_client.send_event(external_event).await?;
        
        Ok(())
    }
}
```

## Usage Examples

### Basic Event Publishing

```rust
// Simple event publishing
let event_bus = EventBus::new(config);

event_bus.publish("orders", OrderCreatedEvent {
    order_id: Uuid::new_v4(),
    customer_id: customer.id,
    items: vec![item1, item2],
    total: calculate_total(&items),
}).await?;
```

### Consumer Group Subscription

```rust
// Join consumer group for load balancing
event_bus.subscribe_with_group(
    "order-processor-group",
    "orders.*",
    |event| async {
        match event {
            Event::OrderCreated(e) => process_new_order(e).await,
            Event::OrderUpdated(e) => update_order(e).await,
            Event::OrderCancelled(e) => cancel_order(e).await,
            _ => Ok(()),
        }
    }
).await?;
```

### Stream Processing

```rust
// Create event stream for analytics
let stream = event_bus.create_stream(StreamConfig {
    name: "user-activity",
    partitions: 10,
    retention: Duration::days(30),
    compaction: CompactionPolicy::Delete,
}).await?;

// Process stream with windowing
stream.process_windowed(
    Duration::minutes(5),
    |window| async {
        let metrics = calculate_metrics(&window.events);
        publish_metrics(metrics).await
    }
).await?;
```