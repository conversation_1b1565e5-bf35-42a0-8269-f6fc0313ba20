# Session Manager Service - Data Flow and Structures

## Data Flow Architecture

### Session Lifecycle Flow
```mermaid
graph TB
    A[Create Session] --> B[Initialize Context]
    B --> C[Attach Terminal]
    C --> D[Execute Commands]
    D --> E[Update State]
    E --> F[Persist Changes]
    F --> D
    G[Session Timeout] --> H[Cleanup]
    E --> G
```

## Core Data Structures

### Session Data Model
```typescript
interface SessionData {
  id: string;
  userId: string;
  createdAt: Date;
  lastActivity: Date;
  workingDirectory: string;
  environment: Record<string, string>;
  history: CommandHistoryEntry[];
  state: SessionState;
  terminals: TerminalReference[];
  collaborators: CollaboratorInfo[];
  security: SecuritySettings;
}

interface CommandHistoryEntry {
  command: string;
  timestamp: Date;
  duration: number;
  exitCode: number;
  output?: string;
}

interface TerminalReference {
  id: string;
  type: 'native' | 'ssh' | 'container';
  shell: string;
  status: 'active' | 'idle' | 'closed';
  recording?: RecordingInfo;
}
```

### REPL Context Model
```typescript
interface REPLContext {
  variables: Record<string, any>;
  functions: Record<string, Function>;
  imports: ImportMap;
  workingDirectory: string;
  environment: Record<string, string>;
  completionCache: CompletionCache;
}

interface CompletionCache {
  entries: Map<string, CacheEntry>;
  maxSize: number;
  ttl: number;
}
```

### Collaboration Data Flow
```typescript
interface CollaborationFlow {
  shareRequest: {
    sessionId: string;
    ownerId: string;
    permissions: Permissions;
    duration: number;
  };
  
  tokenGeneration: {
    shareToken: string;
    expiresAt: Date;
    maxUses: number;
  };
  
  joinProcess: {
    tokenValidation: boolean;
    userAuthentication: boolean;
    permissionCheck: boolean;
    sessionAccess: SessionAccess;
  };
  
  realtimeSync: {
    cursorUpdates: CursorPosition[];
    commandBroadcasts: CommandEvent[];
    stateChanges: StateChangeEvent[];
  };
}
```

This data flow documentation provides the essential structures for session management, terminal integration, and collaborative features in the RUST-SS system.