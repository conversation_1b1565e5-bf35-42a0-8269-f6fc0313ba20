# Session Manager Service - Configuration Guide

## Core Configuration Structure

```json
{
  "sessionManager": {
    "persistence": {
      "provider": "redis",
      "redis": {
        "host": "redis-cluster.internal",
        "port": 6379,
        "password": "${REDIS_PASSWORD}",
        "keyPrefix": "session:",
        "ttl": 86400
      },
      "backup": {
        "provider": "postgresql",
        "connectionString": "${POSTGRES_URL}",
        "retentionDays": 30
      }
    },
    "terminals": {
      "maxTerminalsPerSession": 10,
      "defaultShell": "/bin/bash",
      "timeout": 3600000,
      "recording": {
        "enabled": true,
        "storage": "s3",
        "compression": true,
        "retention": "90d"
      }
    },
    "repl": {
      "historySize": 1000,
      "autoComplete": true,
      "commandTimeout": 30000,
      "maxConcurrentCommands": 5
    },
    "collaboration": {
      "maxCollaborators": 10,
      "shareTokenTTL": 7200000,
      "permissions": {
        "default": {
          "canView": true,
          "canExecute": false,
          "canEdit": false
        }
      }
    },
    "security": {
      "sessionTimeout": 1800000,
      "mfaRequired": false,
      "ipBinding": false,
      "encryption": {
        "enabled": true,
        "algorithm": "AES-256-GCM"
      }
    }
  }
}
```

## Environment-Specific Configurations

### Development
```json
{
  "environment": "development",
  "sessionManager": {
    "terminals": {
      "recording": { "enabled": false },
      "timeout": 7200000
    },
    "security": {
      "sessionTimeout": 86400000,
      "encryption": { "enabled": false }
    }
  }
}
```

### Production
```json
{
  "environment": "production",
  "sessionManager": {
    "security": {
      "sessionTimeout": 900000,
      "mfaRequired": true,
      "ipBinding": true,
      "encryption": { "enabled": true }
    },
    "monitoring": {
      "enabled": true,
      "metrics": ["session_count", "command_latency", "error_rate"]
    }
  }
}
```