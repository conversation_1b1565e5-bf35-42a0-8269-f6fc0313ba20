# State Management Service - Usage Patterns and Examples

## Common Usage Patterns

### 1. Session Lifecycle Management

#### Creating and Managing Sessions

```typescript
// Agent registration and session start
const sessionManager = new SessionManager(eventBus, config);
const sessionToken = await sessionManager.createSession({
  name: 'claude-tdd-001',
  namespace: 'ultrasonic-project',
  initialAgents: 2,
  agentProfile: {
    type: 'developer',
    capabilities: ['tdd', 'typescript', 'testing']
  }
});

// Store session context
await sessionToken.checkpoint();

// Restore session after interruption
const restoredSession = await sessionManager.restoreSession('claude-tdd-001');
```

#### Session State Synchronization

```typescript
// Synchronize CLI state with web clients
class SessionManager {
  async syncWithCLI(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Get latest state from CLI process
    const cliState = await this.cliSync.getState(sessionId);
    
    // Merge states
    session.state = this.mergeStates(session.state, cliState);
    session.lastActivity = new Date();
    
    // Notify web clients of state change
    this.notifyWebClients(session, 'state_updated', session.state);
    
    // Persist updated session
    await this.persistence.save(session);
  }
  
  private notifyWebClients(session: Session, event: string, data: any): void {
    for (const client of session.webClients) {
      if (client.socket && client.socket.readyState === WebSocket.OPEN) {
        client.socket.send(JSON.stringify({
          type: 'session_event',
          event,
          data,
          timestamp: Date.now()
        }));
      }
    }
  }
}
```

### 2. Agent State Management

#### Coordinating Agent Activities

```typescript
// Register agent and track state
const agent = await spawnAgent({
  type: 'researcher',
  name: 'market-analyst-01',
  memoryNamespace: 'market-research'
});

// Store agent operational state
await stateManager.save_state(`agent:${agent.id}:state`, {
  status: 'active',
  currentTask: 'analyze-competitor-landscape',
  progress: 0.3,
  lastActivity: new Date(),
  taskQueue: ['research-trends', 'compile-report']
});

// Update agent progress
await stateManager.update_state(`agent:${agent.id}:state`, {
  progress: 0.7,
  completedSubtasks: ['gather-data', 'initial-analysis']
});
```

#### Resource Locking and Coordination

```typescript
// Request resource lock for coordination
const lockToken = await memoryBank.requestResourceLock(
  'claude-tdd-001', 
  'src/user-service.ts'
);

try {
  // Perform exclusive operations
  await editFile('src/user-service.ts', modifications);
  await runTests('src/__tests__/user-service.test.ts');
  
  // Update agent state with results
  await stateManager.update_state(`agent:${agentId}:state`, {
    completedFiles: ['src/user-service.ts'],
    testResults: 'passing'
  });
} finally {
  // Always release the lock
  await memoryBank.releaseResourceLock(lockToken);
}
```

### 3. Workflow State Tracking

#### Multi-Phase Workflow Management

```typescript
// Initialize workflow state
const workflowId = 'enterprise-software-lifecycle';
await stateManager.save_state(`workflow:${workflowId}`, {
  name: 'Enterprise Software Development Lifecycle',
  currentPhase: 'requirements-gathering',
  phases: {
    'requirements-gathering': { status: 'running', progress: 0.4 },
    'architecture-design': { status: 'pending', progress: 0 },
    'implementation': { status: 'pending', progress: 0 },
    'testing-validation': { status: 'pending', progress: 0 }
  },
  variables: {
    project_name: 'enterprise-platform',
    security_level: 'high',
    compliance_required: true
  }
});

// Update workflow progress
await stateManager.update_state(`workflow:${workflowId}`, {
  'phases.requirements-gathering.status': 'completed',
  'phases.requirements-gathering.progress': 1.0,
  'phases.architecture-design.status': 'running',
  'phases.architecture-design.progress': 0.1,
  currentPhase: 'architecture-design'
});
```

#### Checkpoint and Recovery

```typescript
// Create workflow checkpoint
await stateManager.create_snapshot(`workflow:${workflowId}`);

// Handle workflow failure recovery
const recoverWorkflow = async (workflowId: string) => {
  try {
    // Get last checkpoint
    const snapshots = await stateManager.list_snapshots({
      namespace: `workflow:${workflowId}`,
      orderBy: 'timestamp',
      limit: 1
    });
    
    if (snapshots.length > 0) {
      // Restore from checkpoint
      await stateManager.restore_snapshot(snapshots[0].id);
      
      // Resume workflow from last known good state
      const workflowState = await stateManager.get_state(`workflow:${workflowId}`);
      await resumeWorkflowFromPhase(workflowState.currentPhase);
    }
  } catch (error) {
    console.error('Workflow recovery failed:', error);
    throw new Error('Unable to recover workflow state');
  }
};
```

### 4. Configuration Management

#### Dynamic Configuration Updates

```typescript
// Store configuration with versioning
await stateManager.save_state('system:config', {
  version: '2.1.0',
  features: {
    advancedAnalytics: true,
    parallelExecution: true,
    autoRecovery: true
  },
  limits: {
    maxConcurrentAgents: 10,
    maxWorkflowDepth: 5,
    sessionTimeout: 3600000
  }
}, { version: true });

// Subscribe to configuration changes
stateManager.on('config_updated', async (config) => {
  // Apply new configuration
  await applyConfiguration(config);
  
  // Notify all active agents
  const activeAgents = await getActiveAgents();
  for (const agent of activeAgents) {
    await agent.updateConfiguration(config);
  }
});
```

#### Environment-Specific Settings

```typescript
// Load environment-specific configuration
const environment = process.env.NODE_ENV || 'development';
const config = await stateManager.get_state(`config:${environment}`);

// Merge with base configuration
const baseConfig = await stateManager.get_state('config:base');
const mergedConfig = deepMerge(baseConfig, config);

// Store effective configuration
await stateManager.save_state('config:effective', mergedConfig);
```

### 5. Event Sourcing Patterns

#### Storing and Replaying Events

```typescript
// Store state change events
const eventStore = new EventStore();

await eventStore.append({
  id: generateId(),
  type: 'agent:task:completed',
  timestamp: Date.now(),
  payload: {
    agentId: 'claude-researcher-01',
    taskId: 'market-analysis',
    result: 'success',
    duration: 12000,
    artifacts: ['market-report.md', 'competitor-analysis.json']
  }
});

// Replay events to reconstruct state
const currentState = await eventStore.replay();

// Get state at specific point in time
const historicalState = await eventStore.replay(
  Date.now() - (24 * 60 * 60 * 1000) // 24 hours ago
);
```

#### Projection Updates

```typescript
// Update projections based on events
class EventStore {
  private async updateProjections(event: Event): Promise<void> {
    switch (event.type) {
      case 'task:completed':
        await this.updateTaskCompletionProjection(event);
        break;
      case 'agent:performance':
        await this.updateAgentPerformanceProjection(event);
        break;
      case 'workflow:phase:completed':
        await this.updateWorkflowProgressProjection(event);
        break;
    }
  }
  
  private async updateTaskCompletionProjection(event: Event): Promise<void> {
    const projection = this.projections.get('task_completions') || {};
    const { agentId, taskType } = event.payload;
    
    projection[agentId] = projection[agentId] || {};
    projection[agentId][taskType] = (projection[agentId][taskType] || 0) + 1;
    projection[agentId].total = (projection[agentId].total || 0) + 1;
    
    this.projections.set('task_completions', projection);
  }
}
```

### 6. Cross-Service State Synchronization

#### Distributed State Updates

```typescript
// Coordinate state across multiple services
const distributedUpdate = async (updates: StateUpdate[]) => {
  const transaction = await stateManager.begin_transaction();
  
  try {
    for (const update of updates) {
      await stateManager.update_state(update.key, update.value, {
        transactionId: transaction.id
      });
    }
    
    // Commit all changes atomically
    await stateManager.commit_transaction(transaction.id);
    
    // Notify other services
    await eventBus.emit('state:distributed_update', {
      transactionId: transaction.id,
      updates: updates
    });
    
  } catch (error) {
    // Rollback on failure
    await stateManager.rollback_transaction(transaction.id);
    throw error;
  }
};
```

#### Cache Invalidation Patterns

```typescript
// Coordinate cache invalidation across services
stateManager.on('state_updated', async (event) => {
  const { key, value, metadata } = event;
  
  // Invalidate related caches
  const cacheKeys = generateCacheKeys(key);
  await Promise.all(
    cacheKeys.map(cacheKey => 
      cacheManager.invalidate(cacheKey)
    )
  );
  
  // Notify dependent services
  await notifyDependentServices(key, value, metadata);
});
```

These patterns demonstrate how to effectively use the state management service for reliable, consistent, and performant state operations across the RUST-SS system.