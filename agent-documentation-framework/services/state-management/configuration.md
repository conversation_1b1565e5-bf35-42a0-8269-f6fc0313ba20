# State Management Service - Configuration Guide

## Configuration Structure

### Main Configuration Schema

```typescript
interface StateManagementConfig {
  storage: StorageConfig;
  persistence: PersistenceConfig;
  caching: CachingConfig;
  replication: ReplicationConfig;
  transactions: TransactionConfig;
  monitoring: MonitoringConfig;
  security: SecurityConfig;
  performance: PerformanceConfig;
}
```

## Storage Configuration

### Multi-Tier Storage Setup

```json
{
  "storage": {
    "tiers": {
      "l1_cache": {
        "type": "memory",
        "maxSize": "512MB",
        "evictionPolicy": "lru",
        "ttl": 300
      },
      "l2_cache": {
        "type": "redis",
        "host": "redis-cluster.internal",
        "port": 6379,
        "password": "${REDIS_PASSWORD}",
        "database": 0,
        "maxConnections": 50,
        "keyPrefix": "state:",
        "ttl": 3600
      },
      "l3_storage": {
        "type": "postgresql",
        "host": "postgres-primary.internal",
        "port": 5432,
        "database": "claude_flow_state",
        "username": "state_service",
        "password": "${POSTGRES_PASSWORD}",
        "ssl": true,
        "poolSize": 20,
        "connectionTimeout": 5000
      },
      "archive": {
        "type": "s3",
        "bucket": "claude-flow-state-archive",
        "region": "us-west-2",
        "accessKeyId": "${AWS_ACCESS_KEY_ID}",
        "secretAccessKey": "${AWS_SECRET_ACCESS_KEY}",
        "storageClass": "STANDARD_IA",
        "compression": true
      }
    },
    "routing": {
      "hotDataThreshold": "1MB",
      "archiveAfterDays": 90,
      "compressionThreshold": "10MB"
    }
  }
}
```

### Storage Tier Selection Rules

```json
{
  "storage": {
    "rules": {
      "agent_state": {
        "primary": "l2_cache",
        "backup": "l3_storage",
        "ttl": 1800
      },
      "session_data": {
        "primary": "l3_storage",
        "cache": "l2_cache",
        "archive": "archive",
        "ttl": 86400
      },
      "workflow_state": {
        "primary": "l3_storage",
        "cache": "l1_cache",
        "snapshotInterval": 300
      },
      "configuration": {
        "primary": "l3_storage",
        "cache": "l1_cache",
        "replication": "sync"
      }
    }
  }
}
```

## Persistence Configuration

### Session Persistence Settings

```json
{
  "persistence": {
    "sessions": {
      "autoSave": true,
      "saveInterval": 30000,
      "checkpointInterval": 300000,
      "maxCheckpoints": 10,
      "compression": true,
      "encryption": true,
      "retentionDays": 30
    },
    "agents": {
      "stateSnapshots": true,
      "snapshotInterval": 60000,
      "maxSnapshots": 5,
      "persistMemory": true,
      "memoryLimit": "100MB"
    },
    "workflows": {
      "checkpointOnPhaseChange": true,
      "autoRecovery": true,
      "recoveryTimeout": 30000,
      "preserveIntermediateResults": true
    }
  }
}
```

### Recovery Configuration

```json
{
  "recovery": {
    "strategies": {
      "session": {
        "method": "checkpoint",
        "fallback": "snapshot",
        "timeout": 10000
      },
      "agent": {
        "method": "state_restore",
        "restartOnFailure": true,
        "maxRestartAttempts": 3
      },
      "workflow": {
        "method": "phase_rollback",
        "preserveCompletedPhases": true,
        "notifyOnRecovery": true
      }
    },
    "backup": {
      "enabled": true,
      "schedule": "0 2 * * *",
      "retentionDays": 7,
      "incrementalBackups": true,
      "compression": "gzip"
    }
  }
}
```

## Caching Configuration

### Cache Strategy Settings

```json
{
  "caching": {
    "levels": {
      "l1": {
        "type": "memory",
        "size": "256MB",
        "algorithm": "lru",
        "maxEntries": 10000,
        "ttl": 300
      },
      "l2": {
        "type": "redis",
        "cluster": true,
        "nodes": [
          "redis-01.internal:6379",
          "redis-02.internal:6379",
          "redis-03.internal:6379"
        ],
        "keyPrefix": "cache:",
        "ttl": 3600,
        "maxMemory": "2GB"
      }
    },
    "policies": {
      "writeThrough": true,
      "readThrough": true,
      "refreshAhead": true,
      "writeCoalescing": true
    },
    "invalidation": {
      "strategy": "event_driven",
      "propagationDelay": 100,
      "batchUpdates": true,
      "maxBatchSize": 100
    }
  }
}
```

### Cache Warming Configuration

```json
{
  "caching": {
    "warming": {
      "enabled": true,
      "strategies": [
        {
          "name": "session_preload",
          "pattern": "session:*",
          "schedule": "startup",
          "priority": "high"
        },
        {
          "name": "config_preload",
          "pattern": "config:*",
          "schedule": "startup",
          "priority": "critical"
        },
        {
          "name": "agent_state_refresh",
          "pattern": "agent:*/state",
          "schedule": "*/5 * * * *",
          "priority": "medium"
        }
      ]
    }
  }
}
```

## Replication Configuration

### High Availability Setup

```json
{
  "replication": {
    "mode": "multi_master",
    "topology": "mesh",
    "nodes": [
      {
        "id": "primary-us-west",
        "region": "us-west-2",
        "role": "master",
        "weight": 1.0
      },
      {
        "id": "replica-us-east",
        "region": "us-east-1",
        "role": "master",
        "weight": 0.8
      },
      {
        "id": "replica-eu-west",
        "region": "eu-west-1",
        "role": "master",
        "weight": 0.6
      }
    ],
    "consistency": {
      "level": "eventual",
      "maxLag": 1000,
      "conflictResolution": "timestamp",
      "reconciliationInterval": 30000
    },
    "failover": {
      "autoFailover": true,
      "healthCheckInterval": 5000,
      "failoverTimeout": 30000,
      "splitBrainPrevention": true
    }
  }
}
```

### Synchronization Settings

```json
{
  "replication": {
    "synchronization": {
      "batchSize": 1000,
      "batchTimeout": 5000,
      "compression": true,
      "encryption": true,
      "retryPolicy": {
        "maxRetries": 3,
        "backoffMultiplier": 2,
        "maxBackoff": 30000
      }
    },
    "monitoring": {
      "lagAlerts": {
        "warning": 5000,
        "critical": 15000
      },
      "conflictAlerts": true,
      "performanceMetrics": true
    }
  }
}
```

## Transaction Configuration

### ACID Compliance Settings

```json
{
  "transactions": {
    "isolation": "read_committed",
    "timeout": 30000,
    "maxConcurrency": 100,
    "deadlockDetection": true,
    "deadlockTimeout": 10000,
    "lockWaitTimeout": 5000,
    "retryPolicy": {
      "maxRetries": 3,
      "backoffBase": 1000,
      "jitter": true
    }
  }
}
```

### Distributed Transaction Support

```json
{
  "transactions": {
    "distributed": {
      "enabled": true,
      "coordinator": "2pc",
      "participantTimeout": 60000,
      "prepareTimeout": 30000,
      "commitTimeout": 30000,
      "recoveryInterval": 300000,
      "logRetention": 86400000
    }
  }
}
```

## Security Configuration

### Encryption Settings

```json
{
  "security": {
    "encryption": {
      "atRest": {
        "enabled": true,
        "algorithm": "AES-256-GCM",
        "keyRotationDays": 90,
        "keyManagement": "aws_kms"
      },
      "inTransit": {
        "enabled": true,
        "tlsVersion": "1.3",
        "cipherSuites": [
          "TLS_AES_256_GCM_SHA384",
          "TLS_CHACHA20_POLY1305_SHA256"
        ]
      }
    },
    "authentication": {
      "method": "mutual_tls",
      "certificateValidation": true,
      "certificateRevocation": true
    },
    "authorization": {
      "model": "rbac",
      "defaultDeny": true,
      "auditLog": true
    }
  }
}
```

### Access Control Configuration

```json
{
  "security": {
    "accessControl": {
      "namespaces": {
        "isolation": "strict",
        "crossNamespaceAccess": false,
        "adminOverride": true
      },
      "rateLimiting": {
        "enabled": true,
        "requestsPerSecond": 1000,
        "burstSize": 2000,
        "keyFunction": "ip_address"
      },
      "auditLogging": {
        "enabled": true,
        "logLevel": "info",
        "includePayload": false,
        "retention": "90d"
      }
    }
  }
}
```

## Performance Configuration

### Optimization Settings

```json
{
  "performance": {
    "connectionPools": {
      "postgres": {
        "minConnections": 5,
        "maxConnections": 20,
        "idleTimeout": 300000,
        "acquireTimeout": 10000,
        "validationQuery": "SELECT 1"
      },
      "redis": {
        "minConnections": 2,
        "maxConnections": 10,
        "keepAlive": 30000,
        "commandTimeout": 5000
      }
    },
    "batching": {
      "enabled": true,
      "maxBatchSize": 100,
      "batchTimeout": 10,
      "operationTypes": [
        "read",
        "write",
        "delete"
      ]
    },
    "compression": {
      "enabled": true,
      "algorithm": "lz4",
      "threshold": 1024,
      "level": 6
    }
  }
}
```

### Latency Targets

```json
{
  "performance": {
    "targets": {
      "readLatency": {
        "p50": 1,
        "p95": 10,
        "p99": 50
      },
      "writeLatency": {
        "p50": 10,
        "p95": 50,
        "p99": 100
      },
      "transactionLatency": {
        "p50": 100,
        "p95": 500,
        "p99": 1000
      }
    },
    "alerts": {
      "latencyThreshold": 100,
      "errorRateThreshold": 0.01,
      "availabilityThreshold": 0.999
    }
  }
}
```

## Monitoring Configuration

### Metrics and Alerting

```json
{
  "monitoring": {
    "metrics": {
      "enabled": true,
      "exportInterval": 15000,
      "exporters": [
        {
          "type": "prometheus",
          "endpoint": "/metrics",
          "port": 9090
        },
        {
          "type": "cloudwatch",
          "namespace": "ClaudeFlow/StateManagement",
          "region": "us-west-2"
        }
      ]
    },
    "healthChecks": {
      "enabled": true,
      "interval": 30000,
      "timeout": 5000,
      "endpoints": [
        "/health",
        "/health/ready",
        "/health/live"
      ]
    },
    "logging": {
      "level": "info",
      "format": "json",
      "destination": "stdout",
      "sampling": {
        "enabled": true,
        "rate": 0.1
      }
    }
  }
}
```

## Environment-Specific Configurations

### Development Environment

```json
{
  "environment": "development",
  "debug": true,
  "storage": {
    "tiers": {
      "l1_cache": { "maxSize": "64MB" },
      "l2_cache": { "host": "localhost", "port": 6379 },
      "l3_storage": { "host": "localhost", "port": 5432 }
    }
  },
  "persistence": {
    "sessions": {
      "saveInterval": 10000,
      "retentionDays": 1
    }
  },
  "security": {
    "encryption": {
      "atRest": { "enabled": false },
      "inTransit": { "enabled": false }
    }
  }
}
```

### Production Environment

```json
{
  "environment": "production",
  "debug": false,
  "storage": {
    "tiers": {
      "l1_cache": { "maxSize": "1GB" },
      "l2_cache": { 
        "cluster": true,
        "nodes": ["redis-cluster.prod.internal"]
      },
      "l3_storage": { 
        "host": "postgres-cluster.prod.internal",
        "ssl": true,
        "poolSize": 50
      }
    }
  },
  "replication": {
    "mode": "multi_master",
    "consistency": { "level": "strong" }
  },
  "security": {
    "encryption": {
      "atRest": { "enabled": true },
      "inTransit": { "enabled": true }
    },
    "auditLogging": { "enabled": true }
  },
  "monitoring": {
    "metrics": { "enabled": true },
    "alerts": { "enabled": true }
  }
}
```

This configuration system provides flexible, environment-aware settings for optimal state management performance and reliability.