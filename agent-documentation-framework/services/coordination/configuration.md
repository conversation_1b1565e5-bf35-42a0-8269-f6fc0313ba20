# Coordination Service - Configuration Guide

## Core Configuration Structure

The coordination service uses a layered configuration approach to support multiple coordination modes and strategies.

### Main Configuration Schema

```json
{
  "coordination": {
    "default_mode": "centralized",
    "max_concurrent_swarms": 10,
    "task_queue_size": 1000,
    "metrics_interval": "5s",
    "enabled_modes": ["centralized", "distributed", "hierarchical", "mesh", "hybrid"],
    "strategies": {
      "research": { "enabled": true, "max_agents": 10 },
      "development": { "enabled": true, "max_agents": 15 },
      "analysis": { "enabled": true, "max_agents": 8 },
      "testing": { "enabled": true, "max_agents": 12 },
      "optimization": { "enabled": true, "max_agents": 6 },
      "maintenance": { "enabled": true, "max_agents": 5 }
    }
  }
}
```

### Swarm Configuration

```rust
struct SwarmConfig {
    objective: String,
    strategy: ExecutionStrategy,
    mode: CoordinationMode,
    max_agents: usize,
    parallel: bool,
    batch_optimized: bool,
    memory_shared: bool,
    timeout: Option<Duration>,
    quality_threshold: Option<f32>,
}
```

## Environment-Specific Configurations

### Development Configuration

```json
{
  "coordination": {
    "default_mode": "centralized",
    "max_concurrent_swarms": 3,
    "task_queue_size": 100,
    "debug_logging": true,
    "mock_agents": true,
    "load_balancer": {
      "enabled": false
    }
  }
}
```

### Production Configuration

```json
{
  "coordination": {
    "default_mode": "hybrid",
    "max_concurrent_swarms": 50,
    "task_queue_size": 10000,
    "debug_logging": false,
    "load_balancer": {
      "enabled": true,
      "threshold": 0.75,
      "interval": "30s"
    },
    "resource_limits": {
      "max_memory_per_swarm": "1GB",
      "max_cpu_per_agent": "2",
      "task_timeout": "30m"
    }
  }
}
```

## Configuration Validation Rules

### Required Fields
- `default_mode` must be one of: centralized, distributed, hierarchical, mesh, hybrid
- `max_concurrent_swarms` must be between 1 and 100
- `task_queue_size` must be between 10 and 100000

### Strategy Configuration
- Each strategy must have `enabled` and `max_agents` fields
- `max_agents` must be between 1 and 50
- At least one strategy must be enabled

### Mode-Specific Configuration

#### Centralized Mode
```json
{
  "centralized": {
    "coordinator_selection": "round-robin",
    "failover_enabled": true,
    "heartbeat_interval": "5s"
  }
}
```

#### Distributed Mode
```json
{
  "distributed": {
    "consensus_algorithm": "raft",
    "quorum_size": 3,
    "election_timeout": "150ms",
    "replication_factor": 2
  }
}
```

#### Hierarchical Mode
```json
{
  "hierarchical": {
    "max_levels": 4,
    "delegates_per_level": [2, 4, 8, 16],
    "propagation_timeout": "1s"
  }
}
```

## Default Values and Required Fields

### Default Values
```rust
impl Default for CoordinationConfig {
    fn default() -> Self {
        Self {
            default_mode: CoordinationMode::Centralized,
            max_concurrent_swarms: 5,
            task_queue_size: 1000,
            metrics_interval: Duration::from_secs(5),
            enabled_modes: vec![CoordinationMode::Centralized],
            load_threshold: 0.75,
            balance_interval: Duration::from_secs(30),
        }
    }
}
```

### Required Fields
- `coordination` (root object)
- `coordination.default_mode`
- `coordination.strategies` (at least one enabled)

### Optional Fields with Defaults
- `max_concurrent_swarms`: 5
- `task_queue_size`: 1000
- `metrics_interval`: "5s"
- `load_threshold`: 0.75
- `balance_interval`: "30s"

## Advanced Configuration Options

### Resource Management
```json
{
  "resources": {
    "enable_pooling": true,
    "pool_sizes": {
      "task_definitions": 1000,
      "agent_profiles": 100,
      "resource_handles": 500
    },
    "gc_interval": "5m"
  }
}
```

### Performance Tuning
```json
{
  "performance": {
    "task_batch_size": 10,
    "parallel_assignment": true,
    "cache_agent_capabilities": true,
    "cache_ttl": "5m",
    "event_buffer_size": 10000
  }
}
```

### Monitoring Configuration
```json
{
  "monitoring": {
    "export_metrics": true,
    "metrics_endpoint": "http://localhost:9090/metrics",
    "trace_sampling_rate": 0.1,
    "log_level": "info"
  }
}
```