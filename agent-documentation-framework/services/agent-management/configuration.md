# Agent Management Service - Configuration Guide

## Core Configuration Structure

```json
{
  "agentManagement": {
    "spawning": {
      "maxConcurrentAgents": 100,
      "agentTimeout": 300000,
      "spawnRetries": 3,
      "poolSize": {
        "min": 5,
        "max": 50,
        "target": 20
      }
    },
    "lifecycle": {
      "heartbeatInterval": 30000,
      "healthCheckTimeout": 5000,
      "gracefulShutdownTimeout": 60000,
      "zombieDetectionInterval": 120000
    },
    "resourceLimits": {
      "memoryLimitMB": 512,
      "cpuLimitPercent": 25,
      "diskLimitMB": 1024,
      "networkBandwidthMbps": 100
    },
    "types": {
      "researcher": {
        "enabled": true,
        "maxInstances": 10,
        "defaultTimeout": 600000,
        "resourceProfile": "medium"
      },
      "coder": {
        "enabled": true,
        "maxInstances": 20,
        "defaultTimeout": 1800000,
        "resourceProfile": "high"
      },
      "analyst": {
        "enabled": true,
        "maxInstances": 15,
        "defaultTimeout": 900000,
        "resourceProfile": "medium"
      },
      "tester": {
        "enabled": true,
        "maxInstances": 10,
        "defaultTimeout": 300000,
        "resourceProfile": "low"
      }
    },
    "coordination": {
      "swarmEnabled": true,
      "maxSwarmSize": 10,
      "coordinationProtocol": "gossip",
      "leaderElection": {
        "enabled": true,
        "algorithm": "raft",
        "electionTimeout": 5000
      }
    },
    "communication": {
      "protocolVersion": "v2",
      "compression": true,
      "encryption": {
        "enabled": true,
        "algorithm": "AES-256-GCM"
      },
      "messageQueueSize": 1000
    }
  }
}
```

## Environment-Specific Configurations

### Development
```json
{
  "environment": "development",
  "agentManagement": {
    "spawning": {
      "maxConcurrentAgents": 20,
      "poolSize": {
        "min": 2,
        "max": 10,
        "target": 5
      }
    },
    "resourceLimits": {
      "memoryLimitMB": 256,
      "cpuLimitPercent": 50
    },
    "communication": {
      "encryption": { "enabled": false }
    }
  }
}
```

### Production
```json
{
  "environment": "production",
  "agentManagement": {
    "spawning": {
      "maxConcurrentAgents": 200,
      "poolSize": {
        "min": 10,
        "max": 100,
        "target": 40
      }
    },
    "resourceLimits": {
      "memoryLimitMB": 1024,
      "cpuLimitPercent": 20
    },
    "communication": {
      "encryption": { "enabled": true }
    }
  }
}
```