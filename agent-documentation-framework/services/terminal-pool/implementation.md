# Terminal Pool Service - Implementation Details

## Technical Architecture

The terminal pool service implements a containerized terminal management system with dynamic resource allocation, security isolation, and real-time communication capabilities.

### Core Components

```rust
pub struct TerminalPoolService {
    pool_manager: PoolManager,
    container_runtime: Box<dyn ContainerRuntime>,
    session_manager: SessionManager,
    security_manager: SecurityManager,
    resource_manager: ResourceManager,
    communication_handler: CommunicationHandler,
    config: TerminalPoolConfig,
}

struct PoolManager {
    active_terminals: HashMap<TerminalId, TerminalInstance>,
    available_pool: VecDeque<TerminalInstance>,
    allocation_queue: PriorityQueue<AllocationRequest>,
    pool_metrics: PoolMetrics,
    scaling_policy: ScalingPolicy,
}

struct TerminalInstance {
    id: TerminalId,
    container_id: ContainerId,
    status: TerminalStatus,
    allocated_resources: ResourceAllocation,
    assigned_agent: Option<AgentId>,
    session: Option<TerminalSession>,
    created_at: DateTime<Utc>,
    last_activity: DateTime<Utc>,
}
```

## Key Data Structures

### Terminal Instance Management

```rust
#[derive(Clone, Debug)]
struct TerminalInstance {
    id: TerminalId,
    container_id: ContainerId,
    environment_type: EnvironmentType,
    status: TerminalStatus,
    allocated_resources: ResourceAllocation,
    network_config: NetworkConfig,
    security_context: SecurityContext,
    session_info: Option<SessionInfo>,
    created_at: DateTime<Utc>,
    last_activity: DateTime<Utc>,
    health_status: HealthStatus,
}

#[derive(Debug, PartialEq)]
enum TerminalStatus {
    Creating,
    Available,
    Allocated,
    Running,
    Suspended,
    Terminating,
    Failed,
}

impl TerminalInstance {
    async fn allocate_to_agent(&mut self, agent_id: AgentId) -> Result<SessionToken> {
        if self.status != TerminalStatus::Available {
            return Err(Error::TerminalNotAvailable(self.id.clone()));
        }
        
        self.assigned_agent = Some(agent_id);
        self.status = TerminalStatus::Allocated;
        self.last_activity = Utc::now();
        
        // Generate session token
        let session_token = SessionToken::new(&self.id, &agent_id);
        
        // Create session info
        self.session_info = Some(SessionInfo {
            agent_id,
            session_token: session_token.clone(),
            started_at: Utc::now(),
            last_activity: Utc::now(),
        });
        
        Ok(session_token)
    }
    
    async fn execute_command(&mut self, command: ExecutionRequest) -> Result<ExecutionResponse> {
        // Validate security permissions
        self.validate_command_security(&command)?;
        
        // Update activity timestamp
        self.last_activity = Utc::now();
        self.status = TerminalStatus::Running;
        
        // Execute command in container
        let execution_result = self.container_runtime
            .execute_command(&self.container_id, &command)
            .await?;
        
        // Update status
        self.status = TerminalStatus::Allocated;
        
        Ok(execution_result)
    }
}
```

### Resource Management

```rust
#[derive(Clone, Debug)]
struct ResourceAllocation {
    cpu_cores: f32,
    memory_bytes: u64,
    storage_bytes: u64,
    network_bandwidth_bps: Option<u64>,
    gpu_allocation: Option<GpuAllocation>,
}

struct ResourceManager {
    total_resources: SystemResources,
    allocated_resources: ResourceAllocation,
    resource_limits: ResourceLimits,
    quota_manager: QuotaManager,
}

impl ResourceManager {
    fn can_allocate(&self, requirements: &ResourceRequirements) -> bool {
        let available = self.calculate_available_resources();
        
        available.cpu_cores >= requirements.cpu_cores &&
        available.memory_bytes >= requirements.memory_bytes &&
        available.storage_bytes >= requirements.storage_bytes
    }
    
    async fn allocate_resources(&mut self, requirements: ResourceRequirements) -> Result<ResourceAllocation> {
        if !self.can_allocate(&requirements) {
            return Err(Error::InsufficientResources);
        }
        
        let allocation = ResourceAllocation {
            cpu_cores: requirements.cpu_cores,
            memory_bytes: requirements.memory_bytes,
            storage_bytes: requirements.storage_bytes,
            network_bandwidth_bps: requirements.network_bandwidth_bps,
            gpu_allocation: None,
        };
        
        // Update allocated resources
        self.allocated_resources.cpu_cores += allocation.cpu_cores;
        self.allocated_resources.memory_bytes += allocation.memory_bytes;
        self.allocated_resources.storage_bytes += allocation.storage_bytes;
        
        Ok(allocation)
    }
    
    fn release_resources(&mut self, allocation: &ResourceAllocation) {
        self.allocated_resources.cpu_cores -= allocation.cpu_cores;
        self.allocated_resources.memory_bytes -= allocation.memory_bytes;
        self.allocated_resources.storage_bytes -= allocation.storage_bytes;
    }
}
```

## Core Algorithms

### Pool Management Algorithm

```rust
impl PoolManager {
    async fn allocate_terminal(&mut self, request: AllocationRequest) -> Result<TerminalInstance> {
        // 1. Check for available pre-warmed terminal
        if let Some(terminal) = self.find_available_terminal(&request) {
            return self.assign_terminal(terminal, request).await;
        }
        
        // 2. Check if we can create a new terminal
        if self.active_terminals.len() < self.config.max_size {
            return self.create_new_terminal(request).await;
        }
        
        // 3. Add to queue and trigger scaling
        self.allocation_queue.push(request);
        self.trigger_scaling_if_needed().await?;
        
        Err(Error::PoolCapacityExceeded)
    }
    
    fn find_available_terminal(&self, request: &AllocationRequest) -> Option<&TerminalInstance> {
        self.available_pool
            .iter()
            .find(|terminal| {
                terminal.status == TerminalStatus::Available &&
                terminal.environment_type == request.environment_type &&
                self.resource_manager.can_satisfy(
                    &terminal.allocated_resources,
                    &request.resource_requirements
                )
            })
    }
    
    async fn create_new_terminal(&mut self, request: AllocationRequest) -> Result<TerminalInstance> {
        // 1. Allocate resources
        let resource_allocation = self.resource_manager
            .allocate_resources(request.resource_requirements)
            .await?;
        
        // 2. Create container
        let container_id = self.container_runtime
            .create_container(&request.environment_type, &resource_allocation)
            .await?;
        
        // 3. Initialize terminal instance
        let terminal = TerminalInstance {
            id: TerminalId::new(),
            container_id,
            environment_type: request.environment_type,
            status: TerminalStatus::Creating,
            allocated_resources: resource_allocation,
            network_config: self.create_network_config()?,
            security_context: self.create_security_context(&request)?,
            session_info: None,
            created_at: Utc::now(),
            last_activity: Utc::now(),
            health_status: HealthStatus::Unknown,
        };
        
        // 4. Start container and wait for ready
        self.container_runtime.start_container(&container_id).await?;
        self.wait_for_container_ready(&container_id).await?;
        
        // 5. Update terminal status
        let mut terminal = terminal;
        terminal.status = TerminalStatus::Available;
        terminal.health_status = HealthStatus::Healthy;
        
        // 6. Add to pool
        self.active_terminals.insert(terminal.id.clone(), terminal.clone());
        
        Ok(terminal)
    }
}
```

### Command Execution Engine

```rust
struct CommandExecutor {
    container_runtime: Box<dyn ContainerRuntime>,
    security_validator: SecurityValidator,
    output_buffer: OutputBuffer,
}

impl CommandExecutor {
    async fn execute_command(
        &mut self,
        terminal_id: &TerminalId,
        command: ExecutionRequest,
    ) -> Result<ExecutionResponse> {
        // 1. Validate command security
        self.security_validator.validate_command(&command)?;
        
        // 2. Prepare execution environment
        let exec_config = ExecutionConfig {
            command: command.command,
            working_directory: command.working_directory
                .unwrap_or_else(|| "/workspace".to_string()),
            environment_vars: command.environment_vars,
            timeout: command.timeout.unwrap_or(Duration::from_secs(300)),
            resource_limits: self.get_resource_limits(terminal_id)?,
        };
        
        // 3. Execute command with streaming
        let execution_id = ExecutionId::new();
        let mut output_stream = self.container_runtime
            .execute_with_streaming(&terminal_id, exec_config)
            .await?;
        
        // 4. Collect output and monitor execution
        let mut stdout_buffer = Vec::new();
        let mut stderr_buffer = Vec::new();
        let start_time = Instant::now();
        
        while let Some(output) = output_stream.next().await {
            match output? {
                ContainerOutput::Stdout(data) => {
                    stdout_buffer.extend_from_slice(&data);
                    self.output_buffer.append_stdout(execution_id, data).await?;
                }
                ContainerOutput::Stderr(data) => {
                    stderr_buffer.extend_from_slice(&data);
                    self.output_buffer.append_stderr(execution_id, data).await?;
                }
                ContainerOutput::ExitCode(code) => {
                    let execution_time = start_time.elapsed();
                    
                    return Ok(ExecutionResponse {
                        execution_id,
                        exit_code: Some(code),
                        stdout: OutputStream::from_bytes(stdout_buffer),
                        stderr: OutputStream::from_bytes(stderr_buffer),
                        execution_time,
                        resource_usage: self.get_resource_usage(terminal_id).await?,
                    });
                }
            }
        }
        
        Err(Error::ExecutionIncomplete)
    }
}
```

### Security Validation

```rust
struct SecurityValidator {
    blocked_commands: HashSet<String>,
    allowed_patterns: Vec<Regex>,
    privilege_checker: PrivilegeChecker,
}

impl SecurityValidator {
    fn validate_command(&self, command: &ExecutionRequest) -> Result<()> {
        // 1. Check against blocked commands
        for blocked in &self.blocked_commands {
            if command.command.contains(blocked) {
                return Err(Error::CommandBlocked(blocked.clone()));
            }
        }
        
        // 2. Check command patterns
        let command_allowed = self.allowed_patterns.iter()
            .any(|pattern| pattern.is_match(&command.command));
        
        if !command_allowed && !self.allowed_patterns.is_empty() {
            return Err(Error::CommandNotAllowed);
        }
        
        // 3. Check privilege requirements
        if self.requires_elevated_privileges(&command.command) {
            return Err(Error::ElevatedPrivilegesNotAllowed);
        }
        
        // 4. Validate environment variables
        self.validate_environment_variables(&command.environment_vars)?;
        
        Ok(())
    }
    
    fn requires_elevated_privileges(&self, command: &str) -> bool {
        let privilege_indicators = [
            "sudo", "su", "chmod +s", "setuid", "setgid",
            "mount", "umount", "chroot", "iptables"
        ];
        
        privilege_indicators.iter()
            .any(|indicator| command.contains(indicator))
    }
    
    fn validate_environment_variables(&self, env_vars: &HashMap<String, String>) -> Result<()> {
        let sensitive_vars = ["PATH", "LD_LIBRARY_PATH", "LD_PRELOAD"];
        
        for (key, value) in env_vars {
            if sensitive_vars.contains(&key.as_str()) {
                // Validate that sensitive environment variables are safe
                self.validate_sensitive_env_var(key, value)?;
            }
        }
        
        Ok(())
    }
}
```

## Error Handling Patterns

### Graceful Degradation

```rust
async fn handle_container_failure(
    &mut self,
    terminal_id: TerminalId,
    error: ContainerError,
) -> Result<()> {
    match error {
        ContainerError::Timeout => {
            // Attempt to kill and restart container
            self.restart_terminal(&terminal_id).await?;
        }
        ContainerError::OutOfMemory => {
            // Increase memory allocation and restart
            self.scale_up_terminal_resources(&terminal_id).await?;
        }
        ContainerError::NetworkFailure => {
            // Recreate network configuration
            self.recreate_terminal_network(&terminal_id).await?;
        }
        ContainerError::Fatal(reason) => {
            // Remove terminal from pool and notify
            self.remove_terminal(&terminal_id).await?;
            self.notify_terminal_failure(&terminal_id, reason).await?;
        }
    }
    
    Ok(())
}
```

### Resource Exhaustion Handling

```rust
async fn handle_resource_exhaustion(&mut self) -> Result<()> {
    // 1. Identify least active terminals
    let inactive_terminals = self.find_inactive_terminals(Duration::from_secs(300));
    
    // 2. Suspend or terminate inactive terminals
    for terminal_id in inactive_terminals {
        if self.can_suspend(&terminal_id) {
            self.suspend_terminal(&terminal_id).await?;
        } else {
            self.terminate_terminal(&terminal_id).await?;
        }
    }
    
    // 3. Request additional resources if available
    if self.resource_manager.can_scale_up() {
        self.request_additional_resources().await?;
    }
    
    // 4. Notify administrators if resources critically low
    if self.get_available_capacity() < 0.1 {
        self.send_capacity_alert().await?;
    }
    
    Ok(())
}
```

## Testing Strategies

### Unit Testing

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_terminal_allocation() {
        let mut pool = create_test_pool().await;
        
        let request = AllocationRequest {
            agent_id: AgentId::new(),
            environment_type: EnvironmentType::BasicShell,
            resource_requirements: ResourceRequirements {
                cpu_cores: 1.0,
                memory_bytes: 512 * 1024 * 1024,
                storage_bytes: 1024 * 1024 * 1024,
                network_bandwidth_bps: None,
                gpu_required: false,
            },
            session_config: SessionConfig::default(),
            security_policy: SecurityPolicy::default(),
        };
        
        let terminal = pool.allocate_terminal(request).await.unwrap();
        assert_eq!(terminal.status, TerminalStatus::Allocated);
        assert!(terminal.assigned_agent.is_some());
    }
    
    #[tokio::test]
    async fn test_command_execution() {
        let mut executor = create_test_executor().await;
        let terminal_id = create_test_terminal().await;
        
        let command = ExecutionRequest {
            terminal_id: terminal_id.clone(),
            command: "echo 'Hello, World!'".to_string(),
            working_directory: None,
            environment_vars: HashMap::new(),
            timeout: Some(Duration::from_secs(10)),
            input_stream: None,
        };
        
        let response = executor.execute_command(&terminal_id, command).await.unwrap();
        assert_eq!(response.exit_code, Some(0));
        assert!(response.stdout.to_string().contains("Hello, World!"));
    }
    
    #[tokio::test]
    async fn test_security_validation() {
        let validator = SecurityValidator::new(test_security_config());
        
        let blocked_command = ExecutionRequest {
            command: "sudo rm -rf /".to_string(),
            ..Default::default()
        };
        
        assert!(validator.validate_command(&blocked_command).is_err());
        
        let allowed_command = ExecutionRequest {
            command: "ls -la".to_string(),
            ..Default::default()
        };
        
        assert!(validator.validate_command(&allowed_command).is_ok());
    }
}
```

### Integration Testing

```rust
#[tokio::test]
async fn test_end_to_end_terminal_lifecycle() {
    let mut pool_service = start_test_terminal_pool().await;
    
    // Allocate terminal
    let allocation_request = create_test_allocation_request();
    let terminal = pool_service.allocate_terminal(allocation_request).await.unwrap();
    
    // Execute commands
    let commands = vec![
        "pwd",
        "echo 'test' > test.txt",
        "cat test.txt",
        "ls -la",
    ];
    
    for command in commands {
        let exec_request = ExecutionRequest {
            terminal_id: terminal.id.clone(),
            command: command.to_string(),
            ..Default::default()
        };
        
        let response = pool_service.execute_command(exec_request).await.unwrap();
        assert_eq!(response.exit_code, Some(0));
    }
    
    // Release terminal
    pool_service.release_terminal(&terminal.id).await.unwrap();
    
    // Verify terminal is available for reuse
    let pool_status = pool_service.get_pool_status().await;
    assert!(pool_status.available_terminals > 0);
}
```

## Key Interfaces

### Core Service Traits

```rust
#[async_trait]
trait TerminalPoolManager {
    async fn allocate_terminal(&mut self, request: AllocationRequest) -> Result<TerminalInstance>;
    async fn release_terminal(&mut self, terminal_id: &TerminalId) -> Result<()>;
    async fn execute_command(&mut self, request: ExecutionRequest) -> Result<ExecutionResponse>;
    async fn get_terminal_status(&self, terminal_id: &TerminalId) -> Result<TerminalStatus>;
    async fn list_agent_terminals(&self, agent_id: &AgentId) -> Result<Vec<TerminalInstance>>;
}

#[async_trait]
trait ContainerRuntime {
    async fn create_container(&self, env_type: &EnvironmentType, resources: &ResourceAllocation) -> Result<ContainerId>;
    async fn start_container(&self, container_id: &ContainerId) -> Result<()>;
    async fn stop_container(&self, container_id: &ContainerId) -> Result<()>;
    async fn execute_command(&self, container_id: &ContainerId, command: &ExecutionRequest) -> Result<ExecutionResponse>;
    async fn get_container_stats(&self, container_id: &ContainerId) -> Result<ContainerStats>;
}

trait SecurityManager {
    fn validate_command(&self, command: &str) -> Result<()>;
    fn create_security_context(&self, request: &AllocationRequest) -> Result<SecurityContext>;
    fn audit_command_execution(&self, terminal_id: &TerminalId, command: &str, result: &ExecutionResponse);
}
```