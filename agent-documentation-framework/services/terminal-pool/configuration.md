# Terminal Pool Service - Configuration Guide

## Core Configuration Structure

The terminal pool service manages isolated terminal environments with configurable resource limits, security policies, and pooling strategies.

### Main Configuration Schema

```json
{
  "terminalPool": {
    "pool": {
      "initialSize": 10,
      "maxSize": 100,
      "preWarmCount": 5,
      "growthStrategy": "exponential",
      "shrinkStrategy": "gradual",
      "idleTimeout": 1800000,
      "maxLifetime": 7200000
    },
    "resources": {
      "defaultLimits": {
        "cpu": "1",
        "memory": "512MB",
        "storage": "1GB",
        "networkBandwidth": "100Mbps"
      },
      "quotas": {
        "maxTerminalsPerAgent": 5,
        "maxConcurrentExecutions": 10,
        "maxSessionDuration": 3600000
      }
    },
    "security": {
      "isolation": "container",
      "networkIsolation": true,
      "readOnlyFileSystem": false,
      "allowedCommands": ["*"],
      "blockedCommands": ["rm -rf /", "sudo", "passwd"],
      "environmentVariableFiltering": true
    }
  }
}
```

### Environment Configuration

```json
{
  "environments": {
    "basic-shell": {
      "image": "ubuntu:22.04",
      "shell": "/bin/bash",
      "workingDirectory": "/workspace",
      "preInstalledPackages": ["curl", "wget", "git", "vim"],
      "environmentVariables": {
        "LANG": "en_US.UTF-8",
        "TERM": "xterm-256color"
      }
    },
    "python": {
      "image": "python:3.11-slim",
      "shell": "/bin/bash",
      "workingDirectory": "/workspace",
      "preInstalledPackages": ["pip", "numpy", "pandas", "requests"],
      "environmentVariables": {
        "PYTHONPATH": "/workspace",
        "PIP_CACHE_DIR": "/tmp/pip-cache"
      }
    },
    "nodejs": {
      "image": "node:18-alpine",
      "shell": "/bin/sh",
      "workingDirectory": "/workspace",
      "preInstalledPackages": ["npm", "yarn"],
      "environmentVariables": {
        "NODE_ENV": "development",
        "NPM_CONFIG_CACHE": "/tmp/npm-cache"
      }
    }
  }
}
```

## Environment-Specific Configurations

### Development Configuration

```json
{
  "terminalPool": {
    "pool": {
      "initialSize": 3,
      "maxSize": 10,
      "preWarmCount": 2,
      "idleTimeout": 600000
    },
    "resources": {
      "defaultLimits": {
        "cpu": "0.5",
        "memory": "256MB",
        "storage": "500MB"
      }
    },
    "security": {
      "isolation": "process",
      "strictSecurity": false,
      "allowHostAccess": true
    },
    "logging": {
      "level": "debug",
      "auditEnabled": false
    }
  }
}
```

### Production Configuration

```json
{
  "terminalPool": {
    "pool": {
      "initialSize": 20,
      "maxSize": 200,
      "preWarmCount": 15,
      "idleTimeout": 1800000,
      "healthCheckInterval": 30000
    },
    "resources": {
      "defaultLimits": {
        "cpu": "2",
        "memory": "1GB",
        "storage": "2GB"
      },
      "quotas": {
        "maxTerminalsPerAgent": 10,
        "maxConcurrentExecutions": 50
      }
    },
    "security": {
      "isolation": "container",
      "strictSecurity": true,
      "auditAllCommands": true,
      "encryptedStorage": true
    }
  }
}
```

## Configuration Validation Rules

### Required Fields
- `terminalPool.pool.maxSize` must be greater than `initialSize`
- `terminalPool.resources.defaultLimits` must specify valid resource values
- `terminalPool.security.isolation` must be one of: process, container, vm

### Pool Configuration
- `initialSize` must be between 1 and 1000
- `maxSize` must be between `initialSize` and 10000
- `preWarmCount` must be less than or equal to `initialSize`
- `idleTimeout` must be between 60 seconds and 24 hours
- `maxLifetime` must be greater than `idleTimeout`

### Resource Configuration
- `cpu` must be valid CPU specification (cores or millicores)
- `memory` must be valid memory specification (bytes, KB, MB, GB)
- `storage` must be valid storage specification
- `networkBandwidth` must be valid bandwidth specification

## Default Values and Required Fields

### Default Values
```rust
impl Default for TerminalPoolConfig {
    fn default() -> Self {
        Self {
            pool: PoolConfig {
                initial_size: 10,
                max_size: 100,
                pre_warm_count: 5,
                growth_strategy: GrowthStrategy::Linear,
                shrink_strategy: ShrinkStrategy::Gradual,
                idle_timeout: Duration::from_secs(1800), // 30 minutes
                max_lifetime: Duration::from_secs(7200), // 2 hours
                health_check_interval: Duration::from_secs(60),
            },
            resources: ResourceConfig {
                default_limits: ResourceLimits {
                    cpu: "1".to_string(),
                    memory: "512MB".to_string(),
                    storage: "1GB".to_string(),
                    network_bandwidth: Some("100Mbps".to_string()),
                },
                quotas: ResourceQuotas {
                    max_terminals_per_agent: 5,
                    max_concurrent_executions: 10,
                    max_session_duration: Duration::from_secs(3600),
                },
            },
            security: SecurityConfig {
                isolation: IsolationType::Container,
                network_isolation: true,
                read_only_file_system: false,
                allowed_commands: vec!["*".to_string()],
                blocked_commands: vec![
                    "rm -rf /".to_string(),
                    "sudo".to_string(),
                ],
                environment_variable_filtering: true,
            },
        }
    }
}
```

### Required Fields
- `terminalPool` (root object)
- `terminalPool.pool.maxSize`
- `terminalPool.resources.defaultLimits`

### Optional Fields with Defaults
- `pool.initialSize`: 10
- `pool.preWarmCount`: 5
- `resources.quotas`: Default quota limits
- `security.isolation`: container
- `logging`: Basic logging configuration

## Advanced Configuration Options

### Performance Tuning
```json
{
  "performance": {
    "allocation": {
      "strategy": "least-loaded",
      "affinityEnabled": true,
      "loadBalancing": true
    },
    "caching": {
      "environmentCaching": true,
      "snapshotCaching": true,
      "maxCacheSize": "10GB"
    },
    "optimization": {
      "copyOnWrite": true,
      "lazyLoading": true,
      "compressionEnabled": true
    }
  }
}
```

### Security Configuration
```json
{
  "security": {
    "authentication": {
      "required": true,
      "methods": ["api-key", "jwt", "mutual-tls"]
    },
    "authorization": {
      "rbacEnabled": true,
      "policyFile": "/config/terminal-policies.json"
    },
    "auditing": {
      "enabled": true,
      "auditAllCommands": true,
      "sensitiveDataRedaction": true,
      "logRetention": "90d"
    },
    "scanning": {
      "malwareDetection": true,
      "behaviorAnalysis": true,
      "networkMonitoring": true
    }
  }
}
```

### Monitoring Configuration
```json
{
  "monitoring": {
    "metrics": {
      "enabled": true,
      "exportInterval": "30s",
      "exporters": ["prometheus", "datadog"]
    },
    "healthChecks": {
      "enabled": true,
      "interval": "30s",
      "timeout": "5s",
      "failureThreshold": 3
    },
    "alerts": {
      "resourceUtilization": {
        "cpuThreshold": 0.8,
        "memoryThreshold": 0.85,
        "storageThreshold": 0.9
      },
      "poolHealth": {
        "availabilityThreshold": 0.1,
        "allocationFailureThreshold": 5
      }
    }
  }
}
```

### Networking Configuration
```json
{
  "networking": {
    "isolation": {
      "enabled": true,
      "allowedOutbound": ["80", "443", "22"],
      "blockedOutbound": ["25", "23"],
      "internalNetworkAccess": false
    },
    "proxy": {
      "enabled": false,
      "httpProxy": "${HTTP_PROXY}",
      "httpsProxy": "${HTTPS_PROXY}",
      "noProxy": ["localhost", "127.0.0.1"]
    },
    "dns": {
      "customServers": ["*******", "*******"],
      "searchDomains": ["company.internal"]
    }
  }
}
```

### Container Runtime Configuration
```json
{
  "containerRuntime": {
    "runtime": "docker",
    "registries": {
      "default": "docker.io",
      "private": {
        "url": "registry.company.com",
        "credentials": {
          "username": "${REGISTRY_USERNAME}",
          "password": "${REGISTRY_PASSWORD}"
        }
      }
    },
    "imagePolicy": {
      "allowedRepositories": ["ubuntu", "python", "node"],
      "securityScanning": true,
      "vulnerabilityThreshold": "medium"
    }
  }
}
```