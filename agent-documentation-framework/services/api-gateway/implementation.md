# API Gateway Service - Implementation Documentation

## Core Classes and Interfaces

### REST API Endpoints Interface

Based on the claude-code-flow implementation, the API Gateway provides comprehensive REST endpoints.

```typescript
interface APIEndpoints {
  // Session management
  'POST /api/sessions': {
    body: CreateSessionRequest;
    response: Session;
  };
  
  'GET /api/sessions/:id': {
    response: Session;
  };
  
  'DELETE /api/sessions/:id': {
    response: { success: boolean };
  };
  
  // File operations
  'POST /api/files/upload': {
    body: FormData;
    response: AttachedFile;
  };
  
  'GET /api/files/:id': {
    response: FileContent;
  };
  
  'DELETE /api/files/:id': {
    response: { success: boolean };
  };
  
  // System status
  'GET /api/status': {
    response: SystemStatus;
  };
  
  'GET /api/health': {
    response: HealthCheck;
  };
  
  // Configuration
  'GET /api/config': {
    response: SystemConfig;
  };
  
  'PUT /api/config': {
    body: Partial<SystemConfig>;
    response: SystemConfig;
  };
}
```

### Route Handlers Implementation

```typescript
interface RouteHandlers {
  // Serve web UI assets
  serveStaticAssets(req: Request, res: Response): void;
  
  // Handle file uploads
  handleFileUpload(req: Request, res: Response): Promise<void>;
  
  // Provide session information
  getSessionInfo(req: Request, res: Response): Promise<void>;
  
  // Health check endpoint
  healthCheck(req: Request, res: Response): void;
}
```

## API Gateway Core Implementation

### Main Gateway Class

```typescript
export class APIGateway {
  private app: Express;
  private server: Server;
  private routes: Map<string, RouteHandler>;
  private middleware: MiddlewareStack;
  private loadBalancer: LoadBalancer;
  
  constructor(private config: GatewayConfig) {
    this.app = express();
    this.routes = new Map();
    this.middleware = new MiddlewareStack();
    this.loadBalancer = new LoadBalancer(config.loadBalancing);
    this.setupMiddleware();
    this.setupRoutes();
  }
  
  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: this.config.security.csp,
      hsts: this.config.security.hsts
    }));
    
    // CORS handling
    this.app.use(cors({
      origin: this.config.cors.origins,
      credentials: true,
      optionsSuccessStatus: 200
    }));
    
    // Request logging
    this.app.use(morgan('combined', {
      stream: this.createLogStream()
    }));
    
    // Rate limiting
    this.app.use('/api', this.createRateLimiter());
    
    // Authentication
    this.app.use('/api', this.authenticateRequest.bind(this));
    
    // Request validation
    this.app.use('/api', this.validateRequest.bind(this));
  }
  
  private setupRoutes(): void {
    // Agent management endpoints
    this.app.post('/api/v1/agents', this.handleAgentCreation.bind(this));
    this.app.get('/api/v1/agents', this.handleAgentList.bind(this));
    this.app.get('/api/v1/agents/:id', this.handleAgentStatus.bind(this));
    this.app.delete('/api/v1/agents/:id', this.handleAgentTermination.bind(this));
    
    // Workflow endpoints
    this.app.post('/api/v1/workflows', this.handleWorkflowCreation.bind(this));
    this.app.get('/api/v1/workflows/:id', this.handleWorkflowStatus.bind(this));
    this.app.put('/api/v1/workflows/:id/pause', this.handleWorkflowPause.bind(this));
    this.app.put('/api/v1/workflows/:id/resume', this.handleWorkflowResume.bind(this));
    
    // Session endpoints
    this.app.post('/api/v1/sessions', this.handleSessionCreation.bind(this));
    this.app.get('/api/v1/sessions/:id', this.handleSessionInfo.bind(this));
    this.app.delete('/api/v1/sessions/:id', this.handleSessionTermination.bind(this));
    
    // Memory endpoints
    this.app.post('/api/v1/memory', this.handleMemoryStore.bind(this));
    this.app.get('/api/v1/memory/:key', this.handleMemoryGet.bind(this));
    this.app.post('/api/v1/memory/query', this.handleMemoryQuery.bind(this));
    
    // Health and monitoring
    this.app.get('/health', this.handleHealthCheck.bind(this));
    this.app.get('/health/ready', this.handleReadinessCheck.bind(this));
    this.app.get('/metrics', this.handleMetrics.bind(this));
    
    // Static assets
    this.app.use('/ui', express.static(this.config.ui.staticPath));
    
    // Error handling
    this.app.use(this.handleErrors.bind(this));
  }
  
  async start(): Promise<void> {
    this.server = this.app.listen(this.config.port, this.config.host, () => {
      console.log(`API Gateway listening on ${this.config.host}:${this.config.port}`);
    });
    
    // Setup graceful shutdown
    this.setupGracefulShutdown();
  }
}
```

### Authentication and Authorization

```typescript
export class AuthenticationManager {
  private jwtSecret: string;
  private authProviders: Map<string, AuthProvider>;
  
  constructor(config: AuthConfig) {
    this.jwtSecret = config.jwt.secret;
    this.authProviders = new Map();
    this.setupAuthProviders(config);
  }
  
  async authenticateRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = this.extractToken(req);
      
      if (!token) {
        if (this.isPublicEndpoint(req.path)) {
          return next();
        }
        return res.status(401).json({ error: 'Authentication required' });
      }
      
      const decoded = jwt.verify(token, this.jwtSecret) as JWTPayload;
      
      // Attach user info to request
      req.user = {
        id: decoded.sub,
        email: decoded.email,
        roles: decoded.roles || [],
        permissions: decoded.permissions || []
      };
      
      next();
    } catch (error) {
      res.status(401).json({ error: 'Invalid token' });
    }
  }
  
  async authorizeRequest(req: Request, res: Response, next: NextFunction): Promise<void> {
    const requiredPermission = this.getRequiredPermission(req.method, req.path);
    
    if (!requiredPermission) {
      return next();
    }
    
    if (!req.user?.permissions.includes(requiredPermission)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    
    next();
  }
  
  private extractToken(req: Request): string | null {
    const authHeader = req.headers.authorization;
    
    if (authHeader?.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    
    // Check for API key
    const apiKey = req.headers['x-api-key'] as string;
    if (apiKey) {
      return this.validateAPIKey(apiKey);
    }
    
    return null;
  }
}
```

### Load Balancing Implementation

```typescript
export class LoadBalancer {
  private services: Map<string, ServicePool>;
  private strategies: Map<string, LoadBalancingStrategy>;
  
  constructor(config: LoadBalancingConfig) {
    this.services = new Map();
    this.strategies = new Map();
    this.initializeStrategies();
    this.registerServices(config.services);
  }
  
  async routeRequest(serviceName: string, request: ProxyRequest): Promise<ProxyResponse> {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service ${serviceName} not found`);
    }
    
    const strategy = this.strategies.get(service.strategy);
    const instance = await strategy.selectInstance(service.instances);
    
    if (!instance) {
      throw new Error(`No healthy instances available for ${serviceName}`);
    }
    
    try {
      const response = await this.forwardRequest(instance, request);
      this.updateInstanceMetrics(instance, true, response.time);
      return response;
    } catch (error) {
      this.updateInstanceMetrics(instance, false, 0);
      throw error;
    }
  }
  
  private async forwardRequest(instance: ServiceInstance, request: ProxyRequest): Promise<ProxyResponse> {
    const startTime = Date.now();
    
    const response = await fetch(`${instance.url}${request.path}`, {
      method: request.method,
      headers: request.headers,
      body: request.body,
      timeout: instance.timeout
    });
    
    const endTime = Date.now();
    
    return {
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
      body: await response.text(),
      time: endTime - startTime
    };
  }
}
```

### WebSocket Integration

```typescript
export class WebSocketManager {
  private wss: WebSocketServer;
  private connections: Map<string, WebSocketConnection>;
  private eventEmitter: EventEmitter;
  
  constructor(server: Server, config: WebSocketConfig) {
    this.wss = new WebSocketServer({ server, path: '/ws' });
    this.connections = new Map();
    this.eventEmitter = new EventEmitter();
    this.setupWebSocketHandlers();
  }
  
  private setupWebSocketHandlers(): void {
    this.wss.on('connection', (ws: WebSocket, req: IncomingMessage) => {
      const connection = this.createConnection(ws, req);
      this.connections.set(connection.id, connection);
      
      ws.on('message', (data: Buffer) => {
        this.handleMessage(connection, data);
      });
      
      ws.on('close', () => {
        this.connections.delete(connection.id);
        this.handleDisconnection(connection);
      });
      
      ws.on('error', (error: Error) => {
        this.handleError(connection, error);
      });
    });
  }
  
  private async handleMessage(connection: WebSocketConnection, data: Buffer): Promise<void> {
    try {
      const message = JSON.parse(data.toString()) as WebSocketMessage;
      
      switch (message.type) {
        case 'subscribe':
          await this.handleSubscription(connection, message.data);
          break;
        case 'unsubscribe':
          await this.handleUnsubscription(connection, message.data);
          break;
        case 'request':
          await this.handleRequest(connection, message);
          break;
        default:
          this.sendError(connection, 'Unknown message type');
      }
    } catch (error) {
      this.sendError(connection, 'Invalid message format');
    }
  }
  
  async broadcast(topic: string, data: any): Promise<void> {
    const message = JSON.stringify({
      type: 'event',
      topic,
      data,
      timestamp: Date.now()
    });
    
    for (const [id, connection] of this.connections) {
      if (connection.subscriptions.has(topic) && connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.send(message);
      }
    }
  }
}
```

### Request Validation and Transformation

```typescript
export class RequestValidator {
  private schemas: Map<string, JSONSchema>;
  
  constructor() {
    this.schemas = new Map();
    this.loadSchemas();
  }
  
  validateRequest(req: Request): ValidationResult {
    const route = `${req.method} ${req.route?.path || req.path}`;
    const schema = this.schemas.get(route);
    
    if (!schema) {
      return { valid: true };
    }
    
    const errors: ValidationError[] = [];
    
    // Validate path parameters
    if (schema.params && !this.validateObject(req.params, schema.params)) {
      errors.push({ field: 'params', message: 'Invalid path parameters' });
    }
    
    // Validate query parameters
    if (schema.query && !this.validateObject(req.query, schema.query)) {
      errors.push({ field: 'query', message: 'Invalid query parameters' });
    }
    
    // Validate request body
    if (schema.body && !this.validateObject(req.body, schema.body)) {
      errors.push({ field: 'body', message: 'Invalid request body' });
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  transformResponse(data: any, route: string): any {
    const transformer = this.getResponseTransformer(route);
    return transformer ? transformer(data) : data;
  }
}
```

### Cache Management

```typescript
export class CacheManager {
  private cache: Map<string, CacheEntry>;
  private config: CacheConfig;
  
  constructor(config: CacheConfig) {
    this.cache = new Map();
    this.config = config;
    this.startCleanupTimer();
  }
  
  async get(key: string): Promise<any | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return null;
    }
    
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    return entry.data;
  }
  
  async set(key: string, data: any, ttl?: number): Promise<void> {
    const entry: CacheEntry = {
      data,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 0,
      ttl: ttl || this.config.defaultTTL,
      etag: this.generateETag(data)
    };
    
    this.cache.set(key, entry);
    
    // Evict old entries if cache is full
    if (this.cache.size > this.config.maxSize) {
      this.evictLeastRecentlyUsed();
    }
  }
  
  generateCacheKey(req: Request): string {
    const parts = [
      req.method,
      req.path,
      JSON.stringify(req.query),
      req.user?.id || 'anonymous'
    ];
    
    return crypto.createHash('md5').update(parts.join(':')).digest('hex');
  }
}
```

This implementation provides a comprehensive API Gateway with authentication, load balancing, WebSocket support, caching, and request validation for the RUST-SS system.