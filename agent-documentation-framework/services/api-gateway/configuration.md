# API Gateway Service - Configuration Guide

## Core Configuration Structure

```json
{
  "apiGateway": {
    "server": {
      "port": 8080,
      "host": "0.0.0.0",
      "maxConnections": 10000,
      "keepAliveTimeout": 60000,
      "requestTimeout": 30000,
      "bodyLimit": "10MB"
    },
    "authentication": {
      "jwt": {
        "secret": "${JWT_SECRET}",
        "algorithm": "HS256",
        "expirationTime": 3600,
        "issuer": "api-gateway",
        "audience": "claude-flow-clients"
      },
      "oauth": {
        "enabled": true,
        "providers": {
          "google": {
            "clientId": "${GOOGLE_CLIENT_ID}",
            "clientSecret": "${GOOGLE_CLIENT_SECRET}",
            "scope": ["email", "profile"]
          }
        }
      },
      "apiKeys": {
        "enabled": true,
        "header": "X-API-Key",
        "queryParam": "api_key"
      }
    },
    "routing": {
      "services": {
        "agent-management": {
          "path": "/api/v1/agents",
          "upstream": "http://agent-service.internal:3001",
          "timeout": 30000,
          "retries": 3
        },
        "workflow-engine": {
          "path": "/api/v1/workflows",
          "upstream": "http://workflow-service.internal:3002",
          "timeout": 60000,
          "retries": 2
        },
        "session-manager": {
          "path": "/api/v1/sessions",
          "upstream": "http://session-service.internal:3003",
          "timeout": 15000,
          "retries": 3
        }
      }
    },
    "loadBalancing": {
      "strategy": "round-robin",
      "healthCheck": {
        "enabled": true,
        "interval": 30000,
        "timeout": 5000,
        "path": "/health"
      }
    },
    "rateLimit": {
      "windowMs": 60000,
      "maxRequests": 1000,
      "skipSuccessfulRequests": false,
      "keyGenerator": "ip",
      "store": "redis"
    },
    "cors": {
      "enabled": true,
      "origins": ["https://app.claudeflow.com", "http://localhost:3000"],
      "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      "allowedHeaders": ["Content-Type", "Authorization", "X-API-Key"],
      "credentials": true
    },
    "cache": {
      "enabled": true,
      "defaultTTL": 300,
      "maxSize": 1000,
      "store": "redis"
    },
    "security": {
      "helmet": {
        "contentSecurityPolicy": {
          "directives": {
            "defaultSrc": ["'self'"],
            "scriptSrc": ["'self'", "'unsafe-inline'"],
            "styleSrc": ["'self'", "'unsafe-inline'"]
          }
        },
        "hsts": {
          "maxAge": 31536000,
          "includeSubDomains": true
        }
      }
    },
    "monitoring": {
      "metrics": {
        "enabled": true,
        "path": "/metrics",
        "labels": ["method", "route", "status_code"]
      },
      "logging": {
        "level": "info",
        "format": "json",
        "requestLogging": true
      }
    }
  }
}
```

## Environment-Specific Configurations

### Development
```json
{
  "environment": "development",
  "apiGateway": {
    "server": { "port": 3000 },
    "authentication": { "jwt": { "expirationTime": 86400 } },
    "cors": { "origins": ["*"] },
    "rateLimit": { "maxRequests": 10000 },
    "security": { "helmet": { "enabled": false } }
  }
}
```

### Production
```json
{
  "environment": "production",
  "apiGateway": {
    "server": { "port": 443, "ssl": true },
    "authentication": { "jwt": { "expirationTime": 900 } },
    "rateLimit": { "maxRequests": 100 },
    "security": { "helmet": { "enabled": true } },
    "monitoring": { "metrics": { "enabled": true } }
  }
}
```