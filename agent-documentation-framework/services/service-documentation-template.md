# Service Documentation Standard Template

## Template Structure

Every service MUST have exactly 5 documentation files:

### 1. CLAUDE.md (Main Documentation)
- Overview and purpose
- Key responsibilities (4-6 main areas)
- Important interfaces (APIs, endpoints)
- Service relationships (dependencies, consumers)
- Performance considerations
- Security features (if applicable)

### 2. configuration.md (Configuration Guide)
- Configuration structure (JSON schema)
- Environment-specific configs (dev, prod)
- Configuration validation rules
- Default values and required fields

### 3. data-flow.md (Data Flow Documentation)
- Data flow diagrams and descriptions
- Input/output specifications
- Data transformation processes
- Integration patterns

### 4. implementation.md (Implementation Details)
- Technical architecture
- Key algorithms and data structures
- Error handling patterns
- Testing strategies

### 5. patterns.md (Design Patterns)
- Design patterns used
- Architectural decisions
- Best practices
- Integration patterns with other services

## Content Standards

### Section Requirements
- **Overview**: 2-3 sentences explaining the service purpose
- **Key Responsibilities**: 4-6 bullet points with sub-items
- **Important Interfaces**: Specific API methods and endpoints
- **Service Relationships**: Clear dependencies and consumers
- **Performance Considerations**: Specific metrics and targets

### Writing Style
- Use active voice
- Be specific with metrics and numbers
- Include code examples in configuration files
- Use consistent terminology across all services

### File Naming
- All lowercase with hyphens for service directories
- Exact filenames: `CLAUDE.md`, `configuration.md`, `data-flow.md`, `implementation.md`, `patterns.md`

## Implementation Process

1. **Audit Current State**: Identify services with non-standard documentation
2. **Create Missing Files**: Generate missing documentation files for each service
3. **Standardize Content**: Ensure all CLAUDE.md files follow the same structure
4. **Remove Redundant Files**: Delete non-standard documentation files
5. **Validate Consistency**: Cross-check all services use identical structure

This template ensures consistent, comprehensive documentation across all 16 services while maintaining implementation-focused content for agents.