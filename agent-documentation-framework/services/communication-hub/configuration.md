# Communication Hub Service - Configuration Guide

## Configuration Structure

### Main Configuration Schema

```typescript
interface CommunicationHubConfig {
  messaging: MessagingConfig;
  protocols: ProtocolConfig;
  routing: RoutingConfig;
  qos: QoSConfig;
  security: SecurityConfig;
  monitoring: MonitoringConfig;
  performance: PerformanceConfig;
  clustering: ClusteringConfig;
}
```

## Messaging Configuration

### NATS Core Configuration

```json
{
  "messaging": {
    "nats": {
      "servers": [
        "nats://nats-01.internal:4222",
        "nats://nats-02.internal:4222",
        "nats://nats-03.internal:4222"
      ],
      "cluster": {
        "name": "claude-flow-cluster",
        "routes": [
          "nats-route://nats-01.internal:6222",
          "nats-route://nats-02.internal:6222",
          "nats-route://nats-03.internal:6222"
        ]
      },
      "jetstream": {
        "enabled": true,
        "domain": "claude-flow",
        "maxMemory": "1GB",
        "maxStorage": "10GB",
        "replicas": 3
      },
      "auth": {
        "token": "${NATS_TOKEN}",
        "timeout": 5000
      },
      "tls": {
        "enabled": true,
        "cert": "/etc/ssl/certs/nats-client.crt",
        "key": "/etc/ssl/private/nats-client.key",
        "ca": "/etc/ssl/certs/nats-ca.crt",
        "verify": true
      },
      "limits": {
        "maxConnections": 1000,
        "maxSubscriptions": 10000,
        "maxPayload": "1MB",
        "maxPending": 1000
      }
    }
  }
}
```

### Message Queue Configuration

```json
{
  "messaging": {
    "queues": {
      "priority": {
        "critical": {
          "maxSize": 1000,
          "timeout": 5000,
          "retryPolicy": "exponential",
          "deadLetterQueue": "dlq.critical"
        },
        "high": {
          "maxSize": 5000,
          "timeout": 10000,
          "retryPolicy": "linear",
          "deadLetterQueue": "dlq.high"
        },
        "normal": {
          "maxSize": 10000,
          "timeout": 30000,
          "retryPolicy": "fixed",
          "deadLetterQueue": "dlq.normal"
        },
        "low": {
          "maxSize": 20000,
          "timeout": 60000,
          "retryPolicy": "none",
          "deadLetterQueue": "dlq.low"
        }
      },
      "processing": {
        "batchSize": 100,
        "batchTimeout": 1000,
        "concurrency": 10,
        "backpressureThreshold": 0.8
      },
      "persistence": {
        "enabled": true,
        "storage": "jetstream",
        "retention": "workqueue",
        "maxAge": "7d",
        "maxBytes": "1GB"
      }
    }
  }
}
```

## Protocol Configuration

### Multi-Protocol Support

```json
{
  "protocols": {
    "websocket": {
      "enabled": true,
      "port": 8080,
      "path": "/ws",
      "maxConnections": 1000,
      "pingInterval": 30000,
      "pongTimeout": 5000,
      "messageTimeout": 60000,
      "compression": {
        "enabled": true,
        "threshold": 1024,
        "level": 6
      },
      "rateLimit": {
        "messagesPerSecond": 100,
        "burstSize": 200
      }
    },
    "grpc": {
      "enabled": true,
      "port": 9090,
      "maxReceiveSize": "4MB",
      "maxSendSize": "4MB",
      "keepalive": {
        "time": 60000,
        "timeout": 5000,
        "permitWithoutStream": true
      },
      "security": {
        "tls": true,
        "mtls": true,
        "allowInsecure": false
      },
      "services": [
        "AgentService",
        "WorkflowService",
        "StateService"
      ]
    },
    "http": {
      "enabled": true,
      "port": 8081,
      "cors": {
        "enabled": true,
        "origins": ["*"],
        "methods": ["GET", "POST", "PUT", "DELETE"],
        "headers": ["Content-Type", "Authorization"]
      },
      "rateLimit": {
        "requestsPerMinute": 1000,
        "burstSize": 100
      },
      "bodyLimit": "10MB"
    },
    "mcp": {
      "enabled": true,
      "mode": "stdio",
      "timeout": 30000,
      "capabilities": ["tools", "completion", "resources"],
      "tools": {
        "registry": "database",
        "validation": true,
        "timeout": 60000
      }
    }
  }
}
```

### Protocol Translation

```json
{
  "protocols": {
    "translation": {
      "enabled": true,
      "mappings": {
        "websocket-to-nats": {
          "enabled": true,
          "topicPrefix": "ws",
          "preserveMetadata": true,
          "compression": false
        },
        "grpc-to-nats": {
          "enabled": true,
          "topicPrefix": "grpc",
          "preserveMetadata": true,
          "streamingSupport": true
        },
        "http-to-nats": {
          "enabled": true,
          "topicPrefix": "http",
          "preserveHeaders": true,
          "asyncResponse": true
        }
      },
      "validation": {
        "enabled": true,
        "strict": false,
        "schemaValidation": true
      }
    }
  }
}
```

## Routing Configuration

### Dynamic Routing Rules

```json
{
  "routing": {
    "strategy": "content-based",
    "rules": [
      {
        "id": "agent-commands",
        "pattern": "agent.*.command.*",
        "targets": ["agent-management-service"],
        "loadBalancing": "round-robin",
        "priority": "high",
        "timeout": 10000
      },
      {
        "id": "workflow-events",
        "pattern": "workflow.*",
        "targets": ["workflow-engine"],
        "loadBalancing": "sticky-session",
        "priority": "normal",
        "timeout": 30000
      },
      {
        "id": "system-events",
        "pattern": "system.*",
        "targets": ["monitoring-service", "logging-service"],
        "loadBalancing": "broadcast",
        "priority": "critical",
        "timeout": 5000
      },
      {
        "id": "metrics-collection",
        "pattern": "metrics.*",
        "targets": ["metrics-aggregator"],
        "loadBalancing": "hash",
        "priority": "low",
        "timeout": 60000,
        "batching": {
          "enabled": true,
          "size": 100,
          "timeout": 5000
        }
      }
    ],
    "fallback": {
      "enabled": true,
      "strategy": "dead-letter-queue",
      "dlqTopic": "dlq.unrouted",
      "retryAttempts": 3
    }
  }
}
```

### Load Balancing Strategies

```json
{
  "routing": {
    "loadBalancing": {
      "roundRobin": {
        "enabled": true,
        "healthCheck": true,
        "failoverTime": 5000
      },
      "weightedRoundRobin": {
        "enabled": true,
        "weights": {
          "agent-management-01": 1.0,
          "agent-management-02": 0.8,
          "agent-management-03": 0.6
        },
        "adjustmentInterval": 60000
      },
      "leastConnections": {
        "enabled": true,
        "connectionTracking": true,
        "rebalanceInterval": 30000
      },
      "hash": {
        "enabled": true,
        "algorithm": "consistent",
        "keyExtractor": "message.userId",
        "virtualNodes": 150
      }
    }
  }
}
```

## Quality of Service Configuration

### Message Delivery Guarantees

```json
{
  "qos": {
    "deliveryGuarantees": {
      "atMostOnce": {
        "enabled": true,
        "topics": ["metrics.*", "logs.*"],
        "acknowledgment": false,
        "persistence": false
      },
      "atLeastOnce": {
        "enabled": true,
        "topics": ["agent.*", "workflow.*"],
        "acknowledgment": true,
        "persistence": true,
        "retryPolicy": {
          "maxRetries": 3,
          "backoffMultiplier": 2,
          "maxBackoff": 30000
        }
      },
      "exactlyOnce": {
        "enabled": true,
        "topics": ["system.critical.*", "financial.*"],
        "acknowledgment": true,
        "persistence": true,
        "deduplication": {
          "enabled": true,
          "window": 300000,
          "keyExtractor": "message.id"
        }
      }
    }
  }
}
```

### Rate Limiting Configuration

```json
{
  "qos": {
    "rateLimiting": {
      "global": {
        "messagesPerSecond": 10000,
        "burstSize": 20000,
        "algorithm": "token-bucket"
      },
      "perClient": {
        "messagesPerSecond": 100,
        "burstSize": 200,
        "algorithm": "sliding-window"
      },
      "perTopic": {
        "system.*": {
          "messagesPerSecond": 1000,
          "burstSize": 2000
        },
        "agent.*": {
          "messagesPerSecond": 500,
          "burstSize": 1000
        },
        "logs.*": {
          "messagesPerSecond": 2000,
          "burstSize": 4000
        }
      },
      "backpressure": {
        "enabled": true,
        "threshold": 0.8,
        "strategy": "drop-oldest",
        "notificationTopic": "system.backpressure"
      }
    }
  }
}
```

### Circuit Breaker Configuration

```json
{
  "qos": {
    "circuitBreaker": {
      "default": {
        "failureThreshold": 5,
        "timeout": 60000,
        "halfOpenSuccessThreshold": 3,
        "monitoringPeriod": 300000
      },
      "services": {
        "agent-management": {
          "failureThreshold": 10,
          "timeout": 30000,
          "halfOpenSuccessThreshold": 5
        },
        "workflow-engine": {
          "failureThreshold": 3,
          "timeout": 120000,
          "halfOpenSuccessThreshold": 2
        },
        "state-management": {
          "failureThreshold": 2,
          "timeout": 180000,
          "halfOpenSuccessThreshold": 1
        }
      }
    }
  }
}
```

## Security Configuration

### Authentication and Authorization

```json
{
  "security": {
    "authentication": {
      "methods": ["jwt", "mtls", "api-key"],
      "jwt": {
        "secretKey": "${JWT_SECRET}",
        "algorithm": "HS256",
        "expirationTime": 3600,
        "issuer": "communication-hub",
        "audience": "claude-flow-services"
      },
      "mtls": {
        "enabled": true,
        "clientCerts": "/etc/ssl/certs/clients/",
        "ca": "/etc/ssl/certs/ca.crt",
        "verify": true,
        "crl": "/etc/ssl/crl/ca.crl"
      },
      "apiKey": {
        "header": "X-API-Key",
        "encryption": "bcrypt",
        "rotation": {
          "enabled": true,
          "interval": "30d",
          "notification": true
        }
      }
    },
    "authorization": {
      "enabled": true,
      "model": "rbac",
      "policies": {
        "admin": {
          "topics": ["*"],
          "actions": ["publish", "subscribe", "admin"]
        },
        "agent": {
          "topics": ["agent.*", "workflow.*", "task.*"],
          "actions": ["publish", "subscribe"]
        },
        "monitor": {
          "topics": ["system.*", "metrics.*", "logs.*"],
          "actions": ["subscribe"]
        }
      },
      "topicACL": {
        "system.*": ["admin"],
        "agent.*": ["admin", "agent"],
        "workflow.*": ["admin", "agent"],
        "metrics.*": ["admin", "monitor"],
        "logs.*": ["admin", "monitor"]
      }
    }
  }
}
```

### Encryption Configuration

```json
{
  "security": {
    "encryption": {
      "inTransit": {
        "enabled": true,
        "tls": {
          "version": "1.3",
          "ciphers": [
            "TLS_AES_256_GCM_SHA384",
            "TLS_CHACHA20_POLY1305_SHA256"
          ],
          "cert": "/etc/ssl/certs/communication-hub.crt",
          "key": "/etc/ssl/private/communication-hub.key"
        }
      },
      "atRest": {
        "enabled": true,
        "algorithm": "AES-256-GCM",
        "keyManagement": {
          "provider": "aws-kms",
          "keyId": "${KMS_KEY_ID}",
          "rotationDays": 90
        }
      },
      "endToEnd": {
        "enabled": false,
        "algorithm": "X25519",
        "keyExchange": "ecdh"
      }
    }
  }
}
```

## Performance Configuration

### Connection Management

```json
{
  "performance": {
    "connections": {
      "pooling": {
        "enabled": true,
        "minConnections": 10,
        "maxConnections": 100,
        "idleTimeout": 300000,
        "maxLifetime": 3600000,
        "acquireTimeout": 10000
      },
      "keepalive": {
        "enabled": true,
        "interval": 60000,
        "timeout": 10000,
        "maxIdleTime": 300000
      },
      "multiplexing": {
        "enabled": true,
        "maxStreamsPerConnection": 100,
        "streamTimeout": 300000
      }
    }
  }
}
```

### Message Processing Optimization

```json
{
  "performance": {
    "processing": {
      "threading": {
        "ioThreads": 4,
        "workerThreads": 8,
        "cpuAffinity": true
      },
      "batching": {
        "enabled": true,
        "maxBatchSize": 100,
        "batchTimeout": 10,
        "dynamicBatching": true
      },
      "serialization": {
        "format": "protobuf",
        "compression": {
          "enabled": true,
          "algorithm": "lz4",
          "threshold": 1024
        },
        "pooling": {
          "enabled": true,
          "maxObjects": 1000
        }
      },
      "caching": {
        "enabled": true,
        "size": "256MB",
        "ttl": 300000,
        "algorithm": "lru"
      }
    }
  }
}
```

### Network Optimization

```json
{
  "performance": {
    "network": {
      "tcp": {
        "nodelay": true,
        "keepalive": true,
        "reuseAddr": true,
        "sendBufferSize": "64KB",
        "receiveBufferSize": "64KB"
      },
      "bandwidth": {
        "throttling": {
          "enabled": false,
          "maxBytesPerSecond": "100MB",
          "burstSize": "10MB"
        },
        "prioritization": {
          "enabled": true,
          "highPriorityBandwidth": "60%",
          "normalPriorityBandwidth": "30%",
          "lowPriorityBandwidth": "10%"
        }
      }
    }
  }
}
```

## Monitoring Configuration

### Metrics Collection

```json
{
  "monitoring": {
    "metrics": {
      "enabled": true,
      "interval": 15000,
      "exporters": [
        {
          "type": "prometheus",
          "endpoint": "/metrics",
          "port": 9092,
          "labels": {
            "service": "communication-hub",
            "version": "${APP_VERSION}",
            "environment": "${NODE_ENV}"
          }
        },
        {
          "type": "statsd",
          "host": "statsd.monitoring.internal",
          "port": 8125,
          "prefix": "claude-flow.communication-hub"
        }
      ],
      "custom": {
        "messageLatency": {
          "enabled": true,
          "buckets": [1, 5, 10, 25, 50, 100, 250, 500, 1000]
        },
        "connectionCount": {
          "enabled": true,
          "labels": ["protocol", "client_type"]
        },
        "topicMetrics": {
          "enabled": true,
          "topPatterns": ["agent.*", "workflow.*", "system.*"]
        }
      }
    }
  }
}
```

### Health Checks

```json
{
  "monitoring": {
    "healthChecks": {
      "enabled": true,
      "interval": 30000,
      "timeout": 5000,
      "endpoints": {
        "liveness": "/health/live",
        "readiness": "/health/ready"
      },
      "checks": [
        {
          "name": "nats-connection",
          "type": "connection",
          "target": "nats://nats-cluster.internal:4222",
          "timeout": 5000
        },
        {
          "name": "message-processing",
          "type": "functional",
          "test": "round-trip-message",
          "timeout": 10000
        },
        {
          "name": "memory-usage",
          "type": "resource",
          "threshold": 0.8,
          "metric": "memory"
        }
      ]
    }
  }
}
```

## Environment-Specific Configurations

### Development Environment

```json
{
  "environment": "development",
  "debug": true,
  "messaging": {
    "nats": {
      "servers": ["nats://localhost:4222"],
      "auth": { "enabled": false },
      "tls": { "enabled": false }
    }
  },
  "security": {
    "authentication": { "enabled": false },
    "encryption": { "inTransit": { "enabled": false } }
  },
  "monitoring": {
    "metrics": { "enabled": false },
    "healthChecks": { "interval": 60000 }
  }
}
```

### Production Environment

```json
{
  "environment": "production",
  "debug": false,
  "messaging": {
    "nats": {
      "cluster": { "replicas": 3 },
      "jetstream": { "enabled": true },
      "tls": { "enabled": true, "verify": true }
    }
  },
  "security": {
    "authentication": { "enabled": true },
    "authorization": { "enabled": true },
    "encryption": { 
      "inTransit": { "enabled": true },
      "atRest": { "enabled": true }
    }
  },
  "performance": {
    "connections": { "maxConnections": 1000 },
    "processing": { "workerThreads": 16 }
  },
  "monitoring": {
    "metrics": { "enabled": true },
    "healthChecks": { "enabled": true }
  }
}
```

This comprehensive configuration system enables fine-tuned control over communication patterns, performance optimization, and security requirements across different deployment environments.