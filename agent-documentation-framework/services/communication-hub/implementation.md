# Communication Hub Service - Implementation Documentation

## Core Traits and Structs

### EventEmitter Trait

The `EventEmitter` trait provides the foundation for event subscription and emission within the system:

```rust
// Example: Type-safe event emitter with async support
use async_trait::async_trait;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::any::Any;

#[async_trait]
pub trait EventEmitter: Send + Sync {
    // Event subscription
    async fn on<T>(&self, event: &str, listener: Arc<dyn EventListener<T>>) -> ListenerId
    where
        T: Send + Sync + 'static;
    
    async fn once<T>(&self, event: &str, listener: Arc<dyn EventListener<T>>) -> ListenerId
    where
        T: Send + Sync + 'static;
    
    async fn off(&self, event: &str, listener_id: Option<ListenerId>);
    
    // Event emission
    async fn emit<T>(&self, event: &str, data: T)
    where
        T: Send + Sync + Clone + 'static;
    
    // Event utilities
    async fn listener_count(&self, event: &str) -> usize;
    async fn event_names(&self) -> Vec<String>;
}

#[async_trait]
pub trait EventListener<T>: Send + Sync {
    async fn handle(&self, data: T);
}

pub type ListenerId = String;

/// Type-safe event emitter implementation
pub struct TypedEventEmitter {
    listeners: Arc<RwLock<HashMap<String, Vec<ListenerEntry>>>>,
}

struct ListenerEntry {
    id: ListenerId,
    listener: Arc<dyn Any + Send + Sync>,
    once: bool,
}

impl TypedEventEmitter {
    pub fn new() -> Self {
        Self {
            listeners: Arc::new(RwLock::new(HashMap::new())),
        }
    }
}

#[async_trait]
impl EventEmitter for TypedEventEmitter {
    async fn emit<T>(&self, event: &str, data: T)
    where
        T: Send + Sync + Clone + 'static,
    {
        let mut listeners = self.listeners.write().await;
        
        if let Some(entries) = listeners.get_mut(event) {
            let mut to_remove = Vec::new();
            
            for (idx, entry) in entries.iter().enumerate() {
                if let Some(listener) = entry.listener
                    .downcast_ref::<Arc<dyn EventListener<T>>>()
                {
                    listener.handle(data.clone()).await;
                    
                    if entry.once {
                        to_remove.push(idx);
                    }
                }
            }
            
            // Remove once listeners
            for idx in to_remove.into_iter().rev() {
                entries.remove(idx);
            }
        }
    }
}
```

### System Events

Defines predefined system-wide events and their associated data structures:

```rust
// Example: Strongly-typed system events
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum SystemEvent {
    // System events
    SystemStarted {
        timestamp: DateTime<Utc>,
    },
    SystemStopped {
        timestamp: DateTime<Utc>,
        reason: Option<String>,
    },
    SystemError {
        error: String,
        component: String,
        timestamp: DateTime<Utc>,
    },
    
    // Agent events
    AgentSpawned {
        agent: AgentInfo,
        timestamp: DateTime<Utc>,
    },
    AgentTerminated {
        agent_id: String,
        reason: Option<String>,
        timestamp: DateTime<Utc>,
    },
    AgentError {
        agent_id: String,
        error: String,
        timestamp: DateTime<Utc>,
    },
    
    // Task events
    TaskCreated {
        task: TaskInfo,
        timestamp: DateTime<Utc>,
    },
    TaskAssigned {
        task_id: String,
        agent_id: String,
        timestamp: DateTime<Utc>,
    },
    TaskStarted {
        task_id: String,
        agent_id: String,
        timestamp: DateTime<Utc>,
    },
    TaskCompleted {
        task_id: String,
        result: TaskResult,
        timestamp: DateTime<Utc>,
    },
    TaskFailed {
        task_id: String,
        error: String,
        timestamp: DateTime<Utc>,
    },
    TaskCancelled {
        task_id: String,
        reason: Option<String>,
        timestamp: DateTime<Utc>,
    },
    
    // Memory events
    MemoryStored {
        item: MemoryItem,
        timestamp: DateTime<Utc>,
    },
    MemoryUpdated {
        item_id: String,
        changes: serde_json::Value,
        timestamp: DateTime<Utc>,
    },
    MemoryDeleted {
        item_id: String,
        timestamp: DateTime<Utc>,
    },
    
    // Terminal events
    TerminalCreated {
        terminal: TerminalInfo,
        timestamp: DateTime<Utc>,
    },
    TerminalCommandExecuted {
        terminal_id: String,
        command: String,
        result: ExecutionResult,
        timestamp: DateTime<Utc>,
    },
    TerminalError {
        terminal_id: String,
        error: String,
        timestamp: DateTime<Utc>,
    },
}

/// Event bus with pattern matching
pub struct EventBus {
    emitter: Arc<TypedEventEmitter>,
    event_log: Arc<RwLock<Vec<SystemEvent>>>,
}

impl EventBus {
    pub fn new() -> Self {
        Self {
            emitter: Arc::new(TypedEventEmitter::new()),
            event_log: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    pub async fn publish(&self, event: SystemEvent) -> Result<(), EventError> {
        // Log event
        self.event_log.write().await.push(event.clone());
        
        // Emit to specific listeners based on event type
        match &event {
            SystemEvent::AgentSpawned { .. } => {
                self.emitter.emit("agent.spawned", event).await;
            }
            SystemEvent::TaskCompleted { .. } => {
                self.emitter.emit("task.completed", event).await;
            }
            // ... other event types
            _ => {}
        }
        
        // Always emit to global listeners
        self.emitter.emit("*", event).await;
        
        Ok(())
    }
    
    pub async fn subscribe<F>(&self, pattern: &str, handler: F) -> ListenerId
    where
        F: Fn(SystemEvent) + Send + Sync + 'static,
    {
        struct Handler<F> {
            func: F,
        }
        
        #[async_trait]
        impl<F> EventListener<SystemEvent> for Handler<F>
        where
            F: Fn(SystemEvent) + Send + Sync,
        {
            async fn handle(&self, event: SystemEvent) {
                (self.func)(event);
            }
        }
        
        let listener = Arc::new(Handler { func: handler });
        self.emitter.on(pattern, listener).await
    }
}
```

## MCP Integration Implementation

### MCPServer Trait

Provides comprehensive MCP (Model Context Protocol) server management capabilities:

```rust
// Example: MCP server trait with tool management
#[async_trait]
pub trait MCPServer: Send + Sync {
    // Server management
    async fn start(&self) -> Result<(), MCPError>;
    async fn stop(&self) -> Result<(), MCPError>;
    async fn restart(&self) -> Result<(), MCPError>;
    async fn get_status(&self) -> Result<MCPStatus, MCPError>;
    
    // Tool management
    async fn register_tool(&self, tool: Box<dyn Tool>) -> Result<(), MCPError>;
    async fn unregister_tool(&self, name: &str) -> Result<bool, MCPError>;
    async fn list_tools(&self) -> Result<Vec<ToolInfo>, MCPError>;
    async fn get_tool(&self, name: &str) -> Result<Option<Arc<dyn Tool>>, MCPError>;
    
    // Tool execution
    async fn execute_tool(
        &self,
        name: &str,
        input: serde_json::Value,
        options: Option<ToolOptions>,
    ) -> Result<ToolResult, MCPError>;
    
    async fn execute_tool_async(
        &self,
        name: &str,
        input: serde_json::Value,
    ) -> Result<String, MCPError>;
    
    async fn get_execution_result(&self, execution_id: &str) -> Result<ToolResult, MCPError>;
    async fn get_execution_status(&self, execution_id: &str) -> Result<ExecutionStatus, MCPError>;
    
    // Client management
    async fn list_clients(&self) -> Result<Vec<MCPClient>, MCPError>;
    async fn authenticate_client(&self, credentials: ClientCredentials) -> Result<String, MCPError>;
    async fn revoke_client(&self, client_id: &str) -> Result<bool, MCPError>;
}

#[async_trait]
pub trait Tool: Send + Sync {
    fn name(&self) -> &str;
    fn description(&self) -> &str;
    fn input_schema(&self) -> &JsonSchema;
    fn output_schema(&self) -> Option<&JsonSchema>;
    
    async fn execute(
        &self,
        input: serde_json::Value,
        context: Option<ToolContext>,
    ) -> Result<ToolResult, ToolError>;
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ToolResult {
    pub success: bool,
    pub data: Option<serde_json::Value>,
    pub error: Option<ToolError>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// MCP server implementation with agent integration
pub struct MCPServerImpl {
    tools: Arc<RwLock<HashMap<String, Arc<dyn Tool>>>>,
    sessions: Arc<RwLock<HashMap<String, MCPSession>>>,
    executions: Arc<RwLock<HashMap<String, ExecutionState>>>,
    mode: MCPMode,
    status: Arc<RwLock<MCPStatus>>,
}

#[derive(Clone, Debug)]
pub enum MCPMode {
    Stdio,
    Http { port: u16 },
    WebSocket { port: u16 },
}

impl MCPServerImpl {
    pub fn new(config: MCPConfig) -> Self {
        Self {
            tools: Arc::new(RwLock::new(HashMap::new())),
            sessions: Arc::new(RwLock::new(HashMap::new())),
            executions: Arc::new(RwLock::new(HashMap::new())),
            mode: config.mode,
            status: Arc::new(RwLock::new(MCPStatus::Stopped)),
        }
    }
}

#[async_trait]
impl MCPServer for MCPServerImpl {
    async fn register_tool(&self, tool: Box<dyn Tool>) -> Result<(), MCPError> {
        let name = tool.name().to_string();
        
        // Validate tool schema
        self.validate_tool_schema(&tool)?;
        
        let mut tools = self.tools.write().await;
        if tools.contains_key(&name) {
            return Err(MCPError::ToolAlreadyExists(name));
        }
        
        tools.insert(name, Arc::from(tool));
        Ok(())
    }
    
    async fn execute_tool(
        &self,
        name: &str,
        input: serde_json::Value,
        options: Option<ToolOptions>,
    ) -> Result<ToolResult, MCPError> {
        let tools = self.tools.read().await;
        let tool = tools.get(name)
            .ok_or_else(|| MCPError::ToolNotFound(name.to_string()))?
            .clone();
        drop(tools);
        
        // Validate input against schema
        self.validate_input(&tool, &input)?;
        
        // Create execution context
        let context = ToolContext {
            session_id: options.as_ref().and_then(|o| o.session_id.clone()),
            timeout: options.as_ref().and_then(|o| o.timeout),
            metadata: options.as_ref().map(|o| o.metadata.clone()).unwrap_or_default(),
        };
        
        // Execute with timeout
        let timeout = context.timeout.unwrap_or(Duration::seconds(30));
        
        match tokio::time::timeout(
            timeout.to_std().unwrap(),
            tool.execute(input, Some(context))
        ).await {
            Ok(Ok(result)) => Ok(result),
            Ok(Err(e)) => Err(MCPError::ToolExecutionFailed(e.to_string())),
            Err(_) => Err(MCPError::Timeout),
        }
    }
}
```

### MCP Protocol Handler Implementation

```rust
// Example: MCP protocol handler with request routing
pub struct MCPProtocolHandler {
    tools: Arc<RwLock<HashMap<String, Arc<dyn Tool>>>>,
    sessions: Arc<RwLock<HashMap<String, MCPSession>>>,
    mode: MCPMode,
    agent_selector: Arc<dyn AgentSelector>,
}

impl MCPProtocolHandler {
    pub fn new(config: MCPConfig) -> Self {
        Self {
            tools: Arc::new(RwLock::new(HashMap::new())),
            sessions: Arc::new(RwLock::new(HashMap::new())),
            mode: config.mode,
            agent_selector: Arc::new(DefaultAgentSelector::new()),
        }
    }
    
    pub async fn handle_request(&self, request: MCPRequest) -> Result<MCPResponse, MCPError> {
        let session = self.get_or_create_session(&request.session_id).await?;
        
        match request.method.as_str() {
            "initialize" => Ok(MCPResponse::Initialize {
                protocol_version: "1.0".to_string(),
                capabilities: self.get_capabilities(),
                tools: self.get_tool_descriptions().await?,
            }),
            
            "tools/list" => {
                let tools = self.tools.read().await;
                let tool_list = tools.values()
                    .map(|tool| self.format_tool(tool))
                    .collect();
                Ok(MCPResponse::ToolsList { tools: tool_list })
            }
            
            "tools/call" => self.execute_tool_request(request).await,
            
            "completion" => self.handle_completion(request).await,
            
            _ => Err(MCPError::UnsupportedMethod(request.method)),
        }
    }
    
    async fn execute_tool_request(&self, request: MCPRequest) -> Result<MCPResponse, MCPError> {
        let params = request.params
            .ok_or(MCPError::MissingParameters)?;
        
        let tool_name = params["name"].as_str()
            .ok_or(MCPError::InvalidParameter("name".to_string()))?;
        
        let tools = self.tools.read().await;
        let tool = tools.get(tool_name)
            .ok_or_else(|| MCPError::ToolNotFound(tool_name.to_string()))?
            .clone();
        drop(tools);
        
        // Validate input
        let arguments = params.get("arguments")
            .ok_or(MCPError::MissingArguments)?;
        
        self.validate_schema(arguments, tool.input_schema())?;
        
        // Select agent for tool execution
        let agent = self.agent_selector
            .select_agent_for_tool(&tool, arguments)
            .await?;
        
        // Execute on agent
        let result = self.execute_tool_on_agent(agent, tool, arguments.clone()).await?;
        
        Ok(MCPResponse::ToolResult {
            content: result,
            is_error: false,
        })
    }
    
    async fn handle_completion(&self, request: MCPRequest) -> Result<MCPResponse, MCPError> {
        let params = request.params
            .ok_or(MCPError::MissingParameters)?;
        
        // Get agents for completion
        let agents = self.agent_selector
            .get_agents_for_completion(&params)
            .await?;
        
        // Collect completions concurrently
        let completion_futures: Vec<_> = agents
            .into_iter()
            .map(|agent| self.get_agent_completion(agent, params.clone()))
            .collect();
        
        let completions = futures::future::try_join_all(completion_futures).await?;
        
        // Merge completions
        let merged = self.merge_completions(completions)?;
        
        Ok(MCPResponse::Completion {
            completions: merged,
        })
    }
    
    async fn execute_tool_on_agent(
        &self,
        agent: Arc<dyn Agent>,
        tool: Arc<dyn Tool>,
        arguments: serde_json::Value,
    ) -> Result<serde_json::Value, MCPError> {
        // Create agent-specific context
        let context = ToolContext {
            session_id: None,
            timeout: Some(Duration::seconds(60)),
            metadata: hashmap![
                "agent_id".to_string() => json!(agent.id()),
                "agent_type".to_string() => json!(agent.agent_type()),
            ],
        };
        
        // Execute through agent
        agent.execute_tool(tool.name(), arguments, context).await
            .map_err(|e| MCPError::AgentExecutionFailed(e.to_string()))
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct MCPRequest {
    pub id: String,
    pub method: String,
    pub params: Option<serde_json::Value>,
    pub session_id: String,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum MCPResponse {
    Initialize {
        protocol_version: String,
        capabilities: MCPCapabilities,
        tools: Vec<ToolDescription>,
    },
    ToolsList {
        tools: Vec<ToolDescription>,
    },
    ToolResult {
        content: serde_json::Value,
        is_error: bool,
    },
    Completion {
        completions: Vec<CompletionItem>,
    },
    Error {
        code: i32,
        message: String,
        data: Option<serde_json::Value>,
    },
}

/// Agent selector for MCP tool routing
#[async_trait]
pub trait AgentSelector: Send + Sync {
    async fn select_agent_for_tool(
        &self,
        tool: &Arc<dyn Tool>,
        arguments: &serde_json::Value,
    ) -> Result<Arc<dyn Agent>, MCPError>;
    
    async fn get_agents_for_completion(
        &self,
        params: &serde_json::Value,
    ) -> Result<Vec<Arc<dyn Agent>>, MCPError>;
}
```

## Communication Hub Core Implementation

### MessageRouter Implementation

```rust
// Example: Message router with async publish/subscribe pattern
use std::collections::{BinaryHeap, HashMap};
use std::cmp::Ordering;
use tokio::sync::{broadcast, Mutex};
use std::sync::Arc;

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum MessagePriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum QoSLevel {
    AtMostOnce,
    AtLeastOnce,
    ExactlyOnce,
}

#[derive(Clone)]
pub struct QueuedMessage {
    pub id: String,
    pub topic: String,
    pub payload: serde_json::Value,
    pub timestamp: i64,
    pub priority: MessagePriority,
    pub qos: QoSLevel,
    pub retry_count: u32,
    pub max_retries: u32,
    pub correlation_id: Option<String>,
}

impl Ord for QueuedMessage {
    fn cmp(&self, other: &Self) -> Ordering {
        // Higher priority messages come first
        other.priority.cmp(&self.priority)
            .then_with(|| self.timestamp.cmp(&other.timestamp))
    }
}

impl PartialOrd for QueuedMessage {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl PartialEq for QueuedMessage {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl Eq for QueuedMessage {}

pub struct MessageRouter {
    routes: Arc<RwLock<HashMap<String, Route>>>,
    subscriptions: Arc<RwLock<HashMap<String, Vec<Subscription>>>>,
    message_queue: Arc<Mutex<BinaryHeap<QueuedMessage>>>,
    rate_limiters: Arc<RwLock<HashMap<String, Arc<dyn RateLimiter>>>>,
}

#[derive(Clone)]
pub struct Route {
    pub pattern: String,
    pub priority: i32,
}

pub struct Subscription {
    pub id: String,
    pub pattern: String,
    pub handler: Arc<dyn MessageHandler>,
    pub options: SubscribeOptions,
    pub created_at: DateTime<Utc>,
    pub message_count: AtomicUsize,
}

#[async_trait]
pub trait MessageHandler: Send + Sync {
    async fn handle(&self, message: Message) -> Result<(), MessageError>;
}

#[derive(Clone, Debug)]
pub struct Message {
    pub topic: String,
    pub payload: serde_json::Value,
    pub metadata: MessageMetadata,
}

#[derive(Clone, Debug)]
pub struct MessageMetadata {
    pub message_id: String,
    pub timestamp: i64,
    pub correlation_id: Option<String>,
}

impl MessageRouter {
    pub fn new(config: RouterConfig) -> Self {
        Self {
            routes: Arc::new(RwLock::new(HashMap::new())),
            subscriptions: Arc::new(RwLock::new(HashMap::new())),
            message_queue: Arc::new(Mutex::new(BinaryHeap::new())),
            rate_limiters: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    pub async fn publish(
        &self,
        topic: &str,
        message: serde_json::Value,
        options: Option<PublishOptions>,
    ) -> Result<(), MessageError> {
        // Validate rate limits
        if let Some(ref opts) = options {
            if let Some(client_id) = &opts.client_id {
                self.check_rate_limit(topic, client_id).await?;
            }
        }
        
        // Create queued message
        let queued_message = QueuedMessage {
            id: Uuid::new_v4().to_string(),
            topic: topic.to_string(),
            payload: message,
            timestamp: Utc::now().timestamp(),
            priority: options.as_ref().map(|o| o.priority.clone()).unwrap_or(MessagePriority::Normal),
            qos: options.as_ref().map(|o| o.qos.clone()).unwrap_or(QoSLevel::AtLeastOnce),
            retry_count: 0,
            max_retries: options.as_ref().map(|o| o.max_retries).unwrap_or(3),
            correlation_id: options.and_then(|o| o.correlation_id),
        };
        
        // Add to queue
        let mut queue = self.message_queue.lock().await;
        let was_empty = queue.is_empty();
        queue.push(queued_message);
        drop(queue);
        
        // Process immediately if queue was empty
        if was_empty {
            tokio::spawn({
                let router = self.clone();
                async move {
                    let _ = router.process_message_queue().await;
                }
            });
        }
        
        Ok(())
    }
    
    pub async fn subscribe<F>(
        &self,
        pattern: &str,
        handler: F,
        options: Option<SubscribeOptions>,
    ) -> String 
    where
        F: Fn(Message) -> Result<(), MessageError> + Send + Sync + 'static,
    {
        let subscription = Subscription {
            id: Uuid::new_v4().to_string(),
            pattern: pattern.to_string(),
            handler: Arc::new(FunctionHandler::new(handler)),
            options: options.unwrap_or_default(),
            created_at: Utc::now(),
            message_count: AtomicUsize::new(0),
        };
        
        let id = subscription.id.clone();
        
        let mut subscriptions = self.subscriptions.write().await;
        subscriptions
            .entry(pattern.to_string())
            .or_insert_with(Vec::new)
            .push(subscription);
        
        id
    }
    
    pub async fn request(
        &self,
        topic: &str,
        payload: serde_json::Value,
        timeout: Option<Duration>,
    ) -> Result<serde_json::Value, MessageError> {
        let correlation_id = Uuid::new_v4().to_string();
        let reply_topic = format!("{}.reply.{}", topic, correlation_id);
        
        // Create a channel for the reply
        let (tx, mut rx) = tokio::sync::oneshot::channel();
        
        // Subscribe to reply topic
        let subscription_id = self.subscribe(
            &reply_topic,
            move |message| {
                let _ = tx.send(message.payload);
                Ok(())
            },
            None,
        ).await;
        
        // Send request
        self.publish(
            topic,
            json!({
                "payload": payload,
                "replyTo": reply_topic,
                "correlationId": correlation_id,
            }),
            Some(PublishOptions {
                correlation_id: Some(correlation_id),
                ..Default::default()
            }),
        ).await?;
        
        // Wait for reply with timeout
        let timeout_duration = timeout.unwrap_or(Duration::seconds(30));
        
        match tokio::time::timeout(timeout_duration.to_std().unwrap(), rx).await {
            Ok(Ok(response)) => {
                self.unsubscribe(&subscription_id).await;
                Ok(response)
            }
            Ok(Err(_)) => {
                self.unsubscribe(&subscription_id).await;
                Err(MessageError::ChannelClosed)
            }
            Err(_) => {
                self.unsubscribe(&subscription_id).await;
                Err(MessageError::Timeout)
            }
        }
    }
    
    async fn process_message_queue(&self) -> Result<(), MessageError> {
        loop {
            let mut queue = self.message_queue.lock().await;
            if let Some(message) = queue.pop() {
                drop(queue);
                self.route_message(message).await?;
            } else {
                break;
            }
        }
        Ok(())
    }
    
    async fn route_message(&self, message: QueuedMessage) -> Result<(), MessageError> {
        let routes = self.find_matching_routes(&message.topic).await;
        
        if routes.is_empty() {
            // Handle dead letter
            return self.handle_dead_letter(message).await;
        }
        
        // Apply routing strategy
        for route in routes {
            if let Err(e) = self.deliver_message(&message, &route).await {
                self.handle_delivery_error(&message, &route, e).await?;
            }
        }
        
        Ok(())
    }
    
    async fn deliver_message(
        &self,
        message: &QueuedMessage,
        route: &Route,
    ) -> Result<(), MessageError> {
        let subscriptions = self.subscriptions.read().await;
        if let Some(subs) = subscriptions.get(&route.pattern) {
            for subscription in subs {
                if self.should_deliver_to_subscription(message, subscription) {
                    let msg = Message {
                        topic: message.topic.clone(),
                        payload: message.payload.clone(),
                        metadata: MessageMetadata {
                            message_id: message.id.clone(),
                            timestamp: message.timestamp,
                            correlation_id: message.correlation_id.clone(),
                        },
                    };
                    
                    subscription.handler.handle(msg).await?;
                    subscription.message_count.fetch_add(1, Ordering::Relaxed);
                }
            }
        }
        Ok(())
    }
}

// Function handler wrapper for closures
struct FunctionHandler<F> {
    func: F,
}

impl<F> FunctionHandler<F> {
    fn new(func: F) -> Self {
        Self { func }
    }
}

#[async_trait]
impl<F> MessageHandler for FunctionHandler<F>
where
    F: Fn(Message) -> Result<(), MessageError> + Send + Sync,
{
    async fn handle(&self, message: Message) -> Result<(), MessageError> {
        (self.func)(message)
    }
}
```

### Protocol Bridge Implementation

```rust
// Example: Protocol bridge traits and implementations
#[async_trait]
pub trait ProtocolBridge: Send + Sync {
    fn name(&self) -> &str;
    fn supported_protocols(&self) -> &[&str];
    
    async fn translate_inbound(
        &self,
        message: serde_json::Value,
        from_protocol: &str,
    ) -> Result<InternalMessage, BridgeError>;
    
    async fn translate_outbound(
        &self,
        message: &InternalMessage,
        to_protocol: &str,
    ) -> Result<serde_json::Value, BridgeError>;
    
    fn validate_message(&self, message: &serde_json::Value, protocol: &str) -> bool;
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct InternalMessage {
    pub id: String,
    pub topic: String,
    pub payload: serde_json::Value,
    pub timestamp: i64,
    pub source: String,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// WebSocket bridge implementation
pub struct WebSocketBridge {
    connections: Arc<RwLock<HashMap<String, WebSocketConnection>>>,
    message_router: Arc<MessageRouter>,
}

impl WebSocketBridge {
    pub fn new(message_router: Arc<MessageRouter>) -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            message_router,
        }
    }
    
    pub async fn handle_connection(&self, connection: WebSocketConnection) {
        let conn_id = connection.id.clone();
        self.connections.write().await.insert(conn_id.clone(), connection.clone());
        
        // Handle incoming messages
        let bridge = self.clone();
        let router = self.message_router.clone();
        
        tokio::spawn(async move {
            while let Some(msg) = connection.receive().await {
                if let Ok(message) = serde_json::from_str::<serde_json::Value>(&msg) {
                    if let Ok(internal_msg) = bridge.translate_inbound(message, "websocket").await {
                        let _ = router.publish(
                            &internal_msg.topic,
                            internal_msg.payload,
                            Some(PublishOptions {
                                client_id: Some(connection.client_id.clone()),
                                correlation_id: internal_msg.metadata
                                    .get("correlationId")
                                    .and_then(|v| v.as_str())
                                    .map(String::from),
                                ..Default::default()
                            }),
                        ).await;
                    }
                }
            }
            
            // Clean up on disconnect
            bridge.connections.write().await.remove(&conn_id);
        });
    }
}

#[async_trait]
impl ProtocolBridge for WebSocketBridge {
    fn name(&self) -> &str {
        "websocket"
    }
    
    fn supported_protocols(&self) -> &[&str] {
        &["websocket", "ws", "wss"]
    }
    
    async fn translate_inbound(
        &self,
        message: serde_json::Value,
        _from_protocol: &str,
    ) -> Result<InternalMessage, BridgeError> {
        Ok(InternalMessage {
            id: Uuid::new_v4().to_string(),
            topic: message["topic"].as_str()
                .ok_or(BridgeError::MissingField("topic"))?
                .to_string(),
            payload: message["payload"].clone(),
            timestamp: Utc::now().timestamp(),
            source: "websocket".to_string(),
            metadata: {
                let mut meta = HashMap::new();
                if let Some(metadata) = message.get("metadata").and_then(|m| m.as_object()) {
                    for (k, v) in metadata {
                        meta.insert(k.clone(), v.clone());
                    }
                }
                if let Some(client_id) = message.get("clientId") {
                    meta.insert("clientId".to_string(), client_id.clone());
                }
                if let Some(conn_id) = message.get("connectionId") {
                    meta.insert("connectionId".to_string(), conn_id.clone());
                }
                meta
            },
        })
    }
    
    async fn translate_outbound(
        &self,
        message: &InternalMessage,
        _to_protocol: &str,
    ) -> Result<serde_json::Value, BridgeError> {
        Ok(json!({
            "type": "message",
            "id": message.id,
            "topic": message.topic,
            "payload": message.payload,
            "timestamp": message.timestamp,
            "metadata": message.metadata,
        }))
    }
    
    fn validate_message(&self, message: &serde_json::Value, _protocol: &str) -> bool {
        message.get("topic").is_some() && message.get("payload").is_some()
    }
}

/// gRPC bridge implementation
pub struct GRPCBridge {
    services: Arc<RwLock<HashMap<String, Arc<dyn GRPCService>>>>,
    message_router: Arc<MessageRouter>,
}

impl GRPCBridge {
    pub fn new(message_router: Arc<MessageRouter>) -> Self {
        Self {
            services: Arc::new(RwLock::new(HashMap::new())),
            message_router,
        }
    }
}

#[async_trait]
impl ProtocolBridge for GRPCBridge {
    fn name(&self) -> &str {
        "grpc"
    }
    
    fn supported_protocols(&self) -> &[&str] {
        &["grpc", "grpc-web"]
    }
    
    async fn translate_inbound(
        &self,
        message: serde_json::Value,
        _from_protocol: &str,
    ) -> Result<InternalMessage, BridgeError> {
        let service = message["service"].as_str()
            .ok_or(BridgeError::MissingField("service"))?;
        let method = message["method"].as_str()
            .ok_or(BridgeError::MissingField("method"))?;
        
        Ok(InternalMessage {
            id: Uuid::new_v4().to_string(),
            topic: format!("grpc.{}.{}", service, method),
            payload: message["request"].clone(),
            timestamp: Utc::now().timestamp(),
            source: "grpc".to_string(),
            metadata: {
                let mut meta = HashMap::new();
                meta.insert("service".to_string(), json!(service));
                meta.insert("method".to_string(), json!(method));
                if let Some(headers) = message.get("headers") {
                    meta.insert("headers".to_string(), headers.clone());
                }
                if let Some(correlation_id) = message.get("correlationId") {
                    meta.insert("correlationId".to_string(), correlation_id.clone());
                }
                meta
            },
        })
    }
    
    async fn translate_outbound(
        &self,
        message: &InternalMessage,
        _to_protocol: &str,
    ) -> Result<serde_json::Value, BridgeError> {
        Ok(json!({
            "response": message.payload,
            "status": message.metadata.get("status").unwrap_or(&json!("OK")),
            "headers": message.metadata.get("headers").unwrap_or(&json!({})),
            "trailers": message.metadata.get("trailers").unwrap_or(&json!({})),
        }))
    }
    
    fn validate_message(&self, message: &serde_json::Value, _protocol: &str) -> bool {
        message.get("service").is_some() 
            && message.get("method").is_some() 
            && message.get("request").is_some()
    }
}
```

### Quality of Service Implementation

```rust
// Example: QoS manager with rate limiting and priority handling
#[async_trait]
pub trait QoSManager: Send + Sync {
    async fn configure_qos(&self, topic: &str, config: QoSConfig) -> Result<(), QoSError>;
    async fn get_qos_metrics(&self, topic: &str) -> Result<QoSMetrics, QoSError>;
    async fn set_rate_limit(&self, client_id: &str, limits: RateLimitConfig) -> Result<(), QoSError>;
    async fn set_priority(&self, pattern: &str, priority: MessagePriority) -> Result<(), QoSError>;
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct QoSConfig {
    pub max_retries: u32,
    pub retry_delay: Duration,
    pub timeout: Duration,
    pub guaranteed_delivery: bool,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct QoSMetrics {
    pub messages_delivered: u64,
    pub messages_failed: u64,
    pub duplicates_detected: u64,
    pub average_latency: f64,
    pub p99_latency: f64,
    pub delivery_rate: f64,
}

#[derive(Clone, Debug)]
pub struct RateLimitConfig {
    pub max_requests: u32,
    pub refill_rate: u32,
    pub refill_period: Duration,
}

pub struct QoSManagerImpl {
    qos_configs: Arc<RwLock<HashMap<String, QoSConfig>>>,
    rate_limiters: Arc<RwLock<HashMap<String, Arc<TokenBucketRateLimiter>>>>,
    priority_rules: Arc<RwLock<HashMap<String, MessagePriority>>>,
    metrics: Arc<RwLock<HashMap<String, QoSMetrics>>>,
}

impl QoSManagerImpl {
    pub fn new() -> Self {
        Self {
            qos_configs: Arc::new(RwLock::new(HashMap::new())),
            rate_limiters: Arc::new(RwLock::new(HashMap::new())),
            priority_rules: Arc::new(RwLock::new(HashMap::new())),
            metrics: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    pub async fn check_rate_limit(&self, client_id: &str) -> Result<bool, QoSError> {
        let limiters = self.rate_limiters.read().await;
        
        if let Some(limiter) = limiters.get(client_id) {
            Ok(limiter.try_consume().await)
        } else {
            Ok(true) // No rate limit configured
        }
    }
    
    pub async fn determine_message_priority(&self, topic: &str) -> MessagePriority {
        let rules = self.priority_rules.read().await;
        
        // Check for exact match first
        if let Some(priority) = rules.get(topic) {
            return priority.clone();
        }
        
        // Check for pattern matches
        for (pattern, priority) in rules.iter() {
            if self.topic_matches(topic, pattern) {
                return priority.clone();
            }
        }
        
        MessagePriority::Normal
    }
    
    fn topic_matches(&self, topic: &str, pattern: &str) -> bool {
        // Simple wildcard matching
        if pattern == "*" {
            return true;
        }
        
        let pattern_parts: Vec<&str> = pattern.split('.').collect();
        let topic_parts: Vec<&str> = topic.split('.').collect();
        
        if pattern_parts.len() != topic_parts.len() {
            return false;
        }
        
        for (pattern_part, topic_part) in pattern_parts.iter().zip(topic_parts.iter()) {
            if pattern_part != &"*" && pattern_part != topic_part {
                return false;
            }
        }
        
        true
    }
}

#[async_trait]
impl QoSManager for QoSManagerImpl {
    async fn configure_qos(&self, topic: &str, config: QoSConfig) -> Result<(), QoSError> {
        self.qos_configs.write().await.insert(topic.to_string(), config);
        
        // Initialize metrics
        self.metrics.write().await.insert(topic.to_string(), QoSMetrics {
            messages_delivered: 0,
            messages_failed: 0,
            duplicates_detected: 0,
            average_latency: 0.0,
            p99_latency: 0.0,
            delivery_rate: 1.0,
        });
        
        Ok(())
    }
    
    async fn get_qos_metrics(&self, topic: &str) -> Result<QoSMetrics, QoSError> {
        self.metrics.read().await
            .get(topic)
            .cloned()
            .ok_or_else(|| QoSError::TopicNotFound(topic.to_string()))
    }
    
    async fn set_rate_limit(&self, client_id: &str, limits: RateLimitConfig) -> Result<(), QoSError> {
        let rate_limiter = Arc::new(TokenBucketRateLimiter::new(
            limits.max_requests as i64,
            limits.refill_rate as i64,
            limits.refill_period,
        ));
        
        self.rate_limiters.write().await.insert(client_id.to_string(), rate_limiter);
        Ok(())
    }
    
    async fn set_priority(&self, pattern: &str, priority: MessagePriority) -> Result<(), QoSError> {
        self.priority_rules.write().await.insert(pattern.to_string(), priority);
        Ok(())
    }
}

/// Token bucket rate limiter implementation
pub struct TokenBucketRateLimiter {
    capacity: i64,
    tokens: Arc<Mutex<i64>>,
    refill_rate: i64,
    refill_period: Duration,
    last_refill: Arc<Mutex<Instant>>,
}

impl TokenBucketRateLimiter {
    pub fn new(capacity: i64, refill_rate: i64, refill_period: Duration) -> Self {
        Self {
            capacity,
            tokens: Arc::new(Mutex::new(capacity)),
            refill_rate,
            refill_period,
            last_refill: Arc::new(Mutex::new(Instant::now())),
        }
    }
    
    pub async fn try_consume(&self) -> bool {
        let mut tokens = self.tokens.lock().await;
        let mut last_refill = self.last_refill.lock().await;
        
        // Refill tokens
        let now = Instant::now();
        let elapsed = now.duration_since(*last_refill);
        let periods_elapsed = elapsed.as_secs() / self.refill_period.num_seconds() as u64;
        
        if periods_elapsed > 0 {
            let tokens_to_add = (periods_elapsed as i64) * self.refill_rate;
            *tokens = (*tokens + tokens_to_add).min(self.capacity);
            *last_refill = now;
        }
        
        // Try to consume a token
        if *tokens > 0 {
            *tokens -= 1;
            true
        } else {
            false
        }
    }
}
```

### Circuit Breaker Implementation

```rust
// Example: Circuit breaker pattern for fault tolerance
#[derive(Clone, Copy, Debug, PartialEq)]
pub enum CircuitState {
    Closed,
    Open,
    HalfOpen,
}

pub struct CircuitBreaker {
    state: Arc<RwLock<CircuitState>>,
    failure_count: Arc<Mutex<u32>>,
    last_failure_time: Arc<Mutex<Option<Instant>>>,
    success_count: Arc<Mutex<u32>>,
    config: CircuitBreakerConfig,
}

#[derive(Clone)]
pub struct CircuitBreakerConfig {
    pub failure_threshold: u32,
    pub success_threshold: u32,
    pub timeout: Duration,
    pub half_open_max_calls: u32,
}

impl CircuitBreaker {
    pub fn new(config: CircuitBreakerConfig) -> Self {
        Self {
            state: Arc::new(RwLock::new(CircuitState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            last_failure_time: Arc::new(Mutex::new(None)),
            success_count: Arc::new(Mutex::new(0)),
            config,
        }
    }
    
    pub async fn execute<F, T, E>(&self, operation: F) -> Result<T, CircuitBreakerError<E>>
    where
        F: Future<Output = Result<T, E>>,
        E: std::error::Error,
    {
        let current_state = *self.state.read().await;
        
        match current_state {
            CircuitState::Open => {
                if self.should_attempt_reset().await {
                    *self.state.write().await = CircuitState::HalfOpen;
                } else {
                    return Err(CircuitBreakerError::Open);
                }
            }
            _ => {}
        }
        
        match operation.await {
            Ok(result) => {
                self.on_success().await;
                Ok(result)
            }
            Err(error) => {
                self.on_failure().await;
                Err(CircuitBreakerError::OperationFailed(error))
            }
        }
    }
    
    async fn on_success(&self) {
        let current_state = *self.state.read().await;
        
        if current_state == CircuitState::HalfOpen {
            let mut success_count = self.success_count.lock().await;
            *success_count += 1;
            
            if *success_count >= self.config.success_threshold {
                self.reset().await;
            }
        } else {
            self.reset().await;
        }
    }
    
    async fn on_failure(&self) {
        let mut failure_count = self.failure_count.lock().await;
        *failure_count += 1;
        
        *self.last_failure_time.lock().await = Some(Instant::now());
        
        if *failure_count >= self.config.failure_threshold {
            *self.state.write().await = CircuitState::Open;
        }
    }
    
    async fn should_attempt_reset(&self) -> bool {
        if let Some(last_failure) = *self.last_failure_time.lock().await {
            last_failure.elapsed() >= self.config.timeout.to_std().unwrap()
        } else {
            false
        }
    }
    
    async fn reset(&self) {
        *self.state.write().await = CircuitState::Closed;
        *self.failure_count.lock().await = 0;
        *self.success_count.lock().await = 0;
        *self.last_failure_time.lock().await = None;
    }
}

#[derive(Debug)]
pub enum CircuitBreakerError<E> {
    Open,
    OperationFailed(E),
}

impl<E: std::error::Error> std::fmt::Display for CircuitBreakerError<E> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CircuitBreakerError::Open => write!(f, "Circuit breaker is open"),
            CircuitBreakerError::OperationFailed(e) => write!(f, "Operation failed: {}", e),
        }
    }
}

impl<E: std::error::Error> std::error::Error for CircuitBreakerError<E> {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            CircuitBreakerError::OperationFailed(e) => Some(e),
            _ => None,
        }
    }
}
```

This implementation provides a robust foundation for inter-service communication, supporting multiple protocols, quality of service guarantees, and fault tolerance mechanisms essential for the RUST-SS system.