# Dependency Resolution - Tracking and Resolution

## Overview

Dependency resolution is a critical component of workflow orchestration in claude-code-flow, ensuring tasks execute in the correct order while preventing deadlocks and managing complex dependency graphs. This document provides comprehensive implementation patterns for dependency tracking, cycle detection, and resolution algorithms.

## Core Dependency Management

### Dependency Graph Implementation (TypeScript)
```typescript
import { DependencyGraph } from './coordination/index.ts';

class WorkflowDependencyManager {
  private graph: DependencyGraph;
  private logger: Logger;
  private eventBus: EventBus;

  constructor(logger: Logger) {
    this.graph = new DependencyGraph(logger);
    this.logger = logger;
    this.eventBus = new EventBus();
  }

  // Add tasks with dependencies to the graph
  addTaskWithDependencies(task: Task): void {
    this.graph.addTask(task);
    
    // Add dependency edges
    if (task.dependencies) {
      for (const depId of task.dependencies) {
        this.graph.addDependency(depId, task.id);
      }
    }

    // Emit task added event
    this.eventBus.emit('task:added', { taskId: task.id, dependencies: task.dependencies });
  }

  // Get tasks that are ready to execute (no pending dependencies)
  getReadyTasks(): Task[] {
    const readyTaskIds = this.graph.getReadyTasks();
    return readyTaskIds.map(id => this.getTaskById(id));
  }

  // Mark task as completed and get newly ready tasks
  markTaskCompleted(taskId: string): Task[] {
    const newlyReadyIds = this.graph.markCompleted(taskId);
    
    // Emit completion event
    this.eventBus.emit('task:completed', { taskId, newlyReady: newlyReadyIds });
    
    return newlyReadyIds.map(id => this.getTaskById(id));
  }

  // Detect dependency cycles
  detectCycles(): string[][] {
    const cycles = this.graph.detectCycles();
    
    if (cycles.length > 0) {
      this.logger.warn(`Dependency cycles detected: ${JSON.stringify(cycles)}`);
      this.eventBus.emit('dependency:cycle-detected', { cycles });
    }
    
    return cycles;
  }

  // Get topological order of tasks
  getExecutionOrder(): string[] {
    try {
      return this.graph.topologicalSort();
    } catch (error) {
      this.logger.error('Failed to generate topological sort:', error);
      throw new Error('Cannot determine task execution order due to dependency cycles');
    }
  }

  // Validate all dependencies are satisfiable
  validateDependencies(): ValidationResult {
    const cycles = this.detectCycles();
    const unresolvedDeps = this.findUnresolvedDependencies();
    
    return {
      isValid: cycles.length === 0 && unresolvedDeps.length === 0,
      cycles,
      unresolvedDependencies: unresolvedDeps,
      recommendations: this.generateRecommendations(cycles, unresolvedDeps)
    };
  }
}
```

## Simple Dependency Patterns

### Single Dependency (CLI)
```bash
# Single dependency - task will only start once dependency is complete
claude-flow task create implementation "Develop frontend components" \
  --dependencies "api-specification-complete" \
  --priority medium \
  --estimated-duration 8h \
  --assigned-agent "frontend-team"
```

### Multiple Dependencies (CLI)
```bash
# Multiple dependencies - ALL must be satisfied before task starts
claude-flow task create deployment "Deploy to production" \
  --dependencies "frontend-complete,backend-complete,testing-complete,security-audit-passed" \
  --priority critical \
  --approval-required \
  --assigned-agent "devops-team"
```

### Conditional Dependencies (CLI)
```bash
# Dependency with conditions - task proceeds only if dependency meets criteria
claude-flow task create integration "Integrate payment system" \
  --dependencies "payment-provider-approval:status=approved,security-review:score>=95" \
  --priority high \
  --timeout 7200 \
  --assigned-agent "integration-team"
```

## Complex Dependency Patterns

### Dependency Chain Creation (CLI)
```bash
# Create sequential dependency chain with unique IDs
claude-flow task create research "Market research" --id market-research-001
claude-flow task create analysis "Analyze research findings" --id analysis-001 \
  --dependencies "market-research-001"
claude-flow task create implementation "Develop product features" --id implementation-001 \
  --dependencies "analysis-001"
claude-flow task create coordination "Launch planning" --id launch-planning-001 \
  --dependencies "implementation-001"
```

### Parallel Task Dependencies (CLI)
```bash
# Create parallel development tasks that can run simultaneously
claude-flow task create implementation "Backend API development" --id backend-api
claude-flow task create implementation "Frontend UI development" --id frontend-ui
claude-flow task create implementation "Database schema design" --id database-schema

# Create integration task that depends on all parallel tasks
claude-flow task create integration "System integration testing" \
  --dependencies "backend-api,frontend-ui,database-schema" \
  --parallel-until-sync true \
  --priority high
```

## Advanced Dependency Configuration

### Conditional Dependencies (JSON)
```json
{
  "task": "deploy-to-production",
  "dependencies": [
    {
      "task": "security-audit",
      "condition": "security_enabled == true",
      "required_score": ">= 95",
      "timeout": 3600000,
      "retry_on_failure": true
    },
    {
      "task": "performance-testing",
      "condition": "load_testing_required == true",
      "required_metrics": {
        "response_time": "< 200ms",
        "throughput": "> 1000rps",
        "error_rate": "< 0.1%"
      },
      "validation_window": 1800000
    },
    {
      "task": "code-review",
      "condition": "review_threshold >= 95",
      "reviewers_required": 2,
      "approval_timeout": 86400000
    },
    {
      "task": "compliance-check",
      "condition": "environment == 'production'",
      "required_certifications": ["SOC2", "GDPR"],
      "mandatory": true
    }
  ],
  "fallback_strategy": "manual_approval",
  "escalation": {
    "timeout": 7200000,
    "notify": ["project-manager", "technical-lead"]
  }
}
```

### Task Dependency Specification in Workflows (JSON)
```json
{
  "tasks": [
    {
      "id": "task-a",
      "name": "Initial Task",
      "agentId": "agent-1",
      "estimatedDuration": "2h",
      "deliverables": ["specification.md"]
    },
    {
      "id": "task-b",
      "name": "Sequential Task",
      "agentId": "agent-2",
      "dependencies": ["task-a"],
      "dependencyConditions": {
        "task-a": {
          "status": "completed",
          "validation": "passed",
          "deliverables": "all-present"
        }
      },
      "estimatedDuration": "4h"
    },
    {
      "id": "task-c",
      "name": "Parallel Task",
      "agentId": "agent-3",
      "dependencies": ["task-a"],
      "parallel": true,
      "dependencyConditions": {
        "task-a": {
          "status": "completed",
          "minimum_quality": 85
        }
      },
      "estimatedDuration": "3h"
    },
    {
      "id": "task-d",
      "name": "Convergence Task",
      "agentId": "agent-4",
      "dependencies": ["task-b", "task-c"],
      "dependencyConditions": {
        "task-b": {
          "status": "completed",
          "output_validation": "schema_compliant"
        },
        "task-c": {
          "status": "completed",
          "performance_criteria": "met"
        }
      },
      "waitStrategy": "all-dependencies",
      "timeout": 14400000
    }
  ]
}
```

## Dependency Graph Algorithms

### Cycle Detection Algorithm (TypeScript)
```typescript
class CycleDetector {
  private graph: Map<string, string[]>;
  private visited: Set<string>;
  private recStack: Set<string>;

  constructor(dependencyGraph: Map<string, string[]>) {
    this.graph = dependencyGraph;
    this.visited = new Set();
    this.recStack = new Set();
  }

  detectCycles(): string[][] {
    const cycles: string[][] = [];
    
    for (const node of this.graph.keys()) {
      if (!this.visited.has(node)) {
        const cycle = this.detectCycleFromNode(node, []);
        if (cycle.length > 0) {
          cycles.push(cycle);
        }
      }
    }
    
    return cycles;
  }

  private detectCycleFromNode(node: string, path: string[]): string[] {
    this.visited.add(node);
    this.recStack.add(node);
    path.push(node);

    const neighbors = this.graph.get(node) || [];
    
    for (const neighbor of neighbors) {
      if (!this.visited.has(neighbor)) {
        const cycle = this.detectCycleFromNode(neighbor, [...path]);
        if (cycle.length > 0) {
          return cycle;
        }
      } else if (this.recStack.has(neighbor)) {
        // Found cycle - return the cycle path
        const cycleStart = path.indexOf(neighbor);
        return path.slice(cycleStart).concat([neighbor]);
      }
    }

    this.recStack.delete(node);
    return [];
  }
}
```

### Topological Sort Implementation (TypeScript)
```typescript
class TopologicalSorter {
  private graph: Map<string, string[]>;
  private inDegree: Map<string, number>;

  constructor(dependencyGraph: Map<string, string[]>) {
    this.graph = dependencyGraph;
    this.inDegree = this.calculateInDegrees();
  }

  sort(): string[] {
    const result: string[] = [];
    const queue: string[] = [];
    
    // Add nodes with no incoming edges to queue
    for (const [node, degree] of this.inDegree.entries()) {
      if (degree === 0) {
        queue.push(node);
      }
    }

    while (queue.length > 0) {
      const current = queue.shift()!;
      result.push(current);

      // Reduce in-degree for all neighbors
      const neighbors = this.graph.get(current) || [];
      for (const neighbor of neighbors) {
        const newDegree = this.inDegree.get(neighbor)! - 1;
        this.inDegree.set(neighbor, newDegree);
        
        if (newDegree === 0) {
          queue.push(neighbor);
        }
      }
    }

    // Check if all nodes were processed (no cycles)
    if (result.length !== this.graph.size) {
      throw new Error('Cannot perform topological sort: dependency cycles detected');
    }

    return result;
  }

  private calculateInDegrees(): Map<string, number> {
    const inDegree = new Map<string, number>();
    
    // Initialize all nodes with 0 in-degree
    for (const node of this.graph.keys()) {
      inDegree.set(node, 0);
    }

    // Calculate actual in-degrees
    for (const neighbors of this.graph.values()) {
      for (const neighbor of neighbors) {
        inDegree.set(neighbor, inDegree.get(neighbor)! + 1);
      }
    }

    return inDegree;
  }
}
```

## Dependency Validation and Analysis

### Dependency Graph Visualization (CLI)
```bash
# Generate visual representation of task dependencies
claude-flow task dependencies --graph --output dependency-graph.png --format svg

# Generate detailed dependency analysis
claude-flow task dependencies --analyze \
  --detect-cycles \
  --critical-path \
  --bottleneck-analysis \
  --optimization-suggestions \
  --export-report dependency-analysis.json

# Validate workflow dependencies before execution
claude-flow workflow validate workflow-123 \
  --check-dependencies \
  --detect-cycles \
  --verify-conditions \
  --simulate-execution
```

### Dependency Analysis Report (JSON)
```json
{
  "dependencyAnalysis": {
    "totalTasks": 15,
    "totalDependencies": 23,
    "maxDepth": 6,
    "criticalPath": [
      "requirements-analysis",
      "system-design", 
      "implementation",
      "testing",
      "deployment"
    ],
    "criticalPathDuration": "72h",
    "parallelizableGroups": [
      {
        "group": "implementation-phase",
        "tasks": ["backend-dev", "frontend-dev", "database-design"],
        "maxParallelism": 3,
        "estimatedSavings": "16h"
      }
    ],
    "potentialBottlenecks": [
      {
        "task": "security-review",
        "reason": "Required by 5 downstream tasks",
        "impact": "High - blocks 33% of workflow",
        "recommendation": "Consider splitting into multiple parallel reviews"
      }
    ],
    "cycles": [],
    "unresolvedDependencies": [],
    "recommendations": [
      "Task 'integration-testing' could start earlier if dependencies are restructured",
      "Consider adding conditional dependencies for optional security-scan",
      "Frontend and backend development can be fully parallelized"
    ]
  }
}
```

## Advanced Dependency Features

### Dynamic Dependency Resolution (TypeScript)
```typescript
class DynamicDependencyResolver {
  private dependencyGraph: DependencyGraph;
  private conditionEvaluator: ConditionEvaluator;
  private runtimeContext: RuntimeContext;

  constructor(graph: DependencyGraph, context: RuntimeContext) {
    this.dependencyGraph = graph;
    this.conditionEvaluator = new ConditionEvaluator();
    this.runtimeContext = context;
  }

  async resolveDependencies(taskId: string): Promise<ResolutionResult> {
    const task = await this.getTask(taskId);
    const dependencies = task.dependencies || [];
    
    const resolvedDependencies: ResolvedDependency[] = [];
    
    for (const dep of dependencies) {
      const resolution = await this.resolveSingleDependency(dep, task);
      resolvedDependencies.push(resolution);
    }

    return {
      taskId,
      dependencies: resolvedDependencies,
      isReady: resolvedDependencies.every(dep => dep.satisfied),
      blockers: resolvedDependencies.filter(dep => !dep.satisfied),
      estimatedReadyTime: this.calculateEstimatedReadyTime(resolvedDependencies)
    };
  }

  private async resolveSingleDependency(
    dependency: DependencySpec, 
    task: Task
  ): Promise<ResolvedDependency> {
    const depTask = await this.getTask(dependency.taskId);
    
    // Check basic completion status
    if (depTask.status !== 'completed') {
      return {
        dependencyId: dependency.taskId,
        satisfied: false,
        reason: `Task ${dependency.taskId} is not completed (status: ${depTask.status})`,
        estimatedCompletionTime: depTask.estimatedCompletionTime
      };
    }

    // Evaluate conditional dependencies
    if (dependency.conditions) {
      const conditionResult = await this.conditionEvaluator.evaluate(
        dependency.conditions,
        this.runtimeContext,
        depTask
      );
      
      if (!conditionResult.satisfied) {
        return {
          dependencyId: dependency.taskId,
          satisfied: false,
          reason: `Condition not met: ${conditionResult.failureReason}`,
          requirements: conditionResult.requirements
        };
      }
    }

    // Check quality gates and validation criteria
    if (dependency.validationCriteria) {
      const validationResult = await this.validateDependencyOutput(
        depTask,
        dependency.validationCriteria
      );
      
      if (!validationResult.passed) {
        return {
          dependencyId: dependency.taskId,
          satisfied: false,
          reason: `Validation failed: ${validationResult.failureReason}`,
          validationErrors: validationResult.errors
        };
      }
    }

    return {
      dependencyId: dependency.taskId,
      satisfied: true,
      reason: 'All dependency requirements satisfied',
      validatedAt: new Date()
    };
  }
}
```

### Conditional Dependency Evaluation (TypeScript)
```typescript
class ConditionEvaluator {
  private operators: Map<string, (a: any, b: any) => boolean>;

  constructor() {
    this.operators = new Map([
      ['==', (a, b) => a === b],
      ['!=', (a, b) => a !== b],
      ['>', (a, b) => Number(a) > Number(b)],
      ['>=', (a, b) => Number(a) >= Number(b)],
      ['<', (a, b) => Number(a) < Number(b)],
      ['<=', (a, b) => Number(a) <= Number(b)],
      ['contains', (a, b) => String(a).includes(String(b))],
      ['matches', (a, b) => new RegExp(b).test(String(a))]
    ]);
  }

  async evaluate(
    conditions: ConditionSpec[],
    context: RuntimeContext,
    dependencyTask: Task
  ): Promise<ConditionResult> {
    const results: ConditionEvaluation[] = [];

    for (const condition of conditions) {
      const result = await this.evaluateCondition(condition, context, dependencyTask);
      results.push(result);
    }

    const allSatisfied = results.every(r => r.satisfied);
    const failedConditions = results.filter(r => !r.satisfied);

    return {
      satisfied: allSatisfied,
      evaluations: results,
      failureReason: failedConditions.length > 0 
        ? `Failed conditions: ${failedConditions.map(c => c.description).join(', ')}`
        : undefined,
      requirements: failedConditions.map(c => c.requirement).filter(Boolean)
    };
  }

  private async evaluateCondition(
    condition: ConditionSpec,
    context: RuntimeContext,
    dependencyTask: Task
  ): Promise<ConditionEvaluation> {
    try {
      // Get the actual value from task or context
      const actualValue = await this.getValue(condition.field, dependencyTask, context);
      
      // Get the expected value (may include variable substitution)
      const expectedValue = await this.resolveValue(condition.value, context);
      
      // Apply the operator
      const operator = this.operators.get(condition.operator);
      if (!operator) {
        throw new Error(`Unknown operator: ${condition.operator}`);
      }

      const satisfied = operator(actualValue, expectedValue);

      return {
        condition: condition.description || `${condition.field} ${condition.operator} ${condition.value}`,
        satisfied,
        actualValue,
        expectedValue,
        operator: condition.operator,
        evaluatedAt: new Date()
      };
    } catch (error) {
      return {
        condition: condition.description || `${condition.field} ${condition.operator} ${condition.value}`,
        satisfied: false,
        error: error.message,
        evaluatedAt: new Date()
      };
    }
  }
}
```

## Dependency Monitoring and Troubleshooting

### Real-time Dependency Monitoring (CLI)
```bash
# Monitor dependency resolution in real-time
claude-flow task monitor-dependencies \
  --workflow-id workflow-456 \
  --real-time \
  --alert-on-blocks \
  --show-critical-path \
  --refresh-interval 5s

# Analyze dependency performance and bottlenecks
claude-flow task analyze-dependencies \
  --workflow-id workflow-456 \
  --metrics "resolution-time,blocking-duration,cycle-detection-time" \
  --identify-bottlenecks \
  --optimization-suggestions
```

### Dependency Troubleshooting (CLI)
```bash
# Debug specific dependency issues
claude-flow task debug-dependency \
  --task-id "integration-testing" \
  --dependency-id "backend-api-complete" \
  --show-conditions \
  --validate-criteria \
  --suggest-resolution

# Simulate dependency resolution
claude-flow task simulate-dependencies \
  --workflow-file workflow.json \
  --show-execution-order \
  --identify-parallel-opportunities \
  --estimate-completion-time
```

## Resource Dependencies

### Resource-Aware Dependency Management (JSON)
```json
{
  "resourceDependencies": {
    "task": "heavy-computation",
    "dependencies": [
      {
        "type": "task",
        "taskId": "data-preparation",
        "status": "completed"
      },
      {
        "type": "resource",
        "resourceId": "gpu-cluster",
        "availability": "available",
        "minimumCapacity": "8-gpus"
      },
      {
        "type": "resource",
        "resourceId": "high-memory-pool",
        "availability": "available",
        "minimumCapacity": "64GB"
      }
    ],
    "resourceTimeout": 1800000,
    "fallbackStrategy": "queue-and-wait"
  }
}
```

### Resource Dependency Resolution (CLI)
```bash
# Create task with resource dependencies
claude-flow task create implementation "Machine learning training" \
  --dependencies "data-preprocessing:status=completed" \
  --resource-dependencies "gpu-cluster:8-gpus,memory-pool:64GB" \
  --resource-timeout 30m \
  --fallback-strategy "queue-and-wait"

# Monitor resource dependency resolution
claude-flow task monitor-resource-dependencies \
  --task-id "ml-training-001" \
  --show-resource-queue \
  --estimated-wait-time \
  --alternative-resources
```

## Performance Optimization

### Dependency Resolution Optimization (TypeScript)
```typescript
class OptimizedDependencyResolver {
  private cache: Map<string, ResolutionResult>;
  private batchResolver: BatchResolver;
  private prefetcher: DependencyPrefetcher;

  constructor() {
    this.cache = new Map();
    this.batchResolver = new BatchResolver();
    this.prefetcher = new DependencyPrefetcher();
  }

  async resolveWithOptimization(taskIds: string[]): Promise<Map<string, ResolutionResult>> {
    // Use batch resolution for multiple tasks
    const results = await this.batchResolver.resolveBatch(taskIds);
    
    // Cache results for future use
    for (const [taskId, result] of results.entries()) {
      this.cache.set(taskId, result);
    }

    // Prefetch likely future dependencies
    await this.prefetcher.prefetchLikelyDependencies(taskIds);

    return results;
  }

  async resolveWithCaching(taskId: string): Promise<ResolutionResult> {
    // Check cache first
    const cached = this.cache.get(taskId);
    if (cached && this.isCacheValid(cached)) {
      return cached;
    }

    // Resolve and cache
    const result = await this.resolveFresh(taskId);
    this.cache.set(taskId, result);
    
    return result;
  }
}
```

## Best Practices for Dependency Resolution

1. **Design Minimal Dependencies**: Use only necessary dependencies to reduce complexity
2. **Avoid Circular Dependencies**: Design task flows to prevent cycles
3. **Use Conditional Dependencies**: Implement conditional logic for optional dependencies
4. **Implement Timeout Handling**: Set appropriate timeouts for dependency resolution
5. **Enable Parallel Execution**: Identify opportunities for concurrent task execution
6. **Monitor Critical Path**: Track and optimize the longest dependency chain
7. **Validate Dependencies Early**: Check dependency satisfaction before task execution
8. **Implement Fallback Strategies**: Provide alternatives when dependencies cannot be satisfied
9. **Cache Resolution Results**: Optimize performance with intelligent caching
10. **Provide Clear Error Messages**: Help users understand and resolve dependency issues

## Advanced Dependency Patterns

### Multi-Level Dependencies (JSON)
```json
{
  "complexDependencies": {
    "task": "system-integration",
    "dependencies": [
      {
        "level": 1,
        "tasks": ["database-ready", "api-deployed"]
      },
      {
        "level": 2, 
        "tasks": ["load-balancer-configured"],
        "condition": "level1_complete == true"
      },
      {
        "level": 3,
        "tasks": ["monitoring-setup"],
        "condition": "level2_complete == true && environment == 'production'"
      }
    ],
    "resolution": "sequential-levels",
    "timeout": 3600000
  }
}
```

This dependency resolution framework provides the foundation for reliable, efficient dependency management in RUST-SS implementations, ensuring proper task ordering and preventing execution issues.