# TodoWrite Coordination Patterns - Implementation Guide

## Overview

TodoWrite is the primary coordination mechanism in claude-code-flow for managing complex, multi-step tasks with dependencies, priorities, and assigned agents. This document provides comprehensive implementation patterns extracted directly from the claude-code-flow codebase.

## Core TodoWrite Structure

### Basic TodoWrite Pattern (JavaScript)
```javascript
TodoWrite([
  {
    id: "architecture_design",
    content: "Design system architecture and component interfaces",
    status: "pending",
    priority: "high",
    dependencies: [],
    estimatedTime: "60min",
    assignedAgent: "architect"
  },
  {
    id: "frontend_development", 
    content: "Develop React components and user interface",
    status: "pending",
    priority: "medium",
    dependencies: ["architecture_design"],
    estimatedTime: "120min",
    assignedAgent: "frontend_team"
  }
]);
```

## Todo Item Properties

### Required Properties
- **id**: Unique identifier for task tracking and dependency resolution
- **content**: Detailed description of the task (serves as agent prompt)
- **status**: Current state - "pending", "in_progress", "completed"
- **priority**: Task importance - "critical", "high", "medium", "low", "background"

### Optional Properties
- **dependencies**: Array of task IDs that must complete first
- **estimatedTime**: Time estimate in minutes (e.g., "60min", "2h")
- **assignedAgent**: Specific agent type or team responsible
- **deadline**: ISO timestamp for time-sensitive tasks
- **retryCount**: Number of retry attempts for failed tasks
- **timeout**: Maximum execution time before task cancellation

## Status Management Patterns

### Status Lifecycle
```
pending → in_progress → completed
    ↓         ↓
  failed  ← timeout
```

### Status Update Implementation (TypeScript)
```typescript
// Mark task as in progress before starting work
TodoWrite([
  {
    id: "current_task",
    content: "Process user authentication",
    status: "in_progress", // ONLY mark in_progress when actively working
    priority: "high",
    dependencies: [],
    estimatedTime: "30min",
    assignedAgent: "backend_developer"
  }
]);

// Complete task IMMEDIATELY after finishing
TodoWrite([
  {
    id: "current_task", 
    content: "Process user authentication",
    status: "completed", // Mark completed when fully accomplished
    priority: "high",
    dependencies: [],
    estimatedTime: "30min",
    assignedAgent: "backend_developer"
  }
]);
```

### Critical Status Rules
1. **ONLY mark in_progress** when actively working on a task
2. **LIMIT to ONE in_progress** task at any time
3. **Mark completed IMMEDIATELY** after finishing work
4. **NEVER mark completed** if tests are failing or implementation is partial

## Priority Management

### Priority Levels (CLI Integration)
```bash
# Critical priority (level 1) - immediate execution
claude-flow task create implementation "Fix security vulnerability CVE-2024-001" \
  --priority critical \
  --max-delay 2h \
  --interrupt-lower-priority

# High priority (level 2) - preferential scheduling  
claude-flow task create research "Customer requirements for Q1 release" \
  --priority high \
  --deadline "2024-12-31T23:59:59Z" \
  --escalation-policy "auto-escalate"

# Medium priority (level 3) - standard scheduling
claude-flow task create implementation "Develop user dashboard components" \
  --priority medium \
  --estimated-duration 4h

# Low priority (level 4) - scheduled during low-load
claude-flow task create documentation "Update API documentation" \
  --priority low \
  --execute-during "low-usage-hours"

# Background priority (level 5) - opportunistic execution
claude-flow task create maintenance "Clean up old log files and temporary data" \
  --priority background \
  --execute-when "system-idle" \
  --resource-limit "minimal"
```

## Dependency Management

### Single Dependency Pattern
```javascript
TodoWrite([
  {
    id: "api_specification",
    content: "Design REST API specification",
    status: "completed",
    priority: "high",
    dependencies: [],
    assignedAgent: "architect"
  },
  {
    id: "frontend_components",
    content: "Develop frontend components",
    status: "pending", 
    priority: "medium",
    dependencies: ["api_specification"], // Single dependency
    assignedAgent: "frontend_developer"
  }
]);
```

### Multiple Dependencies Pattern
```javascript
TodoWrite([
  {
    id: "production_deployment",
    content: "Deploy to production environment",
    status: "pending",
    priority: "critical",
    dependencies: [
      "frontend_complete",
      "backend_complete", 
      "testing_complete",
      "security_audit_passed"
    ], // Multiple dependencies - ALL must be satisfied
    assignedAgent: "devops_team"
  }
]);
```

### Conditional Dependencies (JSON Configuration)
```json
{
  "task": "deploy-to-production",
  "dependencies": [
    {
      "task": "security-audit",
      "condition": "security_enabled == true",
      "required_score": ">= 95"
    },
    {
      "task": "performance-testing", 
      "condition": "load_testing_required == true",
      "required_metrics": {
        "response_time": "< 200ms",
        "throughput": "> 1000rps"
      }
    },
    {
      "task": "code-review",
      "condition": "review_threshold >= 95",
      "reviewers_required": 2
    }
  ],
  "fallback_strategy": "manual_approval"
}
```

## Advanced Coordination Patterns

### Parallel Task Execution
```javascript
TodoWrite([
  {
    id: "backend_api_development",
    content: "Build REST API endpoints",
    status: "pending",
    priority: "high",
    dependencies: ["system_design"],
    estimatedTime: "8h",
    assignedAgent: "backend_team",
    parallelizable: true // Can run in parallel with other tasks
  },
  {
    id: "frontend_ui_development", 
    content: "Build React user interface",
    status: "pending",
    priority: "high", 
    dependencies: ["system_design"],
    estimatedTime: "8h",
    assignedAgent: "frontend_team",
    parallelizable: true // Can run in parallel with backend
  },
  {
    id: "integration_testing",
    content: "End-to-end integration tests",
    status: "pending",
    priority: "medium",
    dependencies: ["backend_api_development", "frontend_ui_development"],
    estimatedTime: "4h",
    assignedAgent: "qa_team"
  }
]);
```

### Task Breakdown with Subtasks
```javascript
TodoWrite([
  {
    id: "e_commerce_platform",
    content: "Build complete e-commerce platform",
    status: "pending",
    priority: "high",
    dependencies: [],
    subtasks: [
      {
        id: "user_service",
        content: "Implement user authentication and profiles",
        estimatedTime: "16h",
        assignedAgent: "backend_team_1"
      },
      {
        id: "product_service", 
        content: "Implement product catalog and inventory",
        estimatedTime: "20h",
        assignedAgent: "backend_team_2"
      },
      {
        id: "payment_service",
        content: "Implement payment processing integration", 
        estimatedTime: "12h",
        assignedAgent: "backend_team_3"
      }
    ],
    assignedAgent: "architecture_team"
  }
]);
```

## Error Handling and Recovery

### Failed Task Management
```javascript
TodoWrite([
  {
    id: "failed_task_example",
    content: "Process complex data analysis",
    status: "failed", // Mark as failed, not completed
    priority: "high",
    dependencies: [],
    estimatedTime: "60min",
    assignedAgent: "data_analyst",
    errorMessage: "Insufficient memory for dataset processing",
    retryCount: 2,
    maxRetries: 3,
    nextRetryAt: "2024-12-20T15:30:00Z"
  }
]);
```

### Timeout Handling
```bash
# Schedule task with timeout and retry logic
claude-flow task create analysis "Large dataset processing" \
  --timeout 3600 \
  --retry-policy "exponential-backoff" \
  --max-retries 3 \
  --require-resources "cpu:8-cores,memory:16GB"
```

## Integration with Memory System

### Memory-Driven Coordination
```javascript
// Store architecture decisions in memory first
const memoryStore = await Memory.store("system_architecture", {
  pattern: "microservices",
  gateway: "api-gateway", 
  database: "postgresql",
  caching: "redis"
});

// Reference memory in TodoWrite tasks
TodoWrite([
  {
    id: "implement_user_service",
    content: "Implement user service based on system_architecture in memory",
    status: "pending",
    priority: "high",
    dependencies: [],
    memoryReferences: ["system_architecture"], // Reference stored decisions
    assignedAgent: "backend_developer"
  }
]);
```

## Performance Optimization Patterns

### Batch Task Creation (TypeScript)
```typescript
// Create comprehensive task breakdown with batch optimization
const todos = await coordinator.createTaskTodos(
  "Build e-commerce platform",
  {
    strategy: 'development',
    batchOptimized: true,
    parallelExecution: true,
    memoryCoordination: true
  },
  context
);
```

### Resource-Aware Scheduling
```bash
# Schedule based on resource availability
claude-flow task create implementation "Heavy computation task" \
  --require-resources "cpu:8-cores,memory:16GB,gpu:1" \
  --schedule-when-available \
  --max-wait-time "4h"
```

## Monitoring and Analytics

### Task Progress Tracking
```bash
# Monitor task completion in real-time
claude-flow task monitor --all --dashboard --refresh 2s

# Analyze task performance over time
claude-flow task analytics performance \
  --time-range "30d" \
  --metrics "completion-time,resource-usage,success-rate" \
  --group-by "type,priority,agent"
```

### Workflow Visualization
```bash
# Generate dependency graph visualization
claude-flow task dependencies --graph --output dependency-graph.png --format svg
```

## Best Practices for Implementation

1. **Break Down Complex Tasks**: Use subtasks and dependencies for large initiatives
2. **Set Realistic Time Estimates**: Base estimates on historical data and complexity
3. **Assign Specific Agents**: Match task requirements to agent capabilities  
4. **Monitor Dependencies**: Ensure dependency completion before starting dependent tasks
5. **Handle Failures Gracefully**: Implement retry logic and error recovery strategies
6. **Use Memory Coordination**: Store shared context and decisions for task coordination
7. **Enable Parallel Execution**: Identify tasks that can run concurrently
8. **Track Progress Continuously**: Use real-time monitoring for long-running workflows

This TodoWrite pattern serves as the foundation for all complex task coordination in RUST-SS implementations, ensuring reliable and efficient multi-agent collaboration.