# CLAUDE-AGENTS.md - Agent Modes and Behaviors Configuration

## Overview

This consolidated configuration file contains all SPARC (Structured Pattern for Agent Role Coordination) mode definitions, behaviors, and capabilities. Each mode represents a specialized approach to problem-solving, enabling agents to focus on specific aspects of complex tasks.

## Table of Contents

1. [SPARC Mode Overview](#sparc-mode-overview)
2. [Mode Categories](#mode-categories)
3. [Core Development Modes](#core-development-modes)
   - [Orchestrator](#orchestrator-mode)
   - [Coder](#coder-mode)
   - [Architect](#architect-mode)
   - [TDD](#tdd-mode)
4. [Analysis & Quality Modes](#analysis--quality-modes)
   - [Researcher](#researcher-mode)
   - [Reviewer](#reviewer-mode)
   - [Analyzer](#analyzer-mode)
   - [Tester](#tester-mode)
5. [Specialized Modes](#specialized-modes)
   - [Debugger](#debugger-mode)
   - [Optimizer](#optimizer-mode)
   - [Documenter](#documenter-mode)
   - [Designer](#designer-mode)
6. [Advanced Coordination Modes](#advanced-coordination-modes)
   - [Innovator](#innovator-mode)
   - [Swarm-Coordinator](#swarm-coordinator-mode)
   - [Memory-Manager](#memory-manager-mode)
   - [Batch-Executor](#batch-executor-mode)
   - [Workflow-Manager](#workflow-manager-mode)
7. [Mode Interactions](#mode-interactions)
8. [Best Practices](#best-practices)

---

## SPARC Mode Overview

SPARC modes define specialized behaviors and capabilities for AI agents within the swarm system. Each mode represents a distinct approach to problem-solving, enabling agents to focus on specific aspects of complex tasks.

### Mode Selection Principles
1. **Task Alignment**: Choose modes that match the primary objective
2. **Complementary Skills**: Combine modes that enhance each other
3. **Resource Efficiency**: Deploy only necessary modes for the task
4. **Dynamic Adaptation**: Switch modes as requirements evolve

### Mode Base Interface
```rust
pub trait SparcMode: Send + Sync {
    fn name(&self) -> &'static str;
    fn description(&self) -> &'static str;
    async fn initialize(&mut self) -> Result<(), ModeError>;
    async fn execute(&mut self, task: Task) -> Result<TaskResult, ModeError>;
    async fn transition(&mut self, new_state: State) -> Result<(), ModeError>;
}
```

---

## Mode Categories

### Core Development Modes
- **Orchestrator**: High-level coordination and workflow management
- **Coder**: Implementation and code generation
- **Architect**: System design and structural planning
- **TDD**: Test-driven development approach

### Analysis & Quality Modes
- **Researcher**: Information gathering and analysis
- **Reviewer**: Code review and quality assessment
- **Analyzer**: Deep system analysis and insights
- **Tester**: Comprehensive testing and validation

### Specialized Modes
- **Debugger**: Problem identification and resolution
- **Optimizer**: Performance and efficiency improvements
- **Documenter**: Documentation generation and maintenance
- **Designer**: UI/UX and visual design

### Advanced Coordination Modes
- **Innovator**: Creative problem-solving and ideation
- **Swarm-Coordinator**: Multi-agent orchestration
- **Memory-Manager**: Distributed memory and state management
- **Batch-Executor**: High-volume task processing
- **Workflow-Manager**: Complex workflow orchestration

---

## Core Development Modes

### Orchestrator Mode

#### Purpose and Use Cases
The Orchestrator mode serves as the high-level coordinator for complex multi-agent operations. Orchestrators excel at breaking down large problems, delegating to specialized agents, and ensuring cohesive execution.

#### Primary Use Cases
- Large project coordination
- Multi-agent task distribution
- Workflow orchestration
- Resource management
- Strategic planning

#### Key Behaviors
- **Strategic Planning**: High-level problem decomposition
- **Task Delegation**: Assigning work to specialized agents
- **Progress Monitoring**: Tracking overall completion
- **Resource Orchestration**: Managing agent allocation
- **Conflict Resolution**: Handling inter-agent issues

#### State Machine
```rust
pub enum OrchestratorState {
    Planning { objectives: Vec<Objective> },
    Delegating { tasks: Vec<Task>, agents: Vec<Agent> },
    Monitoring { active_tasks: HashMap<TaskId, TaskStatus> },
    Coordinating { conflicts: Vec<Conflict> },
    Completing { results: Vec<TaskResult> },
}
```

#### Integration Points
- Works with all other modes
- Primary interface for external systems
- Manages shared memory access
- Coordinates with workflow engine

---

### Coder Mode

#### Purpose and Use Cases
The Coder mode specializes in implementation, code generation, and hands-on development. Coder agents transform designs and requirements into working code, focusing on clean implementation and best practices.

#### Primary Use Cases
- Feature implementation from specifications
- Code generation and scaffolding
- Refactoring and code improvement
- Bug fixes and patches
- API and integration development

#### Key Behaviors
- **Clean Code Writing**: Following best practices
- **Batch Processing**: Efficient multi-file operations
- **Pattern Application**: Using design patterns
- **Test Integration**: Writing tests alongside code
- **Incremental Development**: Small, focused changes

#### Implementation Architecture
```rust
pub trait CoderMode: SparcMode {
    async fn implement_feature(&mut self, spec: &FeatureSpec) -> Result<Implementation, ModeError>;
    async fn generate_code(&mut self, template: &CodeTemplate) -> Result<GeneratedCode, ModeError>;
    async fn refactor_code(&mut self, target: &RefactorTarget) -> Result<RefactoredCode, ModeError>;
    async fn batch_implement(&mut self, specs: Vec<FeatureSpec>) -> Result<Vec<Implementation>, ModeError>;
}

pub struct Implementation {
    pub files: Vec<CodeFile>,
    pub tests: Vec<TestFile>,
    pub documentation: Vec<DocFile>,
    pub dependencies: Vec<Dependency>,
}
```

#### Coding Techniques
- Design Patterns (Factory, Observer, Strategy)
- SOLID Principles
- Functional Programming concepts
- Async/Await for concurrency
- Batch Processing for efficiency
- Template-based code generation

---

### Architect Mode

#### Purpose and Use Cases
The Architect mode focuses on system design, architecture planning, and structural decisions. Architects create blueprints that guide implementation while ensuring scalability, maintainability, and performance.

#### Primary Use Cases
- System architecture design
- Component interface definition
- Technology selection
- Pattern recommendation
- Scalability planning

#### Key Behaviors
- **System Design**: Creating architectural blueprints
- **Pattern Selection**: Choosing appropriate patterns
- **Trade-off Analysis**: Evaluating design options
- **Interface Design**: Defining clean contracts
- **Documentation**: Creating design documents

#### Architecture Patterns
- Microservices architecture
- Event-driven systems
- Domain-driven design
- Hexagonal architecture
- CQRS and Event Sourcing
- Layered architecture

---

### TDD Mode

#### Purpose and Use Cases
The TDD (Test-Driven Development) mode specializes in test-first development approaches. TDD agents ensure code quality through comprehensive testing before implementation.

#### Primary Use Cases
- Test suite development
- Behavior-driven development
- Test coverage improvement
- Regression test creation
- Integration testing

#### Key Behaviors
- **Test-First Approach**: Writing tests before code
- **Red-Green-Refactor**: Following TDD cycle
- **Coverage Focus**: Ensuring comprehensive testing
- **Edge Case Identification**: Finding boundary conditions
- **Mock Creation**: Building test doubles

#### TDD Workflow
```rust
pub enum TDDState {
    WritingTest { spec: TestSpecification },
    RunningTest { test: Test, expected: TestResult },
    ImplementingCode { failing_test: Test },
    Refactoring { passing_tests: Vec<Test> },
    Complete { implementation: Implementation, tests: TestSuite },
}
```

---

## Analysis & Quality Modes

### Researcher Mode

#### Purpose and Use Cases
The Researcher mode specializes in information gathering, analysis, and knowledge synthesis. Researcher agents excel at exploring domains, discovering insights, and building comprehensive knowledge bases.

#### Primary Use Cases
- Technology research and evaluation
- Best practices discovery
- Domain knowledge exploration
- Competitive analysis
- Literature review and synthesis

#### Key Behaviors
- **Systematic Exploration**: Methodical information gathering
- **Critical Analysis**: Evaluating source credibility
- **Pattern Recognition**: Identifying trends and insights
- **Knowledge Synthesis**: Combining disparate information
- **Memory Integration**: Building on past research

#### Research Techniques
- Literature Review
- Comparative Analysis
- Technology Scouting
- Expert Synthesis
- Pattern Mining
- Knowledge Mapping

#### Memory Integration
```rust
pub struct MemoryAugmentedResearcher {
    base_researcher: Box<dyn ResearcherMode>,
    memory_store: Arc<MemoryStore>,
    knowledge_graph: KnowledgeGraph,
    learning_engine: LearningEngine,
}
```

---

### Reviewer Mode

#### Purpose and Use Cases
The Reviewer mode provides comprehensive code review, quality assessment, and improvement recommendations. Reviewers ensure code meets standards and best practices.

#### Primary Use Cases
- Code quality assessment
- Security review
- Performance analysis
- Best practice enforcement
- Architectural compliance

#### Key Behaviors
- **Code Analysis**: Deep inspection of implementation
- **Quality Metrics**: Measuring code health
- **Issue Identification**: Finding problems and anti-patterns
- **Recommendation Generation**: Suggesting improvements
- **Standards Enforcement**: Ensuring compliance

#### Review Categories
- Code quality and maintainability
- Security vulnerabilities
- Performance bottlenecks
- Architectural compliance
- Test coverage adequacy
- Documentation completeness

---

### Analyzer Mode

#### Purpose and Use Cases
The Analyzer mode performs deep system analysis, providing insights into complex codebases, performance characteristics, and architectural patterns.

#### Primary Use Cases
- Codebase analysis
- Performance profiling
- Dependency mapping
- Complexity assessment
- Technical debt identification

#### Key Behaviors
- **Static Analysis**: Code structure examination
- **Dynamic Analysis**: Runtime behavior study
- **Metric Collection**: Gathering quantitative data
- **Pattern Detection**: Identifying code patterns
- **Visualization**: Creating analysis reports

#### Analysis Capabilities
```rust
pub trait AnalyzerMode: SparcMode {
    async fn analyze_codebase(&mut self, path: &Path) -> Result<CodebaseAnalysis, ModeError>;
    async fn profile_performance(&mut self, target: &Target) -> Result<PerformanceProfile, ModeError>;
    async fn map_dependencies(&mut self, root: &Module) -> Result<DependencyGraph, ModeError>;
    async fn assess_complexity(&mut self, code: &Code) -> Result<ComplexityReport, ModeError>;
}
```

---

### Tester Mode

#### Purpose and Use Cases
The Tester mode specializes in comprehensive testing strategies, from unit tests to integration and end-to-end testing. Testers ensure system reliability and correctness.

#### Primary Use Cases
- Test strategy development
- Test implementation
- Test automation
- Performance testing
- Security testing

#### Key Behaviors
- **Test Planning**: Comprehensive test strategies
- **Test Implementation**: Writing effective tests
- **Coverage Analysis**: Ensuring completeness
- **Automation**: Building test pipelines
- **Reporting**: Clear test results

#### Testing Strategies
- Unit testing
- Integration testing
- End-to-end testing
- Performance testing
- Security testing
- Chaos testing

---

## Specialized Modes

### Debugger Mode

#### Purpose and Use Cases
The Debugger mode specializes in identifying and resolving issues in complex systems. Debuggers excel at root cause analysis and providing targeted fixes.

#### Primary Use Cases
- Bug investigation
- Root cause analysis
- Performance debugging
- Memory leak detection
- Concurrency issue resolution

#### Key Behaviors
- **Systematic Investigation**: Methodical problem analysis
- **Hypothesis Testing**: Validating assumptions
- **State Inspection**: Examining system state
- **Trace Analysis**: Following execution paths
- **Fix Validation**: Ensuring solutions work

#### Debugging Techniques
- Breakpoint debugging
- Log analysis
- Stack trace examination
- Memory profiling
- Performance profiling
- Distributed tracing

---

### Optimizer Mode

#### Purpose and Use Cases
The Optimizer mode focuses on improving system performance, efficiency, and resource utilization. Optimizers identify bottlenecks and implement improvements.

#### Primary Use Cases
- Performance optimization
- Memory optimization
- Algorithm improvement
- Resource utilization
- Cost reduction

#### Key Behaviors
- **Profiling**: Identifying performance hotspots
- **Optimization**: Implementing improvements
- **Benchmarking**: Measuring impact
- **Trade-off Analysis**: Balancing concerns
- **Continuous Improvement**: Iterative optimization

#### Optimization Strategies
```rust
pub enum OptimizationStrategy {
    TimeComplexity { target: BigO },
    SpaceComplexity { target: MemoryFootprint },
    Parallelization { threads: usize },
    Caching { strategy: CacheStrategy },
    AlgorithmReplacement { new_algorithm: Algorithm },
}
```

---

### Documenter Mode

#### Purpose and Use Cases
The Documenter mode creates comprehensive documentation for systems, APIs, and codebases. Documenters ensure knowledge is captured and accessible.

#### Primary Use Cases
- API documentation
- System documentation
- User guides
- Architecture documents
- Code comments

#### Key Behaviors
- **Content Generation**: Creating clear documentation
- **Structure Organization**: Logical document layout
- **Example Creation**: Providing usage examples
- **Diagram Generation**: Visual representations
- **Maintenance**: Keeping docs current

---

### Designer Mode

#### Purpose and Use Cases
The Designer mode focuses on user interface, user experience, and visual design aspects of systems.

#### Primary Use Cases
- UI component design
- UX flow planning
- Visual design systems
- Accessibility implementation
- Responsive design

#### Key Behaviors
- **Visual Design**: Creating appealing interfaces
- **UX Planning**: Designing user flows
- **Component Design**: Reusable UI elements
- **Accessibility**: Inclusive design
- **Prototype Creation**: Interactive mockups

---

## Advanced Coordination Modes

### Innovator Mode

#### Purpose and Use Cases
The Innovator mode specializes in creative problem-solving, generating novel solutions, and exploring unconventional approaches.

#### Primary Use Cases
- Creative solution generation
- Problem reframing
- Innovation workshops
- Brainstorming sessions
- Paradigm shifts

#### Key Behaviors
- **Creative Thinking**: Generating novel ideas
- **Problem Reframing**: New perspectives
- **Cross-pollination**: Combining concepts
- **Experimentation**: Testing new approaches
- **Risk Taking**: Exploring unknowns

---

### Swarm-Coordinator Mode

#### Purpose and Use Cases
The Swarm Coordinator mode manages large-scale multi-agent operations, orchestrating complex distributed workflows among dozens or hundreds of agents.

#### Primary Use Cases
- Managing large-scale agent deployments
- Coordinating distributed workflows
- Balancing workload across agents
- Handling inter-agent dependencies
- Optimizing swarm performance

#### Key Behaviors
- **Swarm Orchestration**: Managing agent lifecycles
- **Load Balancing**: Distributing work efficiently
- **Dependency Resolution**: Handling complex workflows
- **Performance Monitoring**: Tracking swarm health
- **Adaptive Scaling**: Adjusting resources dynamically

#### Coordination Strategies
- Task Decomposition
- Pipeline Orchestration
- MapReduce Patterns
- Event Choreography
- Consensus Protocols
- Load Shedding

#### Swarm Management
```rust
pub struct SwarmCoordinator {
    agent_pool: Vec<Agent>,
    task_queue: DistributedQueue<Task>,
    load_balancer: LoadBalancer,
    health_monitor: HealthMonitor,
    scaling_policy: ScalingPolicy,
}
```

---

### Memory-Manager Mode

#### Purpose and Use Cases
The Memory Manager mode handles distributed memory and state management across agent swarms, ensuring consistent and efficient information sharing.

#### Primary Use Cases
- Distributed state management
- Memory optimization
- Cache coordination
- Knowledge persistence
- Cross-agent memory sharing

#### Key Behaviors
- **Memory Coordination**: Managing distributed state
- **Cache Management**: Optimizing access patterns
- **Persistence**: Ensuring data durability
- **Synchronization**: Keeping memory consistent
- **Garbage Collection**: Cleaning unused data

---

### Batch-Executor Mode

#### Purpose and Use Cases
The Batch Executor mode specializes in high-volume task processing, optimizing throughput for repetitive operations.

#### Primary Use Cases
- Bulk data processing
- Mass file operations
- Parallel task execution
- Pipeline processing
- High-throughput workflows

#### Key Behaviors
- **Batch Optimization**: Maximizing throughput
- **Parallel Execution**: Concurrent processing
- **Pipeline Design**: Efficient workflows
- **Resource Pooling**: Reusing connections
- **Progress Tracking**: Monitoring completion

---

### Workflow-Manager Mode

#### Purpose and Use Cases
The Workflow Manager mode orchestrates complex multi-step processes with dependencies, conditions, and error handling.

#### Primary Use Cases
- Complex workflow orchestration
- Dependency management
- Conditional execution
- Error recovery
- Process automation

#### Key Behaviors
- **Workflow Design**: Creating process flows
- **Dependency Resolution**: Managing prerequisites
- **State Management**: Tracking progress
- **Error Handling**: Recovery strategies
- **Monitoring**: Process visibility

---

## Mode Interactions

### Communication Patterns
SPARC modes are designed to work together through:
- **Direct Communication**: Point-to-point messaging
- **Event Broadcasting**: Pub/sub patterns
- **Shared Memory**: Common state access
- **Task Handoffs**: Work delegation
- **Result Aggregation**: Combining outputs

### Common Interaction Patterns

#### Research → Architect → Coder → Tester
Classic development flow from research to implementation

#### Debugger ↔ Analyzer ↔ Optimizer
Performance improvement cycle

#### Orchestrator → Swarm-Coordinator → Batch-Executor
Large-scale task distribution

#### Memory-Manager ↔ All Modes
Persistent state sharing across all agents

---

## Best Practices

### Mode Selection
1. Start with an orchestrator for complex tasks
2. Use specialized modes for focused work
3. Combine complementary modes
4. Monitor mode effectiveness
5. Adapt mode selection as needed

### Mode Deployment
1. Initialize modes with clear objectives
2. Provide necessary context and resources
3. Monitor mode performance
4. Handle mode transitions gracefully
5. Clean up resources after completion

### Anti-Patterns to Avoid
1. **Mode Overload**: Deploying too many modes
2. **Poor Communication**: Inadequate inter-mode messaging
3. **Resource Waste**: Keeping idle modes active
4. **Rigid Workflows**: Not adapting to changes
5. **Memory Leaks**: Not cleaning up state

### Performance Optimization
1. Use batch operations where possible
2. Leverage parallel execution
3. Implement caching strategies
4. Monitor resource usage
5. Profile and optimize bottlenecks

---

## Summary

This CLAUDE-AGENTS.md file consolidates all 17 SPARC mode configurations including:
- Complete mode definitions and behaviors
- State machines and architectures
- Integration points and communication patterns
- Best practices and anti-patterns
- Implementation examples and techniques

These modes provide the specialized capabilities needed for comprehensive AI agent orchestration, enabling complex problem-solving through focused expertise and seamless collaboration.