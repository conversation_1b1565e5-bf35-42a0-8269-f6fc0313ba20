# Multi-Tenancy Implementation Guide

## Overview

This guide provides comprehensive implementation details for multi-tenant claude-code-flow deployments, including code examples, configuration patterns, and step-by-step setup instructions.

## Core Implementation Framework

### Tenant Management System

#### Rust Multi-Tenancy Implementation

```rust
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;
use std::collections::HashMap;
use async_trait::async_trait;
use anyhow::{Result, bail};

// Core tenant trait
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct TenantId(String);

impl TenantId {
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    pub fn as_str(&self) -> &str {
        &self.0
    }
}

// Tenant context for request processing
#[derive(Clone)]
pub struct TenantContext {
    pub tenant_id: TenantId,
    pub user_id: String,
    pub roles: Vec<String>,
    pub permissions: Vec<String>,
    pub metadata: HashMap<String, String>,
}

// Trait for tenant-aware components
#[async_trait]
pub trait TenantAware: Send + Sync {
    async fn get_tenant_id(&self) -> TenantId;
    async fn validate_tenant_access(&self, tenant_id: &TenantId) -> Result<bool>;
    async fn switch_tenant_context(&mut self, tenant_id: TenantId) -> Result<()>;
}

// Tenant isolation manager
pub struct TenantIsolationManager {
    tenants: Arc<RwLock<HashMap<TenantId, TenantInstance>>>,
    config: TenantConfig,
}

pub struct TenantInstance {
    pub id: TenantId,
    pub status: TenantStatus,
    pub resources: TenantResources,
    pub security: TenantSecurityConfig,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Clone)]
pub enum TenantStatus {
    Active,
    Suspended,
    Deleted,
    Provisioning,
}

pub struct TenantResources {
    pub memory_quota: usize,
    pub storage_quota: usize,
    pub cpu_shares: u32,
    pub network_bandwidth: u64,
    pub max_agents: u32,
    pub max_concurrent_tasks: u32,
}

pub struct TenantSecurityConfig {
    pub encryption_key_id: String,
    pub network_isolation: NetworkIsolation,
    pub storage_isolation: StorageIsolation,
    pub process_isolation: ProcessIsolation,
}

impl TenantIsolationManager {
    pub fn new(config: TenantConfig) -> Self {
        Self {
            tenants: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    pub async fn create_tenant(&self, config: TenantCreationConfig) -> Result<TenantId> {
        let tenant_id = TenantId::new(Uuid::new_v4().to_string());
        
        // Create isolated resources
        let resources = self.allocate_tenant_resources(&config).await?;
        
        // Set up security boundaries
        let security = self.setup_tenant_security(&tenant_id).await?;
        
        let instance = TenantInstance {
            id: tenant_id.clone(),
            status: TenantStatus::Provisioning,
            resources,
            security,
            created_at: chrono::Utc::now(),
        };
        
        self.tenants.write().await.insert(tenant_id.clone(), instance);
        
        // Initialize tenant namespace
        self.initialize_tenant_namespace(&tenant_id).await?;
        
        Ok(tenant_id)
    }

    async fn allocate_tenant_resources(&self, config: &TenantCreationConfig) -> Result<TenantResources> {
        Ok(TenantResources {
            memory_quota: config.memory_quota,
            storage_quota: config.storage_quota,
            cpu_shares: config.cpu_shares,
            network_bandwidth: config.network_bandwidth,
            max_agents: config.max_agents,
            max_concurrent_tasks: config.max_concurrent_tasks,
        })
    }

    async fn setup_tenant_security(&self, tenant_id: &TenantId) -> Result<TenantSecurityConfig> {
        // Generate tenant-specific encryption key
        let encryption_key_id = format!("tenant-{}-key-{}", tenant_id.as_str(), Uuid::new_v4());
        
        Ok(TenantSecurityConfig {
            encryption_key_id,
            network_isolation: NetworkIsolation::Strict,
            storage_isolation: StorageIsolation::Physical,
            process_isolation: ProcessIsolation::Container,
        })
    }
}
```

### Multi-Tenant Instance Management

#### TypeScript Implementation

```typescript
// Multi-tenant configuration pattern from claude-code-flow
class MultiTenantClaudeFlow {
  private instances = new Map<string, ClaudeFlow>();

  async getTenantInstance(tenantId: string): Promise<ClaudeFlow> {
    if (!this.instances.has(tenantId)) {
      const instance = new ClaudeFlow({
        memory: {
          namespaces: {
            enabled: true,
            defaultNamespace: tenantId,
            strictIsolation: true
          }
        },
        security: {
          isolation: {
            tenantId: tenantId,
            resourceIsolation: true
          }
        }
      });
      
      await instance.start();
      this.instances.set(tenantId, instance);
    }

    return this.instances.get(tenantId)!;
  }

  async executeForTenant(tenantId: string, task: any) {
    const instance = await this.getTenantInstance(tenantId);
    return await instance.executeTask(task);
  }
}
```

### Namespace-Based Isolation Implementation

```typescript
// Namespace isolation configuration from claude-code-flow
const namespaceConfig = {
  enabled: true,
  defaultNamespace: 'tenant-{tenant-id}',
  permissions: {
    'tenant-alpha': {
      read: ['agent-alpha-1', 'agent-alpha-2'],
      write: ['agent-alpha-1'],
      admin: ['admin-alpha'],
      public: false
    },
    'tenant-beta': {
      read: ['agent-beta-1', 'agent-beta-2'],
      write: ['agent-beta-1', 'agent-beta-2'],
      admin: ['admin-beta'],
      public: false
    }
  },
  quotas: {
    'tenant-alpha': {
      maxItems: 10000,
      maxStorage: 100 * 1024 * 1024, // 100MB
      dailyWrites: 1000
    },
    'tenant-beta': {
      maxItems: 5000,
      maxStorage: 50 * 1024 * 1024,  // 50MB
      dailyWrites: 500
    }
  },
  enforcePermissions: true,
  enforceQuotas: true,
  strictIsolation: true,        // Prevent cross-namespace access
  allowGlobalSearch: false      // Disable cross-tenant search
};
```

## Resource Isolation Implementation

### Resource Isolation with cgroups

```rust
use nix::sys::resource::{Resource, setrlimit};
use std::fs;
use std::path::Path;

pub struct ResourceIsolator {
    cgroup_root: String,
}

impl ResourceIsolator {
    pub fn new() -> Self {
        Self {
            cgroup_root: "/sys/fs/cgroup".to_string(),
        }
    }

    pub async fn create_tenant_cgroup(&self, tenant_id: &TenantId) -> Result<()> {
        let tenant_cgroup = format!("{}/claude-flow/{}", self.cgroup_root, tenant_id.as_str());
        
        // Create cgroup v2 hierarchy
        fs::create_dir_all(&tenant_cgroup)?;
        
        // Set memory limits
        self.set_memory_limit(&tenant_cgroup, 1024 * 1024 * 1024)?; // 1GB
        
        // Set CPU limits
        self.set_cpu_limit(&tenant_cgroup, 50000)?; // 50% of one CPU
        
        // Set I/O limits
        self.set_io_limit(&tenant_cgroup, "8:0", 10 * 1024 * 1024)?; // 10MB/s
        
        Ok(())
    }

    fn set_memory_limit(&self, cgroup_path: &str, limit_bytes: u64) -> Result<()> {
        let memory_max = format!("{}/memory.max", cgroup_path);
        fs::write(memory_max, limit_bytes.to_string())?;
        
        let memory_swap_max = format!("{}/memory.swap.max", cgroup_path);
        fs::write(memory_swap_max, "0")?; // No swap for tenants
        
        Ok(())
    }

    fn set_cpu_limit(&self, cgroup_path: &str, quota_us: u64) -> Result<()> {
        let cpu_max = format!("{}/cpu.max", cgroup_path);
        fs::write(cpu_max, format!("{} 100000", quota_us))?;
        
        Ok(())
    }

    fn set_io_limit(&self, cgroup_path: &str, device: &str, bps: u64) -> Result<()> {
        let io_max = format!("{}/io.max", cgroup_path);
        fs::write(io_max, format!("{} rbps={} wbps={}", device, bps, bps))?;
        
        Ok(())
    }

    pub async fn assign_process_to_tenant(&self, tenant_id: &TenantId, pid: u32) -> Result<()> {
        let cgroup_procs = format!("{}/claude-flow/{}/cgroup.procs", 
            self.cgroup_root, tenant_id.as_str());
        
        fs::write(cgroup_procs, pid.to_string())?;
        
        // Also set process resource limits
        self.set_process_limits(pid)?;
        
        Ok(())
    }

    fn set_process_limits(&self, pid: u32) -> Result<()> {
        // Set file descriptor limit
        setrlimit(Resource::RLIMIT_NOFILE, 1024, 2048)?;
        
        // Set process limit
        setrlimit(Resource::RLIMIT_NPROC, 100, 200)?;
        
        Ok(())
    }
}
```

### Resource Management Configuration

```typescript
// Resource configuration from claude-code-flow project template
interface ProjectResourceConfig {
  project: {
    name: string;
    isolation: {
      level: "strict" | "moderate" | "shared";
      resourceIsolation: boolean;
    };
    resources: {
      agents: {
        max: number;
        types: string[];
        defaultLimits: {
          memory: string;
          cpu: string;
          storage: string;
        };
      };
      memory: {
        totalQuota: string;
        cacheQuota: string;
        retentionDays: number;
      };
      network: {
        bandwidth: string;
        allowedDomains: string[];
        blockedDomains: string[];
      };
    };
  };
}

// Example enterprise resource configuration
const enterpriseResourceConfig: ProjectResourceConfig = {
  project: {
    name: "enterprise-tenant-1",
    isolation: {
      level: "strict",
      resourceIsolation: true
    },
    resources: {
      agents: {
        max: 15,
        types: ["coordinator", "implementer", "analyst"],
        defaultLimits: {
          memory: "2GB",
          cpu: "2000m",
          storage: "10GB"
        }
      },
      memory: {
        totalQuota: "4GB",
        cacheQuota: "1GB",
        retentionDays: 90
      },
      network: {
        bandwidth: "100Mbps",
        allowedDomains: ["*.company.com", "github.com", "npmjs.com"],
        blockedDomains: ["*.social-media.com"]
      }
    }
  }
};
```

### Quota Enforcement Implementation

```typescript
// Quota enforcement from claude-code-flow namespace manager
class ResourceQuotaEnforcer {
  async checkResourceQuota(
    tenantId: string,
    resource: string,
    requestedAmount: number
  ): Promise<QuotaCheckResult> {
    
    const currentUsage = await this.getCurrentUsage(tenantId, resource);
    const quota = await this.getQuota(tenantId, resource);
    
    const availableCapacity = quota.limit - currentUsage.total;
    const wouldExceed = requestedAmount > availableCapacity;
    
    return {
      allowed: !wouldExceed,
      currentUsage: currentUsage.total,
      quota: quota.limit,
      availableCapacity,
      requestedAmount,
      wouldExceed
    };
  }

  async enforceQuotas(tenantId: string): Promise<QuotaStatus> {
    const quotas = await this.getTenantQuotas(tenantId);
    const enforcement = new Map<string, QuotaEnforcementResult>();
    
    for (const [resource, quota] of Object.entries(quotas)) {
      const usage = await this.getCurrentUsage(tenantId, resource);
      const status = this.evaluateQuotaStatus(usage, quota);
      
      if (status.action === 'throttle' || status.action === 'block') {
        await this.applyEnforcementAction(tenantId, resource, status.action);
      }
      
      enforcement.set(resource, status);
    }
    
    return {
      tenantId,
      timestamp: new Date(),
      enforcement: Object.fromEntries(enforcement)
    };
  }
}
```

## Storage Isolation Implementation

### Tenant-Specific Storage Management

```rust
use std::path::{Path, PathBuf};
use tokio::fs;
use serde::{Serialize, Deserialize};
use sqlx::{Pool, Postgres, Row};

pub struct TenantStorageManager {
    base_path: PathBuf,
    encryption_service: Arc<EncryptionService>,
    db_pool: Pool<Postgres>,
}

impl TenantStorageManager {
    pub fn new(base_path: PathBuf, encryption_service: Arc<EncryptionService>, db_pool: Pool<Postgres>) -> Self {
        Self {
            base_path,
            encryption_service,
            db_pool,
        }
    }

    pub async fn get_tenant_path(&self, tenant_id: &TenantId) -> PathBuf {
        self.base_path.join("tenants").join(tenant_id.as_str())
    }

    pub async fn ensure_tenant_storage(&self, tenant_id: &TenantId) -> Result<()> {
        let tenant_path = self.get_tenant_path(tenant_id).await;
        
        // Create directory structure
        fs::create_dir_all(&tenant_path).await?;
        fs::create_dir_all(tenant_path.join("data")).await?;
        fs::create_dir_all(tenant_path.join("logs")).await?;
        fs::create_dir_all(tenant_path.join("temp")).await?;
        
        // Set permissions (Unix-specific)
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let permissions = std::fs::Permissions::from_mode(0o700);
            fs::set_permissions(&tenant_path, permissions).await?;
        }
        
        Ok(())
    }

    pub async fn store_tenant_data(
        &self,
        tenant_id: &TenantId,
        key: &str,
        data: &[u8],
    ) -> Result<()> {
        // Encrypt data with tenant-specific key
        let encrypted = self.encryption_service
            .encrypt_for_tenant(tenant_id, data)
            .await?;
        
        // Store in filesystem
        let file_path = self.get_tenant_path(tenant_id)
            .await
            .join("data")
            .join(format!("{}.enc", key));
        
        fs::write(&file_path, encrypted).await?;
        
        // Also store metadata in database
        sqlx::query(
            "INSERT INTO tenant_storage (tenant_id, key, size, created_at) 
             VALUES ($1, $2, $3, NOW()) 
             ON CONFLICT (tenant_id, key) 
             DO UPDATE SET size = $3, updated_at = NOW()"
        )
        .bind(tenant_id.as_str())
        .bind(key)
        .bind(data.len() as i64)
        .execute(&self.db_pool)
        .await?;
        
        Ok(())
    }

    pub async fn retrieve_tenant_data(
        &self,
        tenant_id: &TenantId,
        key: &str,
    ) -> Result<Vec<u8>> {
        // Check access permissions first
        self.verify_tenant_access(tenant_id).await?;
        
        // Read encrypted data
        let file_path = self.get_tenant_path(tenant_id)
            .await
            .join("data")
            .join(format!("{}.enc", key));
        
        let encrypted = fs::read(&file_path).await?;
        
        // Decrypt with tenant-specific key
        let decrypted = self.encryption_service
            .decrypt_for_tenant(tenant_id, &encrypted)
            .await?;
        
        // Update access time
        sqlx::query("UPDATE tenant_storage SET accessed_at = NOW() WHERE tenant_id = $1 AND key = $2")
            .bind(tenant_id.as_str())
            .bind(key)
            .execute(&self.db_pool)
            .await?;
        
        Ok(decrypted)
    }

    async fn verify_tenant_access(&self, tenant_id: &TenantId) -> Result<()> {
        // Verify tenant exists and is active
        let result = sqlx::query(
            "SELECT status FROM tenants WHERE id = $1"
        )
        .bind(tenant_id.as_str())
        .fetch_optional(&self.db_pool)
        .await?;
        
        match result {
            Some(row) => {
                let status: String = row.get("status");
                if status != "active" {
                    bail!("Tenant is not active");
                }
                Ok(())
            }
            None => bail!("Tenant not found"),
        }
    }
}
```

### Database Schema Isolation

```rust
// Database schema isolation
pub struct TenantDatabaseManager {
    admin_pool: Pool<Postgres>,
}

impl TenantDatabaseManager {
    pub async fn create_tenant_schema(&self, tenant_id: &TenantId) -> Result<()> {
        let schema_name = format!("tenant_{}", tenant_id.as_str());
        
        // Create schema
        sqlx::query(&format!("CREATE SCHEMA IF NOT EXISTS {}", schema_name))
            .execute(&self.admin_pool)
            .await?;
        
        // Create tenant user with limited permissions
        let username = format!("user_{}", tenant_id.as_str());
        let password = self.generate_secure_password();
        
        sqlx::query(&format!(
            "CREATE USER {} WITH PASSWORD '{}'",
            username, password
        ))
        .execute(&self.admin_pool)
        .await?;
        
        // Grant permissions only to tenant schema
        sqlx::query(&format!(
            "GRANT ALL PRIVILEGES ON SCHEMA {} TO {}",
            schema_name, username
        ))
        .execute(&self.admin_pool)
        .await?;
        
        // Set default search path for tenant user
        sqlx::query(&format!(
            "ALTER USER {} SET search_path TO {}",
            username, schema_name
        ))
        .execute(&self.admin_pool)
        .await?;
        
        Ok(())
    }

    fn generate_secure_password(&self) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789!@#$%^&*";
        let mut rng = rand::thread_rng();
        
        (0..32)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }
}
```

## Security Implementation

### Tenant-Aware Authentication

```typescript
// Multi-tenant authentication implementation
class MultiTenantAuthenticator {
  async authenticate(
    credentials: AuthCredentials,
    tenantContext: TenantContext
  ): Promise<AuthResult> {
    
    // Validate tenant context
    const tenant = await this.validateTenant(tenantContext.tenantId);
    if (!tenant.active) {
      throw new SecurityError('Tenant account suspended');
    }

    // Tenant-specific authentication provider
    const authProvider = await this.getAuthProvider(tenant.authConfig);
    const identity = await authProvider.authenticate(credentials);
    
    // Verify user belongs to tenant
    const userTenantMembership = await this.verifyTenantMembership(
      identity.userId,
      tenantContext.tenantId
    );
    
    if (!userTenantMembership.valid) {
      this.auditLogger.log('cross-tenant-auth-attempt', {
        userId: identity.userId,
        requestedTenant: tenantContext.tenantId,
        userTenant: userTenantMembership.actualTenant
      });
      throw new SecurityError('User not authorized for this tenant');
    }

    // Generate tenant-scoped token
    const token = await this.tokenManager.generateTenantToken({
      userId: identity.userId,
      tenantId: tenantContext.tenantId,
      roles: userTenantMembership.roles,
      permissions: userTenantMembership.permissions,
      sessionTimeout: tenant.sessionConfig.timeout
    });

    return {
      success: true,
      token,
      tenantId: tenantContext.tenantId,
      user: identity,
      expiresAt: new Date(Date.now() + tenant.sessionConfig.timeout)
    };
  }
}
```

### Tenant-Specific Encryption

```typescript
// Tenant-specific encryption from claude-code-flow security implementation
class TenantEncryptionManager {
  private keyStore = new Map<string, TenantKeySet>();

  async initializeTenantEncryption(tenantId: string): Promise<void> {
    const keySet = await this.generateTenantKeySet(tenantId);
    this.keyStore.set(tenantId, keySet);
    
    // Store encrypted keys in secure vault
    await this.keyVault.storeKeySet(tenantId, keySet);
    
    // Configure key rotation schedule
    await this.scheduleKeyRotation(tenantId, '90d');
  }

  async encryptTenantData(
    tenantId: string,
    data: Buffer,
    context: EncryptionContext
  ): Promise<EncryptedData> {
    
    const keySet = await this.getTenantKeySet(tenantId);
    const encryptionKey = this.selectEncryptionKey(keySet, context.purpose);
    
    const encrypted = await this.encrypt(data, encryptionKey, {
      algorithm: 'AES-256-GCM',
      additionalData: Buffer.from(JSON.stringify({
        tenantId,
        purpose: context.purpose,
        timestamp: Date.now()
      }))
    });

    return {
      tenantId,
      encryptedData: encrypted.data,
      nonce: encrypted.nonce,
      tag: encrypted.tag,
      keyId: encryptionKey.id,
      algorithm: 'AES-256-GCM'
    };
  }

  async decryptTenantData(
    tenantId: string,
    encryptedData: EncryptedData
  ): Promise<Buffer> {
    
    // Verify tenant boundary
    if (encryptedData.tenantId !== tenantId) {
      this.auditLogger.log('cross-tenant-decryption-attempt', {
        requestingTenant: tenantId,
        dataTenant: encryptedData.tenantId
      });
      throw new SecurityError('Cross-tenant data access denied');
    }

    const keySet = await this.getTenantKeySet(tenantId);
    const decryptionKey = keySet.keys.find(k => k.id === encryptedData.keyId);
    
    if (!decryptionKey) {
      throw new SecurityError('Decryption key not found');
    }

    return await this.decrypt(encryptedData, decryptionKey);
  }

  private async generateTenantKeySet(tenantId: string): Promise<TenantKeySet> {
    return {
      tenantId,
      masterKey: await this.generateKey('master', 256),
      dataKey: await this.generateKey('data', 256),
      searchKey: await this.generateKey('search', 256),
      backupKey: await this.generateKey('backup', 256),
      generatedAt: new Date(),
      rotationSchedule: '90d'
    };
  }
}
```

### Network Isolation Implementation

```rust
use std::net::{IpAddr, Ipv4Addr};
use ipnet::{Ipv4Net, Ipv6Net};

pub struct TenantNetworkManager {
    base_subnet: Ipv4Net,
    vlan_start: u16,
}

impl TenantNetworkManager {
    pub fn new() -> Self {
        Self {
            base_subnet: "10.0.0.0/8".parse().unwrap(),
            vlan_start: 1000,
        }
    }

    pub async fn allocate_tenant_network(&self, tenant_id: &TenantId) -> Result<TenantNetwork> {
        // Calculate tenant-specific subnet
        let tenant_hash = self.hash_tenant_id(tenant_id);
        let subnet_id = (tenant_hash % 65536) as u16;
        
        let tenant_subnet = format!("10.{}.{}.0/24", 
            (subnet_id >> 8) & 0xFF, 
            subnet_id & 0xFF
        ).parse()?;
        
        let vlan_id = self.vlan_start + subnet_id;
        
        Ok(TenantNetwork {
            tenant_id: tenant_id.clone(),
            subnet: tenant_subnet,
            vlan_id,
            gateway: format!("10.{}.{}.1", (subnet_id >> 8) & 0xFF, subnet_id & 0xFF).parse()?,
            dns_servers: vec![
                "*********".parse()?,
                "*********".parse()?,
            ],
        })
    }

    pub async fn configure_tenant_firewall(&self, tenant_id: &TenantId) -> Result<()> {
        let network = self.allocate_tenant_network(tenant_id).await?;
        
        // Configure iptables rules for tenant isolation
        self.add_iptables_rule(&format!(
            "-A FORWARD -s {} -d {} -j DROP",
            network.subnet, self.base_subnet
        ))?;
        
        // Allow only specific services
        self.add_iptables_rule(&format!(
            "-A INPUT -s {} -p tcp --dport 443 -j ACCEPT",
            network.subnet
        ))?;
        
        self.add_iptables_rule(&format!(
            "-A INPUT -s {} -p tcp --dport 80 -j ACCEPT",
            network.subnet
        ))?;
        
        // Drop all other traffic
        self.add_iptables_rule(&format!(
            "-A INPUT -s {} -j DROP",
            network.subnet
        ))?;
        
        Ok(())
    }

    fn add_iptables_rule(&self, rule: &str) -> Result<()> {
        use std::process::Command;
        
        let output = Command::new("iptables")
            .args(rule.split_whitespace())
            .output()?;
        
        if !output.status.success() {
            bail!("Failed to add iptables rule: {:?}", output);
        }
        
        Ok(())
    }

    fn hash_tenant_id(&self, tenant_id: &TenantId) -> u64 {
        use std::hash::{Hash, Hasher};
        use std::collections::hash_map::DefaultHasher;
        
        let mut hasher = DefaultHasher::new();
        tenant_id.hash(&mut hasher);
        hasher.finish()
    }
}

pub struct TenantNetwork {
    pub tenant_id: TenantId,
    pub subnet: Ipv4Net,
    pub vlan_id: u16,
    pub gateway: Ipv4Addr,
    pub dns_servers: Vec<Ipv4Addr>,
}
```

## Billing Integration Implementation

### Usage Tracking Engine

```typescript
// Usage tracking from claude-code-flow cost management
class UsageTrackingEngine {
  async trackResourceUsage(tenantId: string, resource: ResourceUsageEvent): Promise<void> {
    const usageRecord = {
      tenantId,
      timestamp: new Date(),
      resourceType: resource.type,
      resourceId: resource.id,
      usage: {
        quantity: resource.quantity,
        unit: resource.unit,        // 'cpu-hours', 'gb-hours', 'api-calls'
        cost: resource.cost
      },
      metadata: {
        agentId: resource.agentId,
        projectId: resource.projectId,
        taskId: resource.taskId
      }
    };

    await this.usageStore.store(usageRecord);
    await this.costCalculator.updateTenantCosts(tenantId, usageRecord);
    await this.quotaTracker.updateQuotaUsage(tenantId, resource);
  }

  async aggregateUsage(
    tenantId: string, 
    period: TimePeriod
  ): Promise<UsageAggregation> {
    const rawUsage = await this.usageStore.query({
      tenantId,
      startDate: period.start,
      endDate: period.end
    });

    return {
      tenantId,
      period,
      aggregations: {
        compute: this.aggregateComputeUsage(rawUsage),
        memory: this.aggregateMemoryUsage(rawUsage),
        storage: this.aggregateStorageUsage(rawUsage),
        network: this.aggregateNetworkUsage(rawUsage),
        apiCalls: this.aggregateApiUsage(rawUsage)
      },
      totalCost: this.calculateTotalCost(rawUsage)
    };
  }
}
```

### Real-Time Usage Metering

```typescript
// Real-time usage collection based on claude-code-flow monitoring
class RealTimeUsageMeter {
  async startMetering(tenantId: string): Promise<void> {
    const meterConfig = {
      tenantId,
      metrics: ['resource-usage', 'agent-performance', 'costs'],
      realTime: true,
      granularity: '1m',           // 1-minute intervals
      aggregationWindows: ['1h', '1d', '1mo']
    };

    await this.metricsCollector.configure(meterConfig);
    await this.usageStreamer.subscribe(tenantId, this.handleUsageEvent.bind(this));
  }

  private async handleUsageEvent(event: UsageEvent): Promise<void> {
    // Immediate usage tracking
    await this.usageTracker.track(event);
    
    // Real-time cost calculation
    const cost = await this.costCalculator.calculate(event);
    
    // Update running totals
    await this.runningTotals.update(event.tenantId, cost);
    
    // Check for billing thresholds
    await this.billingAlerts.checkThresholds(event.tenantId, cost);
  }
}

interface UsageEvent {
  tenantId: string;
  timestamp: Date;
  eventType: 'agent-spawn' | 'memory-allocation' | 'storage-write' | 'api-call';
  resourceMetrics: {
    cpu?: number;
    memory?: number;
    storage?: number;
    network?: number;
  };
  duration?: number;
  quantity: number;
}
```

### External Billing Integration

```typescript
// Billing provider adapters
interface BillingProvider {
  name: string;
  authenticate(credentials: ProviderCredentials): Promise<void>;
  createCustomer(customer: CustomerData): Promise<string>;
  createSubscription(subscription: SubscriptionData): Promise<string>;
  reportUsage(usage: UsageReport): Promise<void>;
  generateInvoice(invoiceData: InvoiceData): Promise<Invoice>;
  processPayment(payment: PaymentData): Promise<PaymentResult>;
}

class StripeBillingProvider implements BillingProvider {
  name = 'stripe';

  async createCustomer(customer: CustomerData): Promise<string> {
    const stripeCustomer = await this.stripe.customers.create({
      email: customer.email,
      name: customer.name,
      metadata: {
        tenantId: customer.tenantId,
        tier: customer.tier
      }
    });
    
    return stripeCustomer.id;
  }

  async reportUsage(usage: UsageReport): Promise<void> {
    // Report usage to Stripe for metered billing
    for (const item of usage.items) {
      await this.stripe.subscriptionItems.createUsageRecord(
        item.subscriptionItemId,
        {
          quantity: item.quantity,
          timestamp: Math.floor(item.timestamp.getTime() / 1000),
          action: 'set'
        }
      );
    }
  }

  async createSubscription(subscription: SubscriptionData): Promise<string> {
    const stripeSubscription = await this.stripe.subscriptions.create({
      customer: subscription.customerId,
      items: subscription.items.map(item => ({
        price: item.priceId,
        quantity: item.quantity
      })),
      metadata: {
        tenantId: subscription.tenantId,
        tier: subscription.tier
      }
    });
    
    return stripeSubscription.id;
  }
}
```

## Monitoring and Observability

### Tenant Resource Monitoring

```typescript
// Resource monitoring based on claude-flow project monitoring
class TenantResourceMonitor {
  async startMonitoring(tenantId: string): Promise<void> {
    const monitoringConfig = {
      tenantId,
      dashboards: true,
      alerts: true,
      metrics: ['resource-usage', 'agent-performance', 'costs'],
      realTime: true,
      alertThresholds: {
        memory: 80,        // Alert at 80% memory usage
        storage: 85,       // Alert at 85% storage usage
        network: 90,       // Alert at 90% network capacity
        apiCalls: 95       // Alert at 95% API quota
      }
    };

    await this.metricsCollector.configure(monitoringConfig);
    await this.alertManager.setupAlerts(tenantId, monitoringConfig.alertThresholds);
  }

  async collectResourceMetrics(tenantId: string): Promise<ResourceMetrics> {
    return {
      tenantId,
      timestamp: new Date(),
      agents: {
        active: await this.getActiveAgentCount(tenantId),
        total: await this.getTotalAgentCount(tenantId),
        resourceUsage: await this.getAgentResourceUsage(tenantId)
      },
      memory: {
        used: await this.getMemoryUsage(tenantId),
        quota: await this.getMemoryQuota(tenantId),
        cacheUsage: await this.getCacheUsage(tenantId)
      },
      storage: {
        used: await this.getStorageUsage(tenantId),
        quota: await this.getStorageQuota(tenantId),
        itemCount: await this.getItemCount(tenantId)
      },
      network: {
        bandwidth: await this.getBandwidthUsage(tenantId),
        quota: await this.getBandwidthQuota(tenantId),
        requestCount: await this.getRequestCount(tenantId)
      },
      costs: {
        current: await this.getCurrentCosts(tenantId),
        projected: await this.getProjectedCosts(tenantId)
      }
    };
  }
}
```

### Security Monitoring

```typescript
// Security monitoring for multi-tenant environments
class TenantSecurityMonitor {
  async startSecurityMonitoring(tenantId: string): Promise<void> {
    const monitoringConfig = {
      tenantId,
      realTime: true,
      threatDetection: 'ml-based',
      incidentResponse: 'automated',
      alertThresholds: {
        failedLogins: 5,
        suspiciousActivity: 3,
        dataExfiltration: 1,
        crossTenantAccess: 1
      },
      monitoredEvents: [
        'authentication',
        'authorization',
        'data-access',
        'configuration-changes',
        'privilege-escalation'
      ]
    };

    await this.securityEventProcessor.configure(monitoringConfig);
    await this.threatDetectionEngine.enableForTenant(tenantId);
  }

  async processSecurityEvent(event: SecurityEvent): Promise<void> {
    // Analyze event for threats
    const threatAnalysis = await this.threatDetector.analyze(event);
    
    if (threatAnalysis.threat) {
      const incident = await this.createSecurityIncident({
        tenantId: event.tenantId,
        event,
        threat: threatAnalysis,
        severity: threatAnalysis.severity
      });

      // Automated response for high-severity incidents
      if (incident.severity === 'high') {
        await this.incidentResponseManager.executeAutomatedResponse(incident);
      }
    }

    // Update tenant security metrics
    await this.securityMetrics.updateTenantMetrics(event.tenantId, event);
  }
}
```

## Deployment and Configuration

### Step-by-Step Implementation

#### 1. Environment Setup

```bash
# Create tenant isolation directories
sudo mkdir -p /sys/fs/cgroup/claude-flow
sudo mkdir -p /var/lib/claude-flow/tenants
sudo mkdir -p /var/log/claude-flow/tenants

# Set up PostgreSQL with tenant schemas
sudo -u postgres createdb claude_flow_multi_tenant
sudo -u postgres psql claude_flow_multi_tenant -c "CREATE EXTENSION IF NOT EXISTS 'uuid-ossp';"

# Configure Redis for tenant-aware caching
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

#### 2. Configuration Files

```yaml
# config/multi-tenant.yaml
multiTenant:
  enabled: true
  defaultIsolationLevel: strict
  maxTenantsPerCluster: 1000
  
  storage:
    provider: postgres
    connectionPool:
      maxConnections: 100
      minConnections: 10
    encryption:
      enabled: true
      keyRotationDays: 90
      
  networking:
    vlanStart: 1000
    subnetBase: "10.0.0.0/8"
    firewallEnabled: true
    
  resourceLimits:
    default:
      memory: "2GB"
      cpu: "2000m"
      storage: "10GB"
      agents: 10
      
  billing:
    enabled: true
    provider: stripe
    metricCollectionInterval: "1m"
    aggregationInterval: "1h"
    
  monitoring:
    enabled: true
    alertThresholds:
      memoryUsage: 80
      storageUsage: 85
      costPerHour: 100
```

#### 3. Database Migration

```sql
-- Database schema for multi-tenancy
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    tier VARCHAR(50) NOT NULL DEFAULT 'standard',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE tenant_resources (
    tenant_id UUID REFERENCES tenants(id),
    resource_type VARCHAR(100) NOT NULL,
    quota_limit BIGINT NOT NULL,
    current_usage BIGINT DEFAULT 0,
    updated_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (tenant_id, resource_type)
);

CREATE TABLE tenant_storage (
    tenant_id UUID REFERENCES tenants(id),
    key VARCHAR(500) NOT NULL,
    size BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    accessed_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (tenant_id, key)
);

CREATE TABLE usage_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES tenants(id),
    event_type VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    quantity DECIMAL(20,4) NOT NULL,
    unit VARCHAR(50) NOT NULL,
    cost DECIMAL(20,4),
    timestamp TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_usage_events_tenant_time 
ON usage_events(tenant_id, timestamp);

CREATE INDEX idx_tenant_storage_tenant_key 
ON tenant_storage(tenant_id, key);
```

#### 4. Service Configuration

```typescript
// Service startup configuration
class MultiTenantService {
  async initialize(): Promise<void> {
    // Initialize tenant isolation manager
    this.tenantManager = new TenantIsolationManager(this.config.multiTenant);
    
    // Set up resource monitoring
    this.resourceMonitor = new TenantResourceMonitor(this.config.monitoring);
    
    // Configure security monitoring
    this.securityMonitor = new TenantSecurityMonitor(this.config.security);
    
    // Initialize billing integration
    this.billingManager = new BillingManager(this.config.billing);
    
    // Start background services
    await this.startResourceMonitoring();
    await this.startUsageTracking();
    await this.startSecurityMonitoring();
    
    console.log('Multi-tenant service initialized successfully');
  }
  
  async createTenant(config: TenantCreationConfig): Promise<Tenant> {
    // Create tenant with all necessary isolation
    const tenant = await this.tenantManager.createTenant(config);
    
    // Set up monitoring for new tenant
    await this.resourceMonitor.startMonitoring(tenant.id);
    await this.securityMonitor.startSecurityMonitoring(tenant.id);
    
    // Initialize billing
    await this.billingManager.setupTenantBilling(tenant.id);
    
    return tenant;
  }
}
```

## Best Practices Implementation

### Error Handling and Recovery

```typescript
// Error handling for tenant isolation failures
class TenantIsolationErrorHandler {
  async handleIsolationBreach(incident: IsolationIncident): Promise<void> {
    // 1. Immediate containment
    await this.containmentManager.isolateTenant(incident.tenantId);

    // 2. Investigation and logging
    await this.investigationManager.startInvestigation(incident);

    // 3. Notification
    await this.notificationManager.alertSecurityTeam(incident);

    // 4. Recovery procedures
    await this.recoveryManager.initiateTenantRecovery(incident.tenantId);
  }
}
```

### Performance Optimization

```typescript
// Performance optimization for multi-tenant deployments
class TenantPerformanceOptimizer {
  async optimizeResourceAllocation(): Promise<void> {
    const tenants = await this.getAllActiveTenants();
    
    for (const tenant of tenants) {
      const usage = await this.getResourceUsage(tenant.id);
      const recommendations = await this.analyzeUsagePatterns(usage);
      
      if (recommendations.canOptimize) {
        await this.applyOptimizations(tenant.id, recommendations);
      }
    }
  }
  
  async preventNoisyNeighbors(): Promise<void> {
    const metrics = await this.collectPerformanceMetrics();
    const anomalies = await this.detectPerformanceAnomalies(metrics);
    
    for (const anomaly of anomalies) {
      await this.mitigatePerformanceIssue(anomaly);
    }
  }
}
```

### Testing and Validation

```typescript
// Integration tests for multi-tenancy
describe('Multi-Tenant Isolation', () => {
  test('should prevent cross-tenant data access', async () => {
    const tenant1 = await createTestTenant('tenant-1');
    const tenant2 = await createTestTenant('tenant-2');
    
    await storeTenantData(tenant1.id, 'secret-data', 'sensitive-info');
    
    await expect(
      retrieveTenantData(tenant2.id, 'secret-data')
    ).rejects.toThrow('Cross-tenant data access denied');
  });
  
  test('should enforce resource quotas', async () => {
    const tenant = await createTestTenant('quota-test', {
      quotas: { memory: 100 * 1024 * 1024 } // 100MB
    });
    
    await expect(
      allocateMemory(tenant.id, 200 * 1024 * 1024) // 200MB
    ).rejects.toThrow('Memory quota exceeded');
  });
});
```

## Integration Points

### Memory Management Integration

```typescript
// Memory manager with tenant isolation from claude-code-flow
class TenantMemoryManager {
  async storeTenantData(tenantId: string, data: any): Promise<void> {
    const memory = await this.getMemoryManager();
    await memory.store({
      category: data.category,
      content: data.content,
      tags: data.tags,
      namespace: tenantId,  // Tenant-specific namespace
      encryption: {
        enabled: true,
        keyId: `tenant-${tenantId}-key`
      }
    });
  }

  async queryTenantData(tenantId: string, query: any): Promise<any[]> {
    const memory = await this.getMemoryManager();
    return await memory.query({
      ...query,
      namespace: tenantId,  // Restrict to tenant namespace
      enforceIsolation: true
    });
  }
}
```

### Agent Management Integration

```typescript
// Agent resource management with tenant boundaries
class AgentResourceManager {
  async allocateAgentResources(config: AgentResourceConfig): Promise<void> {
    // Check tenant quota availability
    const quotaCheck = await this.checkTenantQuota(
      config.tenantId,
      config.resourceLimits
    );
    
    if (!quotaCheck.allowed) {
      throw new Error(`Resource allocation would exceed tenant quota: ${quotaCheck.reason}`);
    }

    // Create resource allocation
    await this.resourceAllocator.allocate({
      agentId: config.agentId,
      tenantId: config.tenantId,
      limits: config.resourceLimits,
      isolation: true
    });

    // Set permissions
    await this.permissionManager.setAgentPermissions(
      config.agentId,
      config.permissions
    );

    // Update tenant usage tracking
    await this.usageTracker.updateTenantUsage(config.tenantId, config.resourceLimits);
  }
}
```

---

*Multi-tenancy implementation guide derived from claude-code-flow enterprise implementation patterns and production deployment experience*