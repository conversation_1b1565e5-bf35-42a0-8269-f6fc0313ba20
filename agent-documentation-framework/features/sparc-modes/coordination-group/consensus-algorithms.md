# Consensus Algorithms

Distributed agreement mechanisms for coordination-focused SPARC modes.

## Overview

Consensus algorithms enable Innovator, Memory Manager, and Swarm Coordinator modes to achieve distributed agreement on decisions, state, and actions.

## Algorithm Categories

### 1. Voting-Based Consensus

**Majority Vote**
```javascript
class MajorityConsensus {
  constructor() {
    this.votes = new Map();
    this.threshold = 0.5;
  }
  
  vote(agentId, choice) {
    this.votes.set(agentId, choice);
    return this.checkConsensus();
  }
  
  checkConsensus() {
    const counts = new Map();
    for (const choice of this.votes.values()) {
      counts.set(choice, (counts.get(choice) || 0) + 1);
    }
    
    const total = this.votes.size;
    for (const [choice, count] of counts) {
      if (count / total > this.threshold) {
        return { consensus: true, choice };
      }
    }
    return { consensus: false };
  }
}
```

**Weighted Voting**
```javascript
class WeightedConsensus {
  constructor() {
    this.votes = new Map();
    this.weights = new Map();
  }
  
  setWeight(agentId, weight) {
    this.weights.set(agentId, weight);
  }
  
  vote(agentId, choice) {
    this.votes.set(agentId, choice);
    return this.checkConsensus();
  }
  
  checkConsensus() {
    const scores = new Map();
    let totalWeight = 0;
    
    for (const [agentId, choice] of this.votes) {
      const weight = this.weights.get(agentId) || 1;
      scores.set(choice, (scores.get(choice) || 0) + weight);
      totalWeight += weight;
    }
    
    for (const [choice, score] of scores) {
      if (score / totalWeight > 0.5) {
        return { consensus: true, choice, confidence: score / totalWeight };
      }
    }
    return { consensus: false };
  }
}
```

### 2. Leader-Based Consensus

**Rotating Leader**
```javascript
class RotatingLeaderConsensus {
  constructor(agents) {
    this.agents = agents;
    this.currentLeader = 0;
    this.term = 0;
  }
  
  propose(proposal) {
    const leader = this.agents[this.currentLeader];
    const votes = this.collectVotes(leader, proposal);
    
    if (this.validateVotes(votes)) {
      this.commit(proposal);
      return { success: true, leader: leader.id };
    }
    
    this.rotateLeader();
    return { success: false };
  }
  
  rotateLeader() {
    this.currentLeader = (this.currentLeader + 1) % this.agents.length;
    this.term++;
  }
}
```

**Elected Leader**
```javascript
class ElectedLeaderConsensus {
  constructor() {
    this.leader = null;
    this.term = 0;
    this.electionTimeout = null;
  }
  
  startElection() {
    this.term++;
    const votes = this.requestVotes();
    
    if (votes.length > this.agents.length / 2) {
      this.leader = this.id;
      this.sendHeartbeats();
      return true;
    }
    return false;
  }
  
  handleProposal(proposal) {
    if (this.leader === this.id) {
      return this.replicateProposal(proposal);
    }
    return this.forwardToLeader(proposal);
  }
}
```

### 3. Byzantine Fault Tolerant

**PBFT Implementation**
```javascript
class PBFTConsensus {
  constructor(nodeCount) {
    this.nodes = nodeCount;
    this.f = Math.floor((nodeCount - 1) / 3);
    this.phases = ['prepare', 'commit'];
  }
  
  consensus(request) {
    // Pre-prepare phase
    const prePrepare = this.createPrePrepare(request);
    const prepareMessages = this.collectPrepares(prePrepare);
    
    if (prepareMessages.length >= 2 * this.f + 1) {
      // Prepare phase successful
      const commitMessages = this.collectCommits();
      
      if (commitMessages.length >= 2 * this.f + 1) {
        // Consensus achieved
        return { consensus: true, value: request };
      }
    }
    
    return { consensus: false };
  }
}
```

## Mode-Specific Applications

### Innovator Mode

**Creative Consensus**
```javascript
class CreativeConsensus {
  constructor() {
    this.ideas = new Map();
    this.evaluations = new Map();
  }
  
  proposeIdea(agentId, idea) {
    this.ideas.set(idea.id, {
      ...idea,
      proposer: agentId,
      timestamp: Date.now()
    });
  }
  
  evaluateIdea(evaluatorId, ideaId, score, feedback) {
    if (!this.evaluations.has(ideaId)) {
      this.evaluations.set(ideaId, []);
    }
    
    this.evaluations.get(ideaId).push({
      evaluator: evaluatorId,
      score,
      feedback,
      timestamp: Date.now()
    });
    
    return this.checkIdeaConsensus(ideaId);
  }
  
  checkIdeaConsensus(ideaId) {
    const evals = this.evaluations.get(ideaId) || [];
    
    if (evals.length < 3) return null;
    
    const avgScore = evals.reduce((sum, e) => sum + e.score, 0) / evals.length;
    const variance = this.calculateVariance(evals);
    
    if (variance < 0.2 && avgScore > 0.7) {
      return {
        consensus: true,
        confidence: 1 - variance,
        score: avgScore
      };
    }
    
    return { consensus: false };
  }
}
```

### Memory Manager Mode

**Memory Consensus**
```javascript
class MemoryConsensus {
  constructor() {
    this.memories = new Map();
    this.validations = new Map();
  }
  
  proposeMemory(memory) {
    const hash = this.hashMemory(memory);
    this.memories.set(hash, memory);
    
    return this.validateMemory(hash);
  }
  
  validateMemory(hash) {
    const validators = this.selectValidators();
    const validations = [];
    
    for (const validator of validators) {
      const result = validator.validate(this.memories.get(hash));
      validations.push(result);
    }
    
    const valid = validations.filter(v => v.valid).length;
    const threshold = Math.ceil(validators.length * 0.66);
    
    return {
      consensus: valid >= threshold,
      confidence: valid / validators.length,
      validators: validators.length
    };
  }
}
```

### Swarm Coordinator Mode

**Swarm Action Consensus**
```javascript
class SwarmConsensus {
  constructor() {
    this.proposals = new Map();
    this.commitments = new Map();
  }
  
  proposeAction(coordinatorId, action) {
    const proposalId = this.generateId();
    
    this.proposals.set(proposalId, {
      action,
      coordinator: coordinatorId,
      timestamp: Date.now(),
      phase: 'proposed'
    });
    
    return this.gatherCommitments(proposalId);
  }
  
  async gatherCommitments(proposalId) {
    const proposal = this.proposals.get(proposalId);
    const agents = await this.getAvailableAgents();
    
    const commitments = await Promise.all(
      agents.map(agent => agent.evaluateProposal(proposal))
    );
    
    const committed = commitments.filter(c => c.commit).length;
    const threshold = Math.ceil(agents.length * 0.75);
    
    if (committed >= threshold) {
      proposal.phase = 'committed';
      return { consensus: true, commitments: committed };
    }
    
    return { consensus: false };
  }
}
```

## Consensus Patterns

### 1. Quorum-Based Patterns

```javascript
class QuorumConsensus {
  constructor(config) {
    this.readQuorum = config.readQuorum || 0.5;
    this.writeQuorum = config.writeQuorum || 0.75;
    this.nodes = config.nodes;
  }
  
  async read(key) {
    const responses = await this.queryNodes(
      key,
      Math.ceil(this.nodes * this.readQuorum)
    );
    
    return this.reconcile(responses);
  }
  
  async write(key, value) {
    const confirmations = await this.writeToNodes(
      key,
      value,
      Math.ceil(this.nodes * this.writeQuorum)
    );
    
    return confirmations.length >= this.writeQuorum * this.nodes;
  }
}
```

### 2. Multi-Phase Commit

```javascript
class MultiPhaseCommit {
  constructor() {
    this.phases = ['propose', 'prepare', 'commit'];
    this.currentPhase = 0;
  }
  
  async execute(transaction) {
    for (const phase of this.phases) {
      const result = await this[phase](transaction);
      
      if (!result.success) {
        await this.rollback(transaction);
        return { success: false, phase };
      }
    }
    
    return { success: true };
  }
}
```

### 3. Gossip Protocol

```javascript
class GossipConsensus {
  constructor() {
    this.state = new Map();
    this.peers = new Set();
    this.gossipInterval = 1000;
  }
  
  startGossip() {
    setInterval(() => {
      const peer = this.selectRandomPeer();
      this.exchangeState(peer);
    }, this.gossipInterval);
  }
  
  exchangeState(peer) {
    const myState = this.getState();
    const peerState = peer.getState();
    
    this.mergeStates(myState, peerState);
    peer.mergeStates(peerState, myState);
  }
}
```

## Performance Optimization

### 1. Batching Consensus

```javascript
class BatchedConsensus {
  constructor() {
    this.batch = [];
    this.batchSize = 100;
    this.batchTimeout = 50;
  }
  
  addToBatch(item) {
    this.batch.push(item);
    
    if (this.batch.length >= this.batchSize) {
      return this.processBatch();
    }
    
    this.scheduleBatchProcessing();
  }
  
  async processBatch() {
    const items = this.batch.splice(0, this.batchSize);
    const consensus = await this.batchConsensus(items);
    
    return {
      processed: items.length,
      consensus: consensus.success,
      latency: consensus.latency
    };
  }
}
```

### 2. Hierarchical Consensus

```javascript
class HierarchicalConsensus {
  constructor() {
    this.levels = [];
    this.localConsensus = new Map();
  }
  
  async achieve(proposal) {
    // Local consensus first
    const local = await this.localConsensus(proposal);
    
    if (!local.success) return local;
    
    // Regional consensus
    const regional = await this.regionalConsensus(proposal);
    
    if (!regional.success) return regional;
    
    // Global consensus
    return await this.globalConsensus(proposal);
  }
}
```

## Failure Handling

### 1. Timeout Management

```javascript
class TimeoutConsensus {
  constructor() {
    this.timeouts = {
      propose: 1000,
      vote: 2000,
      commit: 3000
    };
  }
  
  async withTimeout(operation, phase) {
    return Promise.race([
      operation(),
      this.timeout(this.timeouts[phase], phase)
    ]);
  }
  
  timeout(ms, phase) {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`${phase} timeout`)), ms);
    });
  }
}
```

### 2. Recovery Mechanisms

```javascript
class RecoverableConsensus {
  constructor() {
    this.log = [];
    this.checkpoints = new Map();
  }
  
  async recover() {
    const lastCheckpoint = this.getLastCheckpoint();
    const uncommitted = this.getUncommittedAfter(lastCheckpoint);
    
    for (const entry of uncommitted) {
      await this.replayEntry(entry);
    }
    
    return this.resumeConsensus();
  }
}
```

## Integration Guidelines

### 1. Mode Selection

```javascript
function selectConsensusAlgorithm(context) {
  const { mode, requirements, environment } = context;
  
  if (mode === 'innovator') {
    return requirements.creativity > 0.7
      ? new CreativeConsensus()
      : new WeightedConsensus();
  }
  
  if (mode === 'memory-manager') {
    return requirements.consistency > 0.9
      ? new PBFTConsensus()
      : new QuorumConsensus();
  }
  
  if (mode === 'swarm-coordinator') {
    return environment.distributed
      ? new GossipConsensus()
      : new ElectedLeaderConsensus();
  }
}
```

### 2. Performance Tuning

```javascript
class AdaptiveConsensus {
  constructor() {
    this.algorithms = new Map();
    this.metrics = new Map();
  }
  
  selectOptimal(context) {
    const candidates = this.algorithms.values();
    let best = null;
    let bestScore = -1;
    
    for (const algorithm of candidates) {
      const score = this.evaluateAlgorithm(algorithm, context);
      
      if (score > bestScore) {
        best = algorithm;
        bestScore = score;
      }
    }
    
    return best;
  }
}
```

## Best Practices

1. **Algorithm Selection**
   - Match algorithm to consistency requirements
   - Consider network topology and latency
   - Balance between speed and reliability

2. **Failure Handling**
   - Implement comprehensive timeout strategies
   - Design for partition tolerance
   - Include recovery mechanisms

3. **Performance Optimization**
   - Use batching for high-throughput scenarios
   - Implement hierarchical consensus for scale
   - Monitor and adapt to changing conditions

4. **Security Considerations**
   - Validate all consensus participants
   - Implement Byzantine fault tolerance when needed
   - Audit consensus decisions