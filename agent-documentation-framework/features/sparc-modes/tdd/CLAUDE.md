# TDD (Test-Driven Development) Mode

## Purpose and Use Cases

The TDD mode specializes in test-driven development practices, creating comprehensive test suites before implementation. TDD agents ensure code quality, correctness, and maintainability through rigorous testing methodologies.

### Primary Use Cases
- Developing new features with test-first approach
- Creating comprehensive test suites
- Refactoring with confidence via test coverage
- Establishing quality gates and standards
- Building regression test suites

## Rust Code Examples

### SPARC TDD Mode Trait Definition

```rust
// Example: TDD Mode trait for SPARC system
pub trait TDDMode: Send + Sync {
    fn name(&self) -> &'static str { "TDD" }
    fn description(&self) -> &'static str { 
        "Test-Driven Development mode for comprehensive testing"
    }
    
    // Core TDD cycle methods
    async fn write_failing_test(&mut self, requirement: &str) -> Result<TestCase, ModeError>;
    async fn make_test_pass(&mut self, test: &TestCase) -> Result<Implementation, ModeError>;
    async fn refactor_code(&mut self, impl_code: &Implementation) -> Result<RefactoredCode, ModeError>;
    
    // Test generation capabilities
    async fn generate_test_suite(&mut self, spec: &Specification) -> Result<TestSuite, ModeError>;
    async fn identify_edge_cases(&mut self, component: &Component) -> Result<Vec<TestCase>, ModeError>;
}

// Test case representation
#[derive(Debug, Clone)]
pub struct TestCase {
    pub name: String,
    pub description: String,
    pub test_type: TestType,
    pub assertions: Vec<Assertion>,
    pub setup: Option<String>,
    pub teardown: Option<String>,
}

#[derive(Debug, Clone)]
pub enum TestType {
    Unit,
    Integration, 
    Property,
    Contract,
    Mutation,
}
```

### TDD State Machine Implementation

```rust
// Simple state machine for TDD cycle
#[derive(Debug, Clone)]
pub enum TDDState {
    WritingTest {
        requirement: String,
        attempts: u32,
    },
    TestFailing {
        test_case: TestCase,
        failure_reason: String,
    },
    ImplementingCode {
        test_case: TestCase,
        current_impl: Option<String>,
    },
    TestPassing {
        test_case: TestCase,
        implementation: Implementation,
    },
    Refactoring {
        test_case: TestCase,
        implementation: Implementation,
        improvements: Vec<String>,
    },
    Complete {
        test_suite: TestSuite,
        coverage_report: CoverageReport,
    },
}

impl TDDState {
    pub fn transition(&mut self, event: TDDEvent) -> Result<(), StateError> {
        match (self.clone(), event) {
            (TDDState::WritingTest { .. }, TDDEvent::TestWritten(test)) => {
                *self = TDDState::TestFailing {
                    test_case: test,
                    failure_reason: "No implementation yet".to_string(),
                };
                Ok(())
            }
            (TDDState::TestFailing { test_case, .. }, TDDEvent::CodeImplemented(impl_code)) => {
                *self = TDDState::TestPassing {
                    test_case,
                    implementation: impl_code,
                };
                Ok(())
            }
            // ... other transitions
            _ => Err(StateError::InvalidTransition),
        }
    }
}
```

## Key Behaviors and Characteristics

### Core Behaviors
- **Test-First Design**: Writes tests before implementation
- **Red-Green-Refactor**: Follows TDD cycle rigorously
- **Edge Case Identification**: Finds boundary conditions
- **Coverage Analysis**: Ensures comprehensive testing
- **Continuous Validation**: Runs tests frequently

### Unique Characteristics
- Deep understanding of testing frameworks
- Ability to anticipate failure modes
- Focus on testable design patterns
- Balance between coverage and practicality
- Clear test naming and organization

## When to Use This Mode

Deploy TDD agents when:
- Starting new feature development
- Refactoring critical system components
- Building APIs with contracts
- Establishing quality standards
- Creating safety nets for changes

## Integration Points

### Works Well With
- **Coder**: Implements code to pass tests
- **Architect**: Ensures testable designs
- **Tester**: Extends with integration tests
- **Debugger**: Identifies test gaps
- **Reviewer**: Validates test quality

### Communication Patterns
- Provides test specifications to coders
- Receives design constraints from architects
- Shares coverage reports with orchestrators
- Collaborates with testers on test strategy
- Updates memory with test results

## Success Criteria

TDD success is measured by:
1. **Test Coverage**: Comprehensive code coverage
2. **Test Quality**: Meaningful, maintainable tests
3. **Failure Detection**: Tests catch real bugs
4. **Fast Feedback**: Quick test execution
5. **Clear Specifications**: Tests document behavior

## Best Practices

1. Write minimal test to fail first
2. Implement simplest solution to pass
3. Refactor only with green tests
4. One assertion per test when possible
5. Use descriptive test names
6. Maintain fast test execution

## Anti-Patterns to Avoid

- Test After: Writing tests after code
- Brittle Tests: Over-specific assertions
- Slow Tests: Long-running test suites
- Testing Implementation: Focus on behavior
- Ignored Tests: Don't comment out failures
- Over-Mocking: Test real behavior when possible

## Testing Strategies

The TDD mode employs:
- **Unit Testing**: Isolated component tests
- **Integration Testing**: Component interaction tests
- **Contract Testing**: API boundary validation
- **Property Testing**: Generative test cases
- **Mutation Testing**: Test quality validation
- **Behavior Testing**: User-centric scenarios

## Test Organization

TDD agents structure tests with:
- Clear test file organization
- Consistent naming conventions
- Given-When-Then patterns
- Arrange-Act-Assert structure
- Test data builders
- Shared test utilities

## TDD Cycle

The fundamental cycle:
1. **Red**: Write failing test
2. **Green**: Make test pass
3. **Refactor**: Improve code
4. **Repeat**: Next test case

The TDD mode ensures code quality from the ground up, providing confidence in system behavior and enabling safe refactoring throughout the development lifecycle.

## Mode Coordination Examples

### Coordinating with Other SPARC Modes

```rust
// Example: TDD mode coordinating with other modes
pub struct TDDCoordinator {
    tdd_mode: Box<dyn TDDMode>,
    coder_mode: Option<Box<dyn CoderMode>>,
    architect_mode: Option<Box<dyn ArchitectMode>>,
}

impl TDDCoordinator {
    pub async fn coordinate_test_implementation(
        &mut self,
        requirement: &str,
    ) -> Result<(), CoordinationError> {
        // Step 1: Get architecture constraints if architect is available
        let constraints = if let Some(architect) = &mut self.architect_mode {
            architect.get_design_constraints(requirement).await?
        } else {
            DesignConstraints::default()
        };
        
        // Step 2: Write failing test with constraints
        let test_case = self.tdd_mode
            .write_failing_test(requirement)
            .await?;
        
        // Step 3: Coordinate with coder to implement
        if let Some(coder) = &mut self.coder_mode {
            let implementation = coder
                .implement_to_pass_test(&test_case, &constraints)
                .await?;
            
            // Step 4: Verify test passes
            self.tdd_mode.make_test_pass(&test_case).await?;
            
            // Step 5: Refactor if needed
            self.tdd_mode.refactor_code(&implementation).await?;
        }
        
        Ok(())
    }
}
```

### Test Strategy Pattern

```rust
// Example: Different testing strategies for TDD mode
pub trait TestStrategy: Send + Sync {
    async fn generate_tests(&self, component: &Component) -> Result<Vec<TestCase>, Error>;
    fn priority(&self) -> TestPriority;
}

pub struct UnitTestStrategy;
impl TestStrategy for UnitTestStrategy {
    async fn generate_tests(&self, component: &Component) -> Result<Vec<TestCase>, Error> {
        // Generate isolated unit tests for each public method
        let mut tests = Vec::new();
        for method in &component.public_methods {
            tests.push(TestCase {
                name: format!("test_{}_returns_expected", method.name),
                test_type: TestType::Unit,
                // ... test details
            });
        }
        Ok(tests)
    }
    
    fn priority(&self) -> TestPriority {
        TestPriority::High
    }
}

pub struct PropertyTestStrategy;
impl TestStrategy for PropertyTestStrategy {
    async fn generate_tests(&self, component: &Component) -> Result<Vec<TestCase>, Error> {
        // Generate property-based tests
        Ok(vec![
            TestCase {
                name: "test_invariants_hold".to_string(),
                test_type: TestType::Property,
                // ... property test details
            }
        ])
    }
    
    fn priority(&self) -> TestPriority {
        TestPriority::Medium
    }
}
```

### Mode Transition Example

```rust
// Example: Transitioning between TDD and other modes
#[derive(Debug)]
pub enum SparcMode {
    TDD(TDDState),
    Coder(CoderState),
    Debugger(DebuggerState),
    // ... other modes
}

impl SparcMode {
    pub async fn transition_to(&mut self, target: ModeType) -> Result<(), TransitionError> {
        match (self, target) {
            (SparcMode::TDD(state), ModeType::Coder) => {
                // Transition from TDD to Coder when tests are ready
                if let TDDState::TestFailing { test_case, .. } = state {
                    *self = SparcMode::Coder(CoderState::Implementing {
                        test_to_pass: test_case.clone(),
                    });
                    Ok(())
                } else {
                    Err(TransitionError::InvalidState)
                }
            }
            (SparcMode::TDD(state), ModeType::Debugger) => {
                // Transition to debugger when tests reveal bugs
                if let TDDState::TestFailing { failure_reason, .. } = state {
                    *self = SparcMode::Debugger(DebuggerState::Investigating {
                        issue: failure_reason.clone(),
                    });
                    Ok(())
                } else {
                    Err(TransitionError::InvalidState)
                }
            }
            // ... other transitions
            _ => Err(TransitionError::UnsupportedTransition),
        }
    }
}
```