# TDD Mode Transitions and Lifecycle

## Mode Lifecycle Overview
The TDD mode follows a structured lifecycle with clear entry and exit points, based on claude-flow's SPARC phase transition protocols documented in the memory bank architecture.

## State Transition Diagram
```
┌─────────────┐
│   INITIAL   │
└──────┬──────┘
       │ initialize_tdd()
       ▼
┌─────────────┐    test_fails()     ┌─────────────┐
│  RED_PHASE  │◄──────────────────┤ GREEN_PHASE │
└──────┬──────┘                   └──────┬──────┘
       │ test_written()                  │ test_passes()
       └────────────────────────────────►│
                                         ▼
                                  ┌─────────────┐
                                  │REFACTOR_PHASE│
                                  └──────┬──────┘
                                         │ cycle_complete()
                                         ▼
                                  ┌─────────────┐
                                  │  COMPLETE   │
                                  └─────────────┘
```

## Entry Conditions
The TDD mode can be entered from several contexts within the SPARC ecosystem:

### Direct Invocation
```bash
# Direct TDD mode entry
./claude-flow sparc tdd "implement user authentication system"
./claude-flow sparc run tdd "create payment processing" --pattern "rest-api"
```

### From Other SPARC Modes
Based on context7 handoff protocols:

```typescript
// From Architect mode - specifications ready
const architectHandoff: SparcHandoff = {
  sourceMode: 'architect',
  targetMode: 'tdd',
  prerequisites: ['requirements documented', 'acceptance criteria defined'],
  deliverables: ['functional-spec.md', 'non-functional-requirements.md'],
  validation: 'all requirements traceable'
};

// From Spec-Pseudocode mode - algorithms defined  
const specHandoff: SparcHandoff = {
  sourceMode: 'spec-pseudocode',
  targetMode: 'tdd',
  prerequisites: ['algorithms defined', 'data structures outlined'],
  deliverables: ['pseudocode.md', 'flow-diagrams.md'],
  validation: 'implementation roadmap clear'
};
```

### Batch Orchestration Entry
```bash
# TDD as part of development workflow
batchtool orchestrate --phase3-sequential "Implementation Phase" \
  "npx claude-flow sparc run spec-pseudocode 'create detailed specifications'" \
  "npx claude-flow sparc run architect 'design system architecture'" \
  "npx claude-flow sparc tdd 'implement with test coverage'"
```

## Internal State Transitions

### RED Phase Transitions
```typescript
class RedPhaseTransitions {
  async validateEntry(context: TDDContext): Promise<boolean> {
    // Ensure we have requirements or specifications
    return context.requirements !== null || 
           context.architectureSpecs !== null;
  }
  
  async transitionToGreen(testCode: string): Promise<TransitionResult> {
    // Verify test fails before moving to GREEN
    const testResults = await this.runTest(testCode);
    
    if (testResults.status !== 'FAILED') {
      return {
        success: false,
        reason: 'Test must fail in RED phase',
        suggestedAction: 'revise_test'
      };
    }
    
    return {
      success: true,
      nextState: TDDState.GREEN_PHASE,
      artifacts: { testCode, testResults }
    };
  }
}
```

### GREEN Phase Transitions
```typescript
class GreenPhaseTransitions {
  async transitionToRefactor(implementation: string): Promise<TransitionResult> {
    // Ensure tests pass with minimal implementation
    const testResults = await this.runTest(implementation);
    
    if (testResults.status !== 'PASSED') {
      return {
        success: false,
        reason: 'Tests must pass in GREEN phase',
        suggestedAction: 'revise_implementation',
        fallbackState: TDDState.RED_PHASE
      };
    }
    
    return {
      success: true,
      nextState: TDDState.REFACTOR_PHASE,
      artifacts: { implementation, testResults }
    };
  }
  
  async handleTestFailure(): Promise<TransitionResult> {
    // Fallback to RED phase if implementation insufficient
    return {
      success: true,
      nextState: TDDState.RED_PHASE,
      reason: 'Implementation insufficient - revising tests or requirements'
    };
  }
}
```

### REFACTOR Phase Transitions
```typescript
class RefactorPhaseTransitions {
  async evaluateRefactorCompletion(code: string): Promise<TransitionResult> {
    const metrics = await this.analyzeCodeQuality(code);
    const refactorOpportunities = await this.identifyRefactorOpportunities(code);
    
    if (refactorOpportunities.length === 0 && metrics.quality >= 0.8) {
      return {
        success: true,
        nextState: TDDState.COMPLETE,
        reason: 'TDD cycle complete - high quality achieved'
      };
    }
    
    if (this.shouldContinueRefactoring(metrics, refactorOpportunities)) {
      return {
        success: true,
        nextState: TDDState.REFACTOR_PHASE,
        reason: 'Continue refactoring for better quality'
      };
    }
    
    return {
      success: true,
      nextState: TDDState.COMPLETE,
      reason: 'Acceptable quality achieved'
    };
  }
}
```

## Exit Conditions and Handoffs

### Successful Completion
```typescript
interface TDDCompletionHandoff {
  artifacts: {
    testSuite: TestArtifact[];
    implementation: CodeArtifact[];
    documentation: string[];
    coverage: CoverageReport;
  };
  nextRecommendedModes: SparcMode[];
  memoryKeys: string[];
}

// Example successful handoff
const completionHandoff: TDDCompletionHandoff = {
  artifacts: {
    testSuite: ['user-service.test.ts', 'integration.test.ts'],
    implementation: ['user-service.ts', 'user-repository.ts'],
    documentation: ['tdd-session-report.md'],
    coverage: { lines: 95, branches: 90, functions: 100 }
  },
  nextRecommendedModes: ['integration', 'security-review', 'docs-writer'],
  memoryKeys: ['tdd_session_user_auth', 'test_patterns_user_service']
};
```

### Integration with Other Modes
Based on context7 multi-stage development patterns:

```typescript
// Handoff to Integration mode
const integrationHandoff: SparcHandoff = {
  sourceMode: 'tdd',
  targetMode: 'integration',
  prerequisites: ['all tests passing', 'code coverage met'],
  deliverables: ['test-suite', 'implementation', 'coverage-report'],
  validation: 'integration readiness confirmed'
};

// Handoff to Security Review mode
const securityHandoff: SparcHandoff = {
  sourceMode: 'tdd',
  targetMode: 'security-review',
  prerequisites: ['implementation complete', 'security tests included'],
  deliverables: ['security-test-suite', 'implementation-review'],
  validation: 'security requirements addressed'
};

// Handoff to Documenter mode
const documenterHandoff: SparcHandoff = {
  sourceMode: 'tdd',
  targetMode: 'docs-writer',
  prerequisites: ['TDD cycle complete', 'API stabilized'],
  deliverables: ['test-documentation', 'api-examples'],
  validation: 'documentation requirements met'
};
```

## Parallel Execution Transitions
TDD mode supports parallel execution with other modes:

```typescript
// Parallel TDD execution example from context7
await batchTool.run({
  parallel: true,
  commands: [
    "npx claude-flow sparc tdd 'user authentication' --non-interactive",
    "npx claude-flow sparc tdd 'payment processing' --non-interactive", 
    "npx claude-flow sparc tdd 'notification system' --non-interactive"
  ]
});
```

## Error Recovery Transitions
```typescript
class TDDErrorRecovery {
  async handleCompilationError(error: CompilationError): Promise<TransitionResult> {
    return {
      success: true,
      nextState: TDDState.GREEN_PHASE,
      reason: 'Fix compilation errors in implementation',
      suggestedActions: ['syntax_fix', 'import_resolution']
    };
  }
  
  async handleTestTimeout(error: TimeoutError): Promise<TransitionResult> {
    return {
      success: true,
      nextState: TDDState.RED_PHASE,
      reason: 'Revise test for performance',
      suggestedActions: ['optimize_test', 'reduce_scope']
    };
  }
  
  async handleInfrastructureFailure(error: InfraError): Promise<TransitionResult> {
    return {
      success: false,
      nextState: TDDState.ERROR,
      reason: 'Infrastructure failure - manual intervention required',
      suggestedActions: ['check_dependencies', 'restart_environment']
    };
  }
}
```

## Memory-Driven Transitions
TDD mode uses claude-flow's memory bank for coordination:

```typescript
// Store transition context for coordination
await memoryBank.storeKnowledge('tdd-transitions', 'user-auth-session', {
  currentPhase: 'REFACTOR',
  transitionHistory: [
    { from: 'INITIAL', to: 'RED', timestamp: '2025-01-01T10:00:00Z' },
    { from: 'RED', to: 'GREEN', timestamp: '2025-01-01T10:15:00Z' },
    { from: 'GREEN', to: 'REFACTOR', timestamp: '2025-01-01T10:30:00Z' }
  ],
  nextPossibleStates: ['COMPLETE', 'REFACTOR'],
  artifacts: { tests: 'passed', implementation: 'refactored' }
});

// Query for transition context
const transitionContext = await memoryBank.retrieveKnowledge(
  'tdd-transitions', 
  'user-auth-session'
);
```

## Lifecycle Metrics and Monitoring
```typescript
interface TDDLifecycleMetrics {
  phaseDistribution: {
    red: number;    // Time in RED phase
    green: number;  // Time in GREEN phase  
    refactor: number; // Time in REFACTOR phase
  };
  transitionCount: number;
  cycleEfficiency: number;
  testCoverage: number;
  codeQuality: number;
}
```