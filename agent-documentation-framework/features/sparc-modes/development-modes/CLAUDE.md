# Development Modes - Architect & Coder

## Overview

This document covers the two core development modes in SPARC:

- **Architect Mode**: System design, architectural patterns, and strategic technical decisions
- **Coder Mode**: Implementation, code generation, and hands-on development

These modes work in tandem to transform ideas into working systems, with Architect providing the blueprint and Coder executing the implementation.

## Mode Selection

```yaml
mode_selection_guide:
  use_architect_when:
    - Designing new systems or features
    - Selecting technology stacks
    - Planning integrations
    - Addressing scalability challenges
    - Making strategic technical decisions
    
  use_coder_when:
    - Implementing features from specs
    - Writing actual code
    - Refactoring existing code
    - Fixing bugs
    - Creating APIs and integrations
```

## Architect Mode

### Purpose and Capabilities

The Architect creates system blueprints, ensuring robust, scalable designs aligned with business needs.

```rust
// Core Architect trait
pub trait ArchitectMode: Send + Sync {
    async fn design_system(&mut self, requirements: &Requirements) -> Result<Architecture>;
    async fn evaluate_architecture(&mut self, design: &Architecture) -> Result<Assessment>;
    async fn select_patterns(&mut self, context: &SystemContext) -> Result<Vec<DesignPattern>>;
    async fn recommend_stack(&mut self, constraints: &TechConstraints) -> Result<TechnologyStack>;
}
```

### Architectural Approaches

```yaml
design_methodologies:
  domain_driven_design:
    - Bounded contexts
    - Aggregates and entities
    - Ubiquitous language
    - Context mapping
    
  microservices:
    - Service boundaries
    - Communication patterns
    - Data ownership
    - Orchestration vs choreography
    
  event_driven:
    - Event sourcing
    - CQRS patterns
    - Message brokers
    - Event streams
    
  cloud_native:
    - Container design
    - Orchestration
    - Service mesh
    - Observability
```

### Key Behaviors
- **Strategic Thinking**: Big-picture system design
- **Pattern Recognition**: Applying proven solutions
- **Trade-off Analysis**: Balancing competing concerns
- **Risk Assessment**: Identifying architectural risks
- **Documentation**: Clear architectural communication

## Coder Mode

### Purpose and Capabilities

The Coder transforms designs into working code, focusing on clean implementation and best practices.

```rust
// Core Coder trait
pub trait CoderMode: Send + Sync {
    async fn implement_feature(&mut self, spec: &FeatureSpec) -> Result<Implementation>;
    async fn generate_code(&mut self, template: &CodeTemplate) -> Result<GeneratedCode>;
    async fn refactor_code(&mut self, target: &RefactorTarget) -> Result<RefactoredCode>;
    async fn batch_implement(&mut self, specs: Vec<FeatureSpec>) -> Result<Vec<Implementation>>;
}
```

### Implementation Patterns

```yaml
coding_strategies:
  test_driven_development:
    - Write tests first
    - Red-green-refactor cycle
    - High test coverage
    - Continuous validation
    
  clean_code_practices:
    - SOLID principles
    - DRY/KISS
    - Meaningful names
    - Small functions
    - Clear abstractions
    
  batch_operations:
    - Parallel file generation
    - Bulk refactoring
    - Mass updates
    - Efficient workflows
```

### Key Behaviors
- **Implementation Focus**: Turning specs into code
- **Quality Emphasis**: Clean, maintainable code
- **Tool Mastery**: Efficient use of development tools
- **Testing Discipline**: Comprehensive test coverage
- **Performance Awareness**: Efficient implementations

## Integration Workflow

The Architect and Coder modes work together seamlessly:

```mermaid
graph LR
    R[Requirements] --> A[Architect]
    A -->|Design & Patterns| C[Coder]
    C -->|Implementation| T[Testing]
    T -->|Feedback| A
    C -->|Code| Rev[Reviewer]
```

### Typical Development Flow

```yaml
development_workflow:
  1_architecture_phase:
    actor: Architect
    activities:
      - Analyze requirements
      - Design system architecture
      - Select design patterns
      - Choose technology stack
      - Document decisions
      
  2_implementation_phase:
    actor: Coder
    activities:
      - Review architecture docs
      - Implement components
      - Write tests
      - Create integrations
      - Document code
      
  3_iteration_phase:
    actors: [Architect, Coder]
    activities:
      - Review implementation
      - Refine architecture
      - Optimize code
      - Enhance features
```

## Shared Best Practices

### For Both Modes

1. **Clear Communication**: Document decisions and rationale
2. **Iterative Approach**: Refine through feedback cycles
3. **Quality Focus**: Maintain high standards
4. **Tool Efficiency**: Use batch operations and automation
5. **Knowledge Sharing**: Update shared memory

### Anti-Patterns to Avoid

```yaml
common_pitfalls:
  architect_pitfalls:
    - Over-engineering solutions
    - Ivory tower syndrome
    - Technology bias
    - Documentation lag
    
  coder_pitfalls:
    - Cowboy coding
    - Copy-paste programming
    - Ignoring architecture
    - Skipping tests
    
  collaboration_pitfalls:
    - Poor handoffs
    - Misaligned understanding
    - Lack of feedback
    - Working in silos
```

## Tool Integration

### Shared Tool Usage

```yaml
development_tools:
  Memory:
    architect: "Store design decisions and patterns"
    coder: "Access implementation guidelines"
    
  TodoWrite:
    architect: "Track design tasks"
    coder: "Manage implementation tasks"
    
  Task:
    architect: "Parallel design exploration"
    coder: "Batch code generation"
```

## Advanced Features

### Architecture-Aware Coding

```rust
// Coder that follows architectural patterns
pub struct ArchitectureAwareCoder {
    architecture: Architecture,
    patterns: Vec<DesignPattern>,
    
    pub async fn implement_with_patterns(
        &mut self,
        feature: &Feature,
    ) -> Result<Implementation> {
        let applicable_patterns = self.select_patterns(feature)?;
        let code = self.generate_pattern_based_code(
            feature,
            &applicable_patterns
        )?;
        Ok(Implementation { code, patterns: applicable_patterns })
    }
}
```

### Evolutionary Architecture Support

```rust
// Architect supporting system evolution
pub struct EvolutionaryArchitect {
    current_architecture: Architecture,
    fitness_functions: Vec<FitnessFunction>,
    
    pub async fn evolve_architecture(
        &mut self,
        changing_requirements: &Requirements,
    ) -> Result<Evolution> {
        let fitness_scores = self.evaluate_fitness()?;
        let evolution_path = self.plan_evolution(
            &fitness_scores,
            changing_requirements
        )?;
        Ok(evolution_path)
    }
}
```

## Success Metrics

### Architect Success
1. **Design Quality**: Robust, scalable architectures
2. **Clarity**: Well-documented decisions
3. **Feasibility**: Implementable designs
4. **Alignment**: Meeting business requirements
5. **Maintainability**: Long-term sustainability

### Coder Success
1. **Code Quality**: Clean, efficient implementations
2. **Test Coverage**: Comprehensive testing
3. **Performance**: Meeting speed requirements
4. **Reliability**: Stable, bug-free code
5. **Velocity**: Rapid feature delivery

## Activation Commands

```bash
# Architect mode
sparc --mode architect "Design microservices architecture"
sparc --mode architect "Plan database schema"

# Coder mode
sparc --mode coder "Implement user authentication"
sparc --mode coder "Refactor payment module"

# Combined workflow
sparc "Design and implement notification system"
```

## Quick Reference

| Aspect | Architect | Coder |
|--------|-----------|-------|
| Focus | Design & Strategy | Implementation |
| Outputs | Diagrams, Specs, ADRs | Code, Tests, Docs |
| Thinking | Abstract, Strategic | Concrete, Tactical |
| Tools | Modeling, Diagramming | IDEs, Compilers |
| Success | Quality Design | Working Code |

## Memory Patterns

### Shared Development Knowledge

```json
{
  "development_memory": {
    "architectural_decisions": {
      "patterns": "Proven design patterns",
      "anti_patterns": "What to avoid",
      "decisions": "ADRs and rationale"
    },
    "implementation_patterns": {
      "code_templates": "Reusable implementations",
      "best_practices": "Coding standards",
      "performance_tips": "Optimization techniques"
    },
    "project_context": {
      "domain_knowledge": "Business understanding",
      "technical_debt": "Known issues",
      "future_plans": "Roadmap items"
    }
  }
}
```

Remember: Architect and Coder modes are complementary. Great software emerges from thoughtful design (Architect) and skillful implementation (Coder). Use them together for optimal results.