# Design-Documentation Group - CLAUDE Configuration

## Group Overview

The Design-Documentation Group unifies three creative and optimization-focused SPARC modes:

- **Designer Mode**: System architecture and user experience design
- **Documenter Mode**: Comprehensive documentation and knowledge capture
- **Optimizer Mode**: Performance enhancement and resource optimization

These modes share coordination protocols and execution models while specializing in transforming systems through design, documentation, and optimization.

## Mode Selection

Choose the appropriate mode based on your primary objective:

```yaml
mode_selection_guide:
  use_designer_when:
    - Creating system architectures
    - Designing user interfaces
    - Planning component interactions
    - Establishing design patterns
    
  use_documenter_when:
    - Creating technical documentation
    - Building knowledge bases
    - Generating API references
    - Capturing system behavior
    
  use_optimizer_when:
    - Improving performance
    - Reducing resource usage
    - Enhancing algorithms
    - Streamlining workflows
```

## Shared Architecture

All modes in this group utilize:
- Common coordination protocols
- Unified execution models
- Shared optimization strategies
- Integrated semantic architectures

Refer to the group-level documentation:
- `coordination-protocols.md` - Multi-mode coordination patterns
- `execution-models.md` - Runtime behavior frameworks
- `optimization-strategies.md` - Performance and efficiency patterns
- `semantic-architecture.md` - Conceptual design models

## Mode-Specific Behaviors

### Designer Mode

**Primary Role**: Create elegant system architectures and user experiences

```json
{
  "designer_configuration": {
    "core_capabilities": [
      "system_architecture_design",
      "ui_ux_creation",
      "component_modeling",
      "interaction_design",
      "pattern_establishment"
    ],
    "design_approaches": {
      "top_down": "Start from high-level requirements",
      "bottom_up": "Build from component capabilities",
      "iterative": "Refine through feedback cycles",
      "pattern_based": "Apply proven design patterns"
    },
    "deliverables": [
      "architecture_diagrams",
      "component_specifications",
      "interaction_flows",
      "design_patterns",
      "style_guides"
    ]
  }
}
```

### Documenter Mode

**Primary Role**: Transform system knowledge into accessible documentation

```json
{
  "documenter_configuration": {
    "core_capabilities": [
      "automated_documentation_generation",
      "knowledge_extraction",
      "api_documentation",
      "tutorial_creation",
      "reference_compilation"
    ],
    "documentation_types": {
      "technical": "Code comments, API refs, architecture docs",
      "user_facing": "Guides, tutorials, FAQs",
      "operational": "Runbooks, deployment guides",
      "conceptual": "Design rationale, theory of operation"
    },
    "quality_standards": [
      "completeness",
      "accuracy",
      "clarity",
      "maintainability",
      "searchability"
    ]
  }
}
```

### Optimizer Mode

**Primary Role**: Enhance system performance and efficiency

```json
{
  "optimizer_configuration": {
    "core_capabilities": [
      "performance_profiling",
      "bottleneck_identification",
      "algorithm_optimization",
      "resource_reduction",
      "workflow_streamlining"
    ],
    "optimization_domains": {
      "performance": "Speed, latency, throughput",
      "resources": "Memory, CPU, storage, network",
      "cost": "Computational, operational, financial",
      "quality": "Accuracy, reliability, maintainability"
    },
    "techniques": [
      "algorithmic_improvements",
      "caching_strategies",
      "parallel_processing",
      "code_optimization",
      "architectural_refactoring"
    ]
  }
}
```

## Cross-Mode Coordination

The modes collaborate in design-optimize-document cycles:

```mermaid
graph TD
    D[Designer] -->|Architecture| O[Optimizer]
    O -->|Optimized Design| Doc[Documenter]
    Doc -->|Feedback| D
    
    D -->|Design Specs| C[Coder]
    O -->|Performance Requirements| A[Analyzer]
    Doc -->|Documentation| R[Reviewer]
```

## Integrated Workflows

### Common Patterns

```yaml
design_optimization_cycle:
  1. designer: "Create initial architecture"
  2. optimizer: "Identify performance bottlenecks"
  3. designer: "Refine design for optimization"
  4. documenter: "Document optimized architecture"
  5. reviewer: "Validate design decisions"

documentation_driven_development:
  1. documenter: "Create specification documents"
  2. designer: "Design based on specifications"
  3. coder: "Implement from design"
  4. documenter: "Update with implementation details"
  5. optimizer: "Enhance based on documentation insights"

continuous_improvement:
  1. analyzer: "Identify improvement opportunities"
  2. designer: "Propose architectural changes"
  3. optimizer: "Validate performance gains"
  4. documenter: "Capture lessons learned"
```

## Tool Utilization

### Mode-Specific Tool Usage

```yaml
shared_tools:
  Read:
    designer: "Analyze existing architectures"
    documenter: "Extract code documentation"
    optimizer: "Profile current performance"
    
  Write:
    designer: "Create design documents"
    documenter: "Generate documentation files"
    optimizer: "Output optimization reports"
    
  Edit:
    designer: "Update design patterns"
    documenter: "Refine documentation"
    optimizer: "Apply optimizations"
    
  Task:
    designer: "Parallel design exploration"
    documenter: "Distributed doc generation"
    optimizer: "Concurrent optimization runs"
```

## Memory Integration

### Shared Knowledge Patterns

```json
{
  "design_documentation_memory": {
    "design_patterns": {
      "owner": "designer",
      "content": "Reusable architectures and components",
      "consumers": ["coder", "optimizer", "documenter"]
    },
    "optimization_catalog": {
      "owner": "optimizer",
      "content": "Proven optimization techniques",
      "consumers": ["designer", "coder", "analyzer"]
    },
    "documentation_templates": {
      "owner": "documenter",
      "content": "Reusable documentation structures",
      "consumers": ["all_modes"]
    },
    "style_guides": {
      "shared_ownership": true,
      "content": "Design and documentation standards",
      "maintainers": ["designer", "documenter"]
    }
  }
}
```

## Best Practices

### Effective Mode Coordination

1. **Design First**: Start with designer mode for new features
2. **Optimize Iteratively**: Use optimizer throughout development
3. **Document Continuously**: Keep documentation current
4. **Validate Designs**: Use analyzer/tester modes for validation

### Quality Guidelines

```yaml
quality_standards:
  design:
    - Follow established patterns
    - Consider scalability upfront
    - Design for testability
    - Include error handling
    
  documentation:
    - Write for your audience
    - Include examples
    - Keep it current
    - Make it searchable
    
  optimization:
    - Measure before optimizing
    - Preserve correctness
    - Document trade-offs
    - Consider maintainability
```

## Activation Commands

```bash
# Direct mode activation
sparc --mode designer "Design user authentication system"
sparc --mode documenter "Generate API documentation"
sparc --mode optimizer "Optimize database queries"

# Integrated workflows
sparc "Redesign and optimize the payment flow"  # Uses all three modes
sparc "Document the system architecture"  # Designer + Documenter
```

## Performance Considerations

- **Designer**: Minimal resource usage, mainly cognitive load
- **Documenter**: I/O intensive when processing large codebases
- **Optimizer**: CPU/memory intensive during analysis phases

## Output Formats

### Mode-Specific Outputs

```yaml
designer_outputs:
  - architecture_diagrams: "Mermaid, PlantUML"
  - component_specs: "JSON, YAML"
  - design_docs: "Markdown, AsciiDoc"
  
documenter_outputs:
  - api_docs: "OpenAPI, Swagger"
  - user_guides: "Markdown, HTML"
  - code_comments: "JSDoc, PyDoc"
  
optimizer_outputs:
  - performance_reports: "JSON metrics"
  - optimization_plans: "Markdown"
  - benchmark_results: "CSV, charts"
```

## Quick Reference

| Mode | Primary Focus | Key Activities | Typical Outputs |
|------|--------------|----------------|-----------------|
| Designer | Architecture & UX | Modeling, Planning | Diagrams, Specs |
| Documenter | Knowledge Capture | Writing, Organizing | Docs, Guides |
| Optimizer | Performance | Analysis, Enhancement | Reports, Code |

## Integration Points

The Design-Documentation Group integrates with:
- **Analysis-Testing Group**: Validate designs and optimizations
- **Development Modes**: Implement designs and optimizations
- **Coordination Modes**: Orchestrate complex workflows

Remember: These modes complement each other. Great systems emerge from thoughtful design, thorough documentation, and continuous optimization.