# State Transitions

## Review Lifecycle States

### State Diagram
```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> Initializing: Review Request
    Initializing --> ContextGathering: Init Complete
    Initializing --> Failed: Init Error
    ContextGathering --> Analyzing: Context Ready
    ContextGathering --> WaitingForInput: Missing Context
    WaitingForInput --> ContextGathering: Input Received
    WaitingForInput --> Failed: Timeout
    Analyzing --> GeneratingReport: Analysis Complete
    Analyzing --> PartialComplete: Resource Limit
    GeneratingReport --> Completed: Report Ready
    GeneratingReport --> Failed: Generation Error
    PartialComplete --> Completed: Finalize
    Completed --> Idle: Reset
    Failed --> Idle: Reset
```

### State Definitions

```rust
#[derive(Debug, Clone)]
pub enum ReviewerState {
    Idle {
        ready_since: Timestamp,
    },
    Initializing {
        request_id: RequestId,
        started_at: Timestamp,
    },
    ContextGathering {
        request_id: RequestId,
        progress: ContextProgress,
    },
    WaitingForInput {
        request_id: RequestId,
        needed_inputs: Vec<InputRequirement>,
        timeout_at: Timestamp,
    },
    Analyzing {
        request_id: RequestId,
        current_file: Option<FilePath>,
        files_completed: usize,
        total_files: usize,
        findings: Vec<Finding>,
    },
    GeneratingReport {
        request_id: RequestId,
        format: ReportFormat,
        progress: f32,
    },
    PartialComplete {
        request_id: RequestId,
        reason: PartialReason,
        results: PartialResults,
    },
    Completed {
        request_id: RequestId,
        report: ReviewReport,
        metrics: ReviewMetrics,
    },
    Failed {
        request_id: RequestId,
        error: ReviewError,
        recoverable: bool,
    },
}
```

## Transition Rules

### Valid Transitions
```rust
impl ReviewerState {
    pub fn can_transition_to(&self, target: &ReviewerState) -> bool {
        match (self, target) {
            (Idle { .. }, Initializing { .. }) => true,
            (Initializing { .. }, ContextGathering { .. }) => true,
            (Initializing { .. }, Failed { .. }) => true,
            (ContextGathering { .. }, Analyzing { .. }) => true,
            (ContextGathering { .. }, WaitingForInput { .. }) => true,
            (WaitingForInput { .. }, ContextGathering { .. }) => true,
            (WaitingForInput { .. }, Failed { .. }) => true,
            (Analyzing { .. }, GeneratingReport { .. }) => true,
            (Analyzing { .. }, PartialComplete { .. }) => true,
            (GeneratingReport { .. }, Completed { .. }) => true,
            (GeneratingReport { .. }, Failed { .. }) => true,
            (PartialComplete { .. }, Completed { .. }) => true,
            (Completed { .. }, Idle { .. }) => true,
            (Failed { .. }, Idle { .. }) => true,
            _ => false,
        }
    }
}
```

### Transition Events
```rust
pub enum TransitionEvent {
    ReviewRequested { request: ReviewRequest },
    InitializationComplete { context: InitialContext },
    InitializationFailed { error: Error },
    ContextReady { full_context: ReviewContext },
    InputNeeded { requirements: Vec<InputRequirement> },
    InputProvided { inputs: HashMap<String, Value> },
    InputTimeout,
    FileAnalyzed { file: FilePath, findings: Vec<Finding> },
    AnalysisComplete { total_findings: usize },
    ResourceLimitReached { limit_type: ResourceLimit },
    ReportGenerated { report: ReviewReport },
    ReportGenerationFailed { error: Error },
    PartialFinalized,
    ReviewReset,
}
```

## State Transition Handlers

### Pre-Transition Validation
```rust
pub trait TransitionValidator {
    fn validate_transition(
        &self,
        from: &ReviewerState,
        to: &ReviewerState,
        event: &TransitionEvent,
    ) -> Result<(), TransitionError>;
}
```

### Post-Transition Actions
```rust
pub struct TransitionHandler {
    pub async fn handle_transition(
        &mut self,
        old_state: ReviewerState,
        new_state: ReviewerState,
        event: TransitionEvent,
    ) -> Result<()> {
        match (&old_state, &new_state) {
            (Idle { .. }, Initializing { .. }) => {
                self.start_review_session().await?;
            }
            (Analyzing { findings, .. }, GeneratingReport { .. }) => {
                self.prepare_report_generation(findings).await?;
            }
            (_, Failed { error, .. }) => {
                self.handle_failure(error).await?;
            }
            _ => {}
        }
        
        self.emit_state_change(old_state, new_state, event).await
    }
}
```

## State Persistence

### State Serialization
```rust
impl ReviewerState {
    pub fn serialize(&self) -> Result<Vec<u8>> {
        bincode::serialize(self)
    }
    
    pub fn deserialize(data: &[u8]) -> Result<Self> {
        bincode::deserialize(data)
    }
}
```

### Recovery Mechanisms
- **State checkpointing**: Regular state snapshots
- **Transaction log**: Event sourcing for state reconstruction
- **Partial state recovery**: Resuming from intermediate states
- **Graceful degradation**: Handling corrupted states

## Concurrent State Management

### State Synchronization
```rust
pub struct StateManager {
    state: Arc<RwLock<ReviewerState>>,
    transition_lock: Arc<Mutex<()>>,
    observers: Vec<Box<dyn StateObserver>>,
}

impl StateManager {
    pub async fn transition(&self, event: TransitionEvent) -> Result<ReviewerState> {
        let _lock = self.transition_lock.lock().await;
        
        let current = self.state.read().await.clone();
        let new_state = self.compute_next_state(&current, &event)?;
        
        self.validate_transition(&current, &new_state, &event)?;
        
        *self.state.write().await = new_state.clone();
        self.notify_observers(&current, &new_state, &event).await;
        
        Ok(new_state)
    }
}
```

### State Monitoring
- **Transition metrics**: Tracking state changes
- **State duration tracking**: Time spent in each state
- **Transition failure monitoring**: Identifying problematic transitions
- **State history maintenance**: Audit trail of state changes