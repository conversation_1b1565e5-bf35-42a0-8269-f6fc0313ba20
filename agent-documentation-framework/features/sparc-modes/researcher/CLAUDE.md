# Researcher Mode

## Purpose and Use Cases

The Researcher mode specializes in information gathering, analysis, and knowledge synthesis. Researcher agents excel at exploring domains, discovering insights, and building comprehensive knowledge bases through advanced memory integration and persistent learning.

### Primary Use Cases
- Technology research and evaluation
- Best practices discovery
- Domain knowledge exploration
- Competitive analysis
- Literature review and synthesis

## Rust Code Examples

### SPARC Researcher Mode Trait Definition

```rust
// Example: Researcher Mode trait for SPARC system
pub trait ResearcherMode: Send + Sync {
    fn name(&self) -> &'static str { "Researcher" }
    fn description(&self) -> &'static str { 
        "Information gathering and knowledge synthesis mode with memory integration"
    }
    
    // Core research methods
    async fn research_topic(&mut self, topic: &ResearchTopic) -> Result<ResearchFindings, ModeError>;
    async fn synthesize_knowledge(&mut self, sources: Vec<Source>) -> Result<KnowledgeSynthesis, ModeError>;
    async fn evaluate_technologies(&mut self, techs: Vec<Technology>) -> Result<TechEvaluation, ModeError>;
    
    // Memory integration methods
    async fn store_findings(&mut self, findings: &ResearchFindings) -> Result<MemoryKey, ModeError>;
    async fn retrieve_context(&mut self, topic: &str) -> Result<ResearchContext, ModeError>;
}

// Research data structures
#[derive(Debug, Clone)]
pub struct ResearchFindings {
    pub topic: String,
    pub sources: Vec<VerifiedSource>,
    pub insights: Vec<Insight>,
    pub recommendations: Vec<Recommendation>,
    pub knowledge_graph: KnowledgeGraph,
}

#[derive(Debug, Clone)]
pub enum ResearchStrategy {
    BreadthFirst {
        max_sources: usize,
        diversity_threshold: f64,
    },
    DepthFirst {
        focus_areas: Vec<String>,
        detail_level: DetailLevel,
    },
    Comparative {
        subjects: Vec<String>,
        criteria: Vec<ComparisonCriteria>,
    },
    Exploratory {
        starting_points: Vec<String>,
        discovery_depth: usize,
    },
    MemoryAugmented {
        context_keys: Vec<MemoryKey>,
        learning_rate: f64,
    },
}
```

### Researcher State Machine

```rust
// State machine for research process
#[derive(Debug, Clone)]
pub enum ResearcherState {
    Initializing {
        research_topic: ResearchTopic,
        memory_context: Option<ResearchContext>,
    },
    GatheringSources {
        search_queries: Vec<SearchQuery>,
        collected_sources: Vec<Source>,
    },
    AnalyzingContent {
        sources: Vec<VerifiedSource>,
        extraction_progress: f64,
    },
    SynthesizingKnowledge {
        raw_insights: Vec<RawInsight>,
        knowledge_structures: Vec<KnowledgeStructure>,
    },
    ValidatingFindings {
        preliminary_findings: PreliminaryFindings,
        validation_criteria: Vec<ValidationCriterion>,
    },
    StoringKnowledge {
        validated_findings: ValidatedFindings,
        memory_updates: Vec<MemoryUpdate>,
    },
    Complete {
        research_report: ResearchReport,
        memory_keys: Vec<MemoryKey>,
    },
}

impl ResearcherState {
    pub fn transition(&mut self, event: ResearchEvent) -> Result<(), StateError> {
        match (self.clone(), event) {
            (ResearcherState::Initializing { research_topic, memory_context }, 
             ResearchEvent::ContextLoaded) => {
                *self = ResearcherState::GatheringSources {
                    search_queries: Self::generate_queries(&research_topic, &memory_context),
                    collected_sources: Vec::new(),
                };
                Ok(())
            }
            (ResearcherState::GatheringSources { collected_sources, .. }, 
             ResearchEvent::SourcesCollected) => {
                *self = ResearcherState::AnalyzingContent {
                    sources: Self::verify_sources(collected_sources),
                    extraction_progress: 0.0,
                };
                Ok(())
            }
            // ... other transitions
            _ => Err(StateError::InvalidTransition),
        }
    }
}
```

## Key Behaviors and Characteristics

### Core Behaviors
- **Systematic Exploration**: Methodical information gathering
- **Critical Analysis**: Evaluating source credibility
- **Pattern Recognition**: Identifying trends and insights
- **Knowledge Synthesis**: Combining disparate information
- **Memory Integration**: Building on past research

### Unique Characteristics
- Strong analytical and synthesis skills
- Ability to evaluate source reliability
- Cross-domain connection making
- Persistent knowledge building
- Effective summarization capabilities

## When to Use This Mode

Deploy Researcher agents when:
- Exploring new technologies or domains
- Gathering competitive intelligence
- Building knowledge bases
- Evaluating technical solutions
- Discovering best practices

## Integration Points

### Works Well With
- **Memory Manager**: Stores and retrieves research
- **Analyzer**: Provides data analysis support
- **Documenter**: Creates research documentation
- **Architect**: Informs design decisions
- **Orchestrator**: Coordinates research efforts

### Communication Patterns
- Receives research requests from orchestrators
- Stores findings in memory for persistence
- Shares insights with architects and coders
- Collaborates with analyzers on data
- Updates documentation with findings

## Success Criteria

Researcher success is measured by:
1. **Insight Quality**: Valuable findings discovered
2. **Source Credibility**: Reliable information used
3. **Synthesis Depth**: Comprehensive understanding
4. **Memory Integration**: Knowledge persistence
5. **Actionability**: Practical recommendations

## Best Practices

1. Verify source credibility systematically
2. Use memory to build on past research
3. Cross-reference multiple sources
4. Document research methodology
5. Create actionable recommendations
6. Update knowledge graphs regularly

## Anti-Patterns to Avoid

- Confirmation Bias: Seek diverse viewpoints
- Source Overload: Quality over quantity
- Shallow Research: Go beyond surface level
- Memory Silos: Connect related research
- Analysis Paralysis: Know when to conclude
- Outdated Information: Verify currency

## Research Techniques

The Researcher mode employs:
- **Literature Review**: Academic and industry sources
- **Comparative Analysis**: Side-by-side evaluation
- **Technology Scouting**: Emerging trend identification
- **Expert Synthesis**: Combining authoritative views
- **Pattern Mining**: Discovering hidden connections
- **Knowledge Mapping**: Visual relationship modeling

## Memory Integration Features

Leverages memory for:
- Historical research context
- Evolution of technology trends
- Previously validated sources
- Research methodology patterns
- Domain expertise accumulation
- Cross-project knowledge transfer

## Research Outputs

Researchers produce:
- Comprehensive research reports
- Technology evaluation matrices
- Best practice compilations
- Knowledge graphs and maps
- Trend analysis documents
- Actionable recommendation lists
- Updated memory entries

The Researcher mode provides the deep investigation and knowledge synthesis capabilities needed for informed decision-making and continuous learning.

## Advanced Research Patterns

### Memory-Augmented Research System

```rust
// Example: Research with persistent memory integration
pub struct MemoryAugmentedResearcher {
    base_researcher: Box<dyn ResearcherMode>,
    memory_store: Arc<MemoryStore>,
    knowledge_graph: KnowledgeGraph,
    learning_engine: LearningEngine,
}

impl MemoryAugmentedResearcher {
    pub async fn research_with_memory(
        &mut self,
        topic: &ResearchTopic,
    ) -> Result<AugmentedResearchFindings, ResearchError> {
        // Load relevant context from memory
        let memory_context = self.memory_store
            .query_related_research(topic)
            .await?;
            
        // Extract learned patterns
        let research_patterns = self.learning_engine
            .extract_patterns(&memory_context)
            .await?;
            
        // Conduct research with context
        let mut findings = self.base_researcher
            .research_topic(topic)
            .await?;
            
        // Augment with memory insights
        findings = self.augment_with_memory(&findings, &memory_context)?;
        
        // Update knowledge graph
        self.knowledge_graph.integrate_findings(&findings).await?;
        
        // Store new knowledge
        let memory_keys = self.store_incremental_knowledge(&findings).await?;
        
        // Learn from this research
        self.learning_engine.learn_from(&findings).await?;
        
        Ok(AugmentedResearchFindings {
            core_findings: findings,
            memory_connections: self.find_connections(&findings, &memory_context),
            learned_insights: self.learning_engine.get_insights(),
            knowledge_evolution: self.track_knowledge_evolution(topic),
            memory_keys,
        })
    }
    
    async fn store_incremental_knowledge(
        &mut self,
        findings: &ResearchFindings,
    ) -> Result<Vec<MemoryKey>, ResearchError> {
        let mut keys = Vec::new();
        
        // Store main findings
        keys.push(
            self.memory_store.store(
                "research_findings",
                &findings.to_memory_format()
            ).await?
        );
        
        // Store individual insights
        for insight in &findings.insights {
            if insight.significance > 0.7 {
                keys.push(
                    self.memory_store.store(
                        "research_insight",
                        &InsightMemory {
                            insight: insight.clone(),
                            topic: findings.topic.clone(),
                            timestamp: Utc::now(),
                            connections: self.knowledge_graph.find_connections(&insight.keywords),
                        }
                    ).await?
                );
            }
        }
        
        Ok(keys)
    }
}
```

### Multi-Source Knowledge Synthesis

```rust
// Example: Advanced knowledge synthesis from multiple sources
pub struct KnowledgeSynthesizer {
    source_evaluator: SourceEvaluator,
    conflict_resolver: ConflictResolver,
    insight_extractor: InsightExtractor,
    synthesis_engine: SynthesisEngine,
}

#[derive(Debug, Clone)]
pub struct SynthesisResult {
    pub unified_knowledge: UnifiedKnowledge,
    pub source_contributions: HashMap<SourceId, Contribution>,
    pub conflicts_resolved: Vec<ConflictResolution>,
    pub confidence_scores: HashMap<String, f64>,
    pub synthesis_metadata: SynthesisMetadata,
}

impl KnowledgeSynthesizer {
    pub async fn synthesize_knowledge(
        &mut self,
        sources: Vec<Source>,
        synthesis_goals: &SynthesisGoals,
    ) -> Result<SynthesisResult, SynthesisError> {
        // Evaluate source credibility
        let evaluated_sources = self.source_evaluator
            .evaluate_sources(&sources)
            .await?;
            
        // Extract insights from each source
        let mut source_insights = HashMap::new();
        for source in &evaluated_sources {
            let insights = self.insight_extractor
                .extract_insights(source)
                .await?;
            source_insights.insert(source.id.clone(), insights);
        }
        
        // Identify and resolve conflicts
        let conflicts = self.identify_conflicts(&source_insights)?;
        let resolutions = self.conflict_resolver
            .resolve_conflicts(&conflicts, &evaluated_sources)
            .await?;
            
        // Synthesize unified knowledge
        let unified = self.synthesis_engine
            .synthesize(&source_insights, &resolutions, synthesis_goals)
            .await?;
            
        // Calculate confidence scores
        let confidence_scores = self.calculate_confidence(&unified, &evaluated_sources);
        
        Ok(SynthesisResult {
            unified_knowledge: unified,
            source_contributions: self.track_contributions(&source_insights),
            conflicts_resolved: resolutions,
            confidence_scores,
            synthesis_metadata: self.create_metadata(&sources, synthesis_goals),
        })
    }
    
    fn identify_conflicts(
        &self,
        insights: &HashMap<SourceId, Vec<Insight>>,
    ) -> Result<Vec<KnowledgeConflict>, SynthesisError> {
        let mut conflicts = Vec::new();
        
        // Compare insights across sources
        for (source1, insights1) in insights {
            for (source2, insights2) in insights {
                if source1 < source2 {
                    for insight1 in insights1 {
                        for insight2 in insights2 {
                            if let Some(conflict) = self.detect_conflict(insight1, insight2) {
                                conflicts.push(KnowledgeConflict {
                                    source1: source1.clone(),
                                    source2: source2.clone(),
                                    conflicting_insights: (insight1.clone(), insight2.clone()),
                                    conflict_type: conflict,
                                });
                            }
                        }
                    }
                }
            }
        }
        
        Ok(conflicts)
    }
}
```

### Domain Expertise Builder

```rust
// Example: Building domain expertise over time
pub struct DomainExpertiseBuilder {
    expertise_store: ExpertiseStore,
    concept_mapper: ConceptMapper,
    relationship_analyzer: RelationshipAnalyzer,
    expertise_evaluator: ExpertiseEvaluator,
}

#[derive(Debug, Clone)]
pub struct DomainExpertise {
    pub domain: String,
    pub concept_graph: ConceptGraph,
    pub key_relationships: Vec<ConceptRelationship>,
    pub expertise_level: ExpertiseLevel,
    pub knowledge_gaps: Vec<KnowledgeGap>,
    pub learning_history: Vec<LearningEvent>,
}

impl DomainExpertiseBuilder {
    pub async fn build_expertise(
        &mut self,
        domain: &str,
        research_history: Vec<ResearchFindings>,
    ) -> Result<DomainExpertise, ExpertiseError> {
        // Map concepts from research
        let concepts = self.concept_mapper
            .extract_concepts(&research_history)
            .await?;
            
        // Build concept graph
        let concept_graph = self.build_concept_graph(&concepts).await?;
        
        // Analyze relationships
        let relationships = self.relationship_analyzer
            .analyze_relationships(&concept_graph)
            .await?;
            
        // Evaluate expertise level
        let expertise_level = self.expertise_evaluator
            .evaluate_expertise(&concept_graph, &relationships)
            .await?;
            
        // Identify knowledge gaps
        let gaps = self.identify_knowledge_gaps(&concept_graph, domain).await?;
        
        // Create learning history
        let learning_history = self.create_learning_timeline(&research_history);
        
        Ok(DomainExpertise {
            domain: domain.to_string(),
            concept_graph,
            key_relationships: relationships,
            expertise_level,
            knowledge_gaps: gaps,
            learning_history,
        })
    }
    
    pub async fn recommend_research_areas(
        &self,
        expertise: &DomainExpertise,
    ) -> Result<Vec<ResearchRecommendation>, ExpertiseError> {
        let mut recommendations = Vec::new();
        
        // Recommend based on knowledge gaps
        for gap in &expertise.knowledge_gaps {
            recommendations.push(ResearchRecommendation {
                topic: gap.missing_concept.clone(),
                priority: gap.importance,
                rationale: format!("Fill knowledge gap in {}", gap.area),
                estimated_impact: gap.potential_impact,
            });
        }
        
        // Recommend based on weak relationships
        for relationship in &expertise.key_relationships {
            if relationship.strength < 0.5 {
                recommendations.push(ResearchRecommendation {
                    topic: format!("{} <-> {} relationship", 
                        relationship.concept_a, relationship.concept_b),
                    priority: 0.7,
                    rationale: "Strengthen understanding of relationship",
                    estimated_impact: 0.6,
                });
            }
        }
        
        Ok(recommendations)
    }
}
```

### Research Pattern Learning

```rust
// Example: Learning and applying research patterns
pub struct ResearchPatternLearner {
    pattern_extractor: PatternExtractor,
    pattern_store: PatternStore,
    pattern_matcher: PatternMatcher,
    effectiveness_tracker: EffectivenessTracker,
}

#[derive(Debug, Clone)]
pub struct ResearchPattern {
    pub pattern_type: ResearchPatternType,
    pub context_conditions: Vec<ContextCondition>,
    pub methodology_steps: Vec<MethodologyStep>,
    pub expected_outcomes: Vec<ExpectedOutcome>,
    pub effectiveness_score: f64,
    pub usage_count: usize,
}

impl ResearchPatternLearner {
    pub async fn learn_from_research(
        &mut self,
        completed_research: &CompletedResearch,
    ) -> Result<Vec<LearnedPattern>, LearningError> {
        // Extract patterns from research
        let patterns = self.pattern_extractor
            .extract_patterns(completed_research)
            .await?;
            
        // Evaluate effectiveness
        let effectiveness = self.effectiveness_tracker
            .evaluate_research_effectiveness(completed_research)
            .await?;
            
        // Store successful patterns
        let mut learned_patterns = Vec::new();
        
        for pattern in patterns {
            if effectiveness > 0.7 {
                let stored_pattern = self.pattern_store
                    .store_pattern(&pattern, effectiveness)
                    .await?;
                learned_patterns.push(stored_pattern);
            }
        }
        
        Ok(learned_patterns)
    }
    
    pub async fn apply_learned_patterns(
        &self,
        research_context: &ResearchContext,
    ) -> Result<ResearchStrategy, LearningError> {
        // Find matching patterns
        let matching_patterns = self.pattern_matcher
            .find_matching_patterns(research_context)
            .await?;
            
        // Select best pattern
        let best_pattern = matching_patterns
            .into_iter()
            .max_by(|a, b| {
                let score_a = a.effectiveness_score * a.relevance;
                let score_b = b.effectiveness_score * b.relevance;
                score_a.partial_cmp(&score_b).unwrap()
            })
            .ok_or(LearningError::NoMatchingPattern)?;
            
        // Create research strategy
        Ok(self.pattern_to_strategy(&best_pattern))
    }
}
```

### Collaborative Research Coordination

```rust
// Example: Coordinating research across multiple agents
pub struct CollaborativeResearchCoordinator {
    researcher_pool: Vec<Box<dyn ResearcherMode>>,
    task_distributor: TaskDistributor,
    result_aggregator: ResultAggregator,
    conflict_mediator: ConflictMediator,
}

impl CollaborativeResearchCoordinator {
    pub async fn coordinate_research(
        &mut self,
        research_project: ResearchProject,
    ) -> Result<CollaborativeResearchResult, CoordinationError> {
        // Break down research project
        let research_tasks = self.decompose_project(&research_project)?;
        
        // Distribute tasks to researchers
        let task_assignments = self.task_distributor
            .distribute_tasks(&research_tasks, &self.researcher_pool)
            .await?;
            
        // Execute research in parallel
        let mut research_results = Vec::new();
        let mut handles = Vec::new();
        
        for (researcher, tasks) in task_assignments {
            let handle = tokio::spawn(async move {
                let mut results = Vec::new();
                for task in tasks {
                    let result = researcher.research_topic(&task).await?;
                    results.push(result);
                }
                Ok::<Vec<ResearchFindings>, ResearchError>(results)
            });
            handles.push(handle);
        }
        
        // Collect results
        for handle in handles {
            research_results.extend(handle.await??);
        }
        
        // Aggregate findings
        let aggregated = self.result_aggregator
            .aggregate_findings(&research_results)
            .await?;
            
        // Resolve conflicts
        let final_findings = self.conflict_mediator
            .mediate_conflicts(&aggregated)
            .await?;
            
        Ok(CollaborativeResearchResult {
            unified_findings: final_findings,
            individual_contributions: research_results,
            collaboration_metrics: self.calculate_collaboration_metrics(&task_assignments),
        })
    }
}
```

### Research Quality Assurance

```rust
// Example: Ensuring research quality and validity
pub struct ResearchQualityAssurance {
    fact_checker: FactChecker,
    citation_validator: CitationValidator,
    bias_detector: BiasDetector,
    peer_reviewer: PeerReviewer,
}

impl ResearchQualityAssurance {
    pub async fn validate_research(
        &mut self,
        research: &ResearchFindings,
    ) -> Result<QualityReport, QAError> {
        // Fact check claims
        let fact_check_results = self.fact_checker
            .verify_facts(&research.claims)
            .await?;
            
        // Validate citations
        let citation_validity = self.citation_validator
            .validate_sources(&research.sources)
            .await?;
            
        // Detect potential biases
        let bias_analysis = self.bias_detector
            .analyze_bias(&research)
            .await?;
            
        // Peer review simulation
        let peer_review = self.peer_reviewer
            .review_research(&research)
            .await?;
            
        Ok(QualityReport {
            fact_accuracy: fact_check_results.accuracy_score,
            citation_quality: citation_validity.quality_score,
            bias_assessment: bias_analysis,
            peer_review_feedback: peer_review,
            overall_quality_score: self.calculate_quality_score(
                &fact_check_results,
                &citation_validity,
                &bias_analysis,
                &peer_review
            ),
            improvement_suggestions: self.generate_improvements(&research),
        })
    }
}
```