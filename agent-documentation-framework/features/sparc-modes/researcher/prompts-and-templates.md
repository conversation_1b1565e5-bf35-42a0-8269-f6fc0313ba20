# Researcher Mode - Prompts and Templates

## System Prompts

### Core Researcher System Prompt
```typescript
const researcherSystemPrompt = `You are a thorough technology researcher. Your role is to:
- Gather comprehensive information from reliable sources
- Analyze options objectively with evidence-based reasoning
- Compare alternatives using clear criteria
- Provide actionable recommendations
- Document findings with proper citations
- Focus on practical, real-world applicability`;
```

### Extended Research Instructions
```typescript
const extendedInstructions = `When conducting research:
1. Start with clear research questions
2. Identify authoritative sources
3. Cross-reference information
4. Consider multiple perspectives
5. Evaluate trade-offs objectively
6. Provide confidence levels for findings
7. Include both benefits and limitations
8. Make practical recommendations`;
```

## Research Task Templates

### Technology Comparison Template
```typescript
const comparisonTemplate = `
Research Task: Compare {technology_a} vs {technology_b} for {use_case}

Analyze the following aspects:
1. Performance characteristics
2. Development experience
3. Community and ecosystem
4. Learning curve
5. Long-term viability
6. Cost implications
7. Integration capabilities

Provide:
- Feature comparison matrix
- Pros and cons for each
- Use case recommendations
- Migration considerations
`;
```

### Best Practices Research Template
```typescript
const bestPracticesTemplate = `
Research best practices for {topic}

Focus areas:
- Industry standards and conventions
- Common patterns and anti-patterns
- Security considerations
- Performance optimization techniques
- Scalability approaches
- Testing strategies
- Monitoring and observability

Include:
- Authoritative sources
- Real-world examples
- Implementation guidelines
- Common pitfalls to avoid
`;
```

## Domain-Specific Templates

### Architecture Research Template
```typescript
const architectureResearchTemplate = `
Research {architecture_pattern} for {application_type}

Investigate:
1. Pattern overview and principles
2. When to use (and when not to)
3. Implementation complexity
4. Scalability characteristics
5. Maintenance requirements
6. Team size considerations
7. Cost implications

Deliverables:
- Architecture diagram
- Component breakdown
- Communication patterns
- Deployment strategies
- Case study examples
`;
```

### Security Research Template
```typescript
const securityTemplate = `
Research security requirements for {system_type}

Areas to cover:
- Threat modeling and risk assessment
- Authentication and authorization approaches
- Data protection strategies
- Compliance requirements ({compliance_standards})
- Security testing methodologies
- Incident response planning
- Third-party security tools

Provide:
- Security checklist
- Implementation recommendations
- Tool comparisons
- Compliance mapping
`;
```

### Performance Research Template
```typescript
const performanceTemplate = `
Research performance optimization for {technology_stack}

Analyze:
1. Common performance bottlenecks
2. Benchmarking methodologies
3. Optimization techniques
4. Caching strategies
5. Database optimization
6. Frontend performance
7. Infrastructure considerations

Include:
- Benchmark results
- Before/after comparisons
- Tool recommendations
- Implementation priorities
`;
```

## Analysis Templates

### Framework Evaluation Template
```typescript
const frameworkEvalTemplate = `
Evaluate {framework_name} for {project_type}

Assessment criteria:
- Core features and capabilities
- Performance benchmarks
- Bundle size analysis
- Developer experience
- Ecosystem maturity
- Documentation quality
- Community support
- Enterprise readiness

Output format:
- Executive summary
- Detailed analysis
- Comparison with alternatives
- Recommendation with justification
`;
```

### Tool Research Template
```typescript
const toolResearchTemplate = `
Research {tool_category} tools for {use_case}

Evaluation factors:
- Feature completeness
- Integration capabilities
- Pricing models
- Learning curve
- Support options
- Security features
- Scalability
- User reviews

Deliverables:
- Tool comparison matrix
- Top 3 recommendations
- Implementation guide
- Cost analysis
`;
```

## Market Research Templates

### Technology Trend Analysis
```typescript
const trendAnalysisTemplate = `
Analyze trends in {technology_domain}

Research areas:
1. Current state of technology
2. Emerging innovations
3. Market adoption rates
4. Industry predictions
5. Key players and contributors
6. Potential disruptions
7. Investment patterns

Provide:
- Trend timeline
- Adoption curve analysis
- Future predictions
- Strategic recommendations
`;
```

### Competitive Analysis Template
```typescript
const competitiveTemplate = `
Analyze competitive landscape for {product_category}

Investigation points:
- Market leaders and their offerings
- Feature comparison matrix
- Pricing strategies
- Market share analysis
- Unique differentiators
- Customer satisfaction metrics
- Growth trajectories

Output:
- Competitive positioning map
- SWOT analysis
- Market opportunity assessment
- Strategic recommendations
`;
```

## Implementation Research Templates

### Migration Research Template
```typescript
const migrationTemplate = `
Research migration from {current_technology} to {target_technology}

Key considerations:
1. Migration strategies and patterns
2. Compatibility assessment
3. Data migration approaches
4. Gradual vs big-bang migration
5. Risk assessment and mitigation
6. Timeline estimation
7. Rollback procedures

Deliverables:
- Migration roadmap
- Risk matrix
- Tool recommendations
- Case study analysis
`;
```

### Integration Research Template
```typescript
const integrationTemplate = `
Research integration options for {system_a} with {system_b}

Analyze:
- Integration patterns (API, messaging, database)
- Authentication and authorization
- Data synchronization strategies
- Error handling and recovery
- Performance implications
- Monitoring and debugging
- Existing solutions and libraries

Provide:
- Integration architecture
- Implementation approaches
- Best practices
- Common pitfalls
`;
```

## Cost Analysis Templates

### TCO Research Template
```typescript
const tcoTemplate = `
Research Total Cost of Ownership for {solution}

Cost categories:
1. Initial implementation costs
2. Licensing and subscriptions
3. Infrastructure requirements
4. Maintenance and support
5. Training and onboarding
6. Scaling costs
7. Hidden costs

Analysis period: {time_period}

Include:
- Cost breakdown
- ROI analysis
- Cost comparison with alternatives
- Cost optimization strategies
`;
```

### ROI Analysis Template
```typescript
const roiTemplate = `
Analyze Return on Investment for {technology_investment}

Factors to consider:
- Implementation costs
- Expected benefits
- Time to value
- Risk factors
- Opportunity costs
- Long-term sustainability

Metrics:
- Payback period
- Net present value
- Internal rate of return
- Break-even analysis
`;
```

## Compliance Research Templates

### Regulatory Research Template
```typescript
const regulatoryTemplate = `
Research {compliance_standard} requirements for {application_type}

Areas to investigate:
1. Regulatory requirements
2. Technical controls needed
3. Documentation requirements
4. Audit procedures
5. Certification process
6. Common violations
7. Implementation timeline

Deliverables:
- Compliance checklist
- Gap analysis template
- Implementation roadmap
- Tool recommendations
`;
```

### Privacy Research Template
```typescript
const privacyTemplate = `
Research privacy requirements for {data_type} handling

Consider:
- Data classification
- Storage requirements
- Access controls
- Encryption standards
- Data retention policies
- User rights (GDPR, CCPA, etc.)
- Cross-border transfers

Output:
- Privacy impact assessment
- Technical requirements
- Policy templates
- Implementation guide
`;
```

## Output Format Templates

### Executive Summary Template
```typescript
const executiveSummaryTemplate = `
Executive Summary Format:

1. Research Objective
   - Clear problem statement
   - Scope and constraints

2. Key Findings
   - Top 3-5 discoveries
   - Critical insights

3. Recommendations
   - Primary recommendation
   - Alternative options
   - Risk considerations

4. Next Steps
   - Immediate actions
   - Timeline
   - Success metrics
`;
```

### Detailed Report Template
```typescript
const detailedReportTemplate = `
Detailed Research Report Structure:

1. Introduction
   - Background and context
   - Research methodology
   - Sources consulted

2. Detailed Findings
   - Topic-by-topic analysis
   - Evidence and citations
   - Data visualizations

3. Comparative Analysis
   - Options comparison
   - Trade-off analysis
   - Decision criteria

4. Recommendations
   - Detailed justification
   - Implementation approach
   - Risk mitigation

5. Appendices
   - Raw data
   - Additional resources
   - Glossary
`;
```

## Research Quality Templates

### Source Validation Template
```typescript
const sourceValidationTemplate = `
When citing sources, include:
- Source credibility rating (High/Medium/Low)
- Publication date
- Author expertise
- Potential biases
- Corroborating sources

Citation format:
[Finding] - Source: {source_name}, Date: {date}, Credibility: {rating}
`;
```

### Confidence Assessment Template
```typescript
const confidenceTemplate = `
Rate confidence for each finding:

High Confidence (90-100%):
- Multiple authoritative sources agree
- Extensive real-world validation
- Clear consensus in community

Medium Confidence (60-89%):
- Some conflicting information
- Limited real-world examples
- Emerging consensus

Low Confidence (Below 60%):
- Limited sources available
- Conflicting expert opinions
- Experimental or bleeding-edge
`;
```