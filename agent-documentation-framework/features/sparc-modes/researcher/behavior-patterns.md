# Researcher Mode - Behavioral Patterns

## Overview
The researcher mode specializes in gathering information, analyzing best practices, exploring technology options, and providing evidence-based recommendations for development decisions.

## Core Behavioral Patterns

### 1. Information Gathering Pattern
```python
# Systematic research approach
research_workflow = {
    "define_scope": "Clarify what needs to be researched",
    "identify_sources": "Find authoritative information sources",
    "collect_data": "Gather relevant information",
    "analyze_findings": "Synthesize and evaluate data",
    "generate_insights": "Extract actionable recommendations"
}
```

### 2. Research State Machine
```
[Define Problem] -> [Explore Domain] -> [Gather Evidence] -> [Analyze Options] -> [Synthesize] -> [Recommend]
       |                  |                    |                    |               |              |
       v                  v                    v                    v               v              v
 [Clarify Scope]    [Find Sources]      [Validate Info]      [Compare Pros/Cons] [Draw Conclusions] [Document]
```

### 3. Comparative Analysis Pattern
From implementation examples:
```bash
# Research different technology options
./claude-flow sparc run researcher "Analyze React vs Vue vs Angular performance characteristics"

# Output includes:
# - Performance benchmarks
# - Feature comparisons
# - Community support analysis
# - Use case recommendations
```

### 4. Best Practices Discovery
```python
# Pattern for finding best practices
best_practices_research = {
    "industry_standards": {
        "sources": ["official_docs", "standards_bodies", "rfcs"],
        "validation": "cross_reference_multiple_sources"
    },
    "community_practices": {
        "sources": ["github_repos", "tech_blogs", "conferences"],
        "validation": "check_adoption_rates"
    },
    "case_studies": {
        "sources": ["company_blogs", "white_papers", "postmortems"],
        "validation": "analyze_outcomes"
    }
}
```

## Research Methodologies

### 1. Technology Evaluation Pattern
```python
def evaluate_technology(tech_name):
    evaluation_criteria = {
        "maturity": assess_stability_and_version(),
        "performance": analyze_benchmarks(),
        "scalability": review_case_studies(),
        "community": measure_adoption_and_support(),
        "learning_curve": estimate_complexity(),
        "maintenance": check_update_frequency(),
        "security": review_vulnerability_history()
    }
    return comprehensive_evaluation(evaluation_criteria)
```

### 2. Architecture Research Pattern
```typescript
// Researching architectural patterns
const architectureResearch = {
    patterns: [
        "microservices vs monolith",
        "event-driven vs request-response",
        "serverless vs traditional"
    ],
    factors: {
        scalability: "How does it scale?",
        complexity: "Implementation difficulty",
        cost: "Infrastructure and maintenance costs",
        teamSize: "Required team expertise"
    }
};
```

### 3. Security Research Pattern
```python
# Security-focused research
security_research_pattern = {
    "threat_analysis": "Identify potential vulnerabilities",
    "mitigation_strategies": "Find proven solutions",
    "compliance_requirements": "Check regulatory needs",
    "tool_evaluation": "Compare security tools",
    "incident_analysis": "Learn from past breaches"
}
```

## Information Synthesis Patterns

### 1. Evidence-Based Recommendations
```typescript
interface ResearchSynthesis {
    findings: {
        pros: string[];
        cons: string[];
        risks: string[];
        opportunities: string[];
    };
    recommendation: {
        primary: string;
        alternatives: string[];
        justification: string;
    };
    confidenceLevel: "high" | "medium" | "low";
}
```

### 2. Competitive Analysis Pattern
```python
# Analyzing competing solutions
def competitive_analysis(domain):
    return {
        "market_leaders": identify_top_solutions(),
        "feature_matrix": create_comparison_table(),
        "pricing_models": analyze_cost_structures(),
        "user_feedback": aggregate_reviews(),
        "differentiation": find_unique_aspects(),
        "trends": identify_market_direction()
    }
```

### 3. Trend Analysis Pattern
```bash
# Research emerging trends
./claude-flow sparc run researcher "Research emerging trends in web development"

# Analyzes:
# - New frameworks and tools
# - Changing best practices
# - Industry shifts
# - Future predictions
```

## Domain-Specific Research Patterns

### 1. Framework Research
```typescript
// Framework evaluation pattern
const frameworkResearch = {
    technical: {
        performance: "Benchmark results",
        bundleSize: "Production build analysis",
        features: "Core capabilities",
        ecosystem: "Available packages"
    },
    practical: {
        documentation: "Quality and completeness",
        community: "Size and activity",
        jobMarket: "Demand and opportunities",
        longevity: "Project stability"
    }
};
```

### 2. Library Research
```python
# Library selection research
library_evaluation = {
    "functionality": "Does it meet requirements?",
    "api_design": "Is it intuitive to use?",
    "dependencies": "What does it require?",
    "size": "Impact on bundle size",
    "maintenance": "How actively maintained?",
    "alternatives": "What other options exist?",
    "migration": "How easy to switch later?"
}
```

### 3. Tool Research
```bash
# Development tool research
./claude-flow sparc run researcher "Research best CI/CD tools for our stack"

# Evaluates:
# - Integration capabilities
# - Pricing models
# - Learning curve
# - Feature sets
# - Community support
```

## Research Output Patterns

### 1. Structured Reports
```typescript
interface ResearchReport {
    executive_summary: string;
    detailed_findings: {
        section: string;
        content: string;
        evidence: string[];
        confidence: number;
    }[];
    recommendations: {
        primary: string;
        alternatives: string[];
        risks: string[];
    };
    references: {
        title: string;
        url: string;
        credibility: "high" | "medium" | "low";
    }[];
}
```

### 2. Decision Matrix Pattern
```python
# Creating decision matrices
def create_decision_matrix(options, criteria):
    matrix = {
        "options": options,
        "criteria": criteria,
        "weights": assign_criteria_weights(),
        "scores": evaluate_each_option(),
        "recommendation": calculate_best_option()
    }
    return format_as_table(matrix)
```

### 3. Risk Assessment Pattern
```typescript
// Risk analysis in research
const riskAssessment = {
    identify: "Find potential risks",
    evaluate: {
        likelihood: "How probable?",
        impact: "How severe?",
        mitigation: "How to prevent?"
    },
    prioritize: "Rank by risk score",
    document: "Create risk register"
};
```

## Integration Research Patterns

### 1. API Research
```python
# Researching external APIs
api_research_pattern = {
    "functionality": check_api_capabilities(),
    "reliability": analyze_uptime_history(),
    "performance": review_response_times(),
    "pricing": calculate_cost_projections(),
    "documentation": evaluate_doc_quality(),
    "support": assess_support_options()
}
```

### 2. Platform Research
```bash
# Cloud platform comparison
./claude-flow sparc run researcher "Compare AWS vs Azure vs GCP for our use case"

# Analyzes:
# - Service offerings
# - Pricing models
# - Regional availability
# - Compliance certifications
# - Migration complexity
```

## Performance Research Patterns

### 1. Benchmark Analysis
```typescript
// Performance research methodology
const performanceResearch = {
    metrics: [
        "response_time",
        "throughput",
        "resource_usage",
        "scalability"
    ],
    methods: {
        synthetic: "Controlled benchmarks",
        realWorld: "Production-like tests",
        stress: "Load testing results",
        comparison: "Side-by-side analysis"
    }
};
```

### 2. Optimization Research
```python
# Research optimization techniques
optimization_research = {
    "current_bottlenecks": profile_existing_system(),
    "proven_solutions": find_similar_case_studies(),
    "techniques": research_optimization_methods(),
    "tools": evaluate_profiling_tools(),
    "expected_gains": estimate_improvements()
}
```

## Learning Pattern Research

### 1. Team Skill Assessment
```typescript
// Research learning requirements
interface LearningResearch {
    technology: string;
    currentSkills: string[];
    requiredSkills: string[];
    learningResources: {
        official: string[];
        courses: string[];
        tutorials: string[];
    };
    timeEstimate: string;
    difficulty: "easy" | "medium" | "hard";
}
```

### 2. Adoption Strategy Research
```python
# Technology adoption research
adoption_strategy = {
    "pilot_project": "Small-scale testing approach",
    "training_plan": "Team education strategy",
    "migration_path": "Gradual adoption steps",
    "rollback_plan": "Risk mitigation strategy",
    "success_metrics": "How to measure adoption"
}
```

## Continuous Research Patterns

### 1. Monitoring Trends
```bash
# Set up continuous research
./claude-flow sparc run researcher "Monitor security vulnerabilities in our dependencies"

# Establishes:
# - Information sources
# - Update frequency
# - Alert criteria
# - Response procedures
```

### 2. Knowledge Base Building
```typescript
// Building organizational knowledge
const knowledgeBase = {
    capture: "Document research findings",
    organize: "Categorize by topic",
    update: "Keep information current",
    share: "Make accessible to team",
    validate: "Verify accuracy over time"
};
```