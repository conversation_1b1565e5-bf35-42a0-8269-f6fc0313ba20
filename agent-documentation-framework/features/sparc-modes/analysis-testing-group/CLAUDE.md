# Analysis-Testing Group - CLAUDE Configuration

## Group Overview

The Analysis-Testing Group consolidates three complementary SPARC modes that work together to understand, fix, and validate system behavior:

- **Analyzer Mode**: Deep insight generation through multi-dimensional analysis
- **Debugger Mode**: Systematic investigation and root cause analysis  
- **Tester Mode**: Comprehensive validation and quality assurance

These modes share common architectural patterns and integration mechanisms while maintaining their specialized capabilities.

## Mode Selection

Choose the appropriate mode based on your primary objective:

```yaml
mode_selection_guide:
  use_analyzer_when:
    - Monitoring system performance and health
    - Identifying patterns and anomalies
    - Generating insights and recommendations
    - Forecasting and capacity planning
    
  use_debugger_when:
    - Investigating system failures or errors
    - Finding root causes of issues
    - Developing and validating fixes
    - Understanding unexpected behaviors
    
  use_tester_when:
    - Validating new implementations
    - Ensuring quality standards
    - Running regression tests
    - Measuring test coverage
```

## Shared Architecture

All three modes in this group share:
- Common execution framework patterns
- Unified integration mechanisms
- Coordinated state management
- Shared semantic models

Refer to the group-level documentation:
- `execution-framework.md` - Runtime behavior and tool orchestration
- `integration-patterns.md` - SPARC ecosystem integration
- `semantic-architecture.md` - Conceptual models and workflows  
- `state-transitions.md` - State management and transitions

## Mode-Specific Behaviors

### Analyzer Mode

**Primary Role**: Transform system observables into actionable intelligence

```json
{
  "analyzer_configuration": {
    "core_capabilities": [
      "real_time_monitoring",
      "pattern_discovery", 
      "anomaly_detection",
      "predictive_analytics",
      "performance_optimization"
    ],
    "typical_workflows": {
      "continuous_monitoring": "Track metrics and identify deviations",
      "deep_analysis": "Investigate complex patterns and correlations",
      "capacity_planning": "Forecast resource needs and scaling requirements"
    }
  }
}
```

### Debugger Mode

**Primary Role**: Systematic investigation and problem resolution

```json
{
  "debugger_configuration": {
    "core_capabilities": [
      "issue_reproduction",
      "root_cause_analysis",
      "hypothesis_testing",
      "solution_development",
      "knowledge_capture"
    ],
    "typical_workflows": {
      "bug_investigation": "Reproduce, analyze, and fix issues",
      "performance_debugging": "Identify and resolve bottlenecks",
      "forensic_analysis": "Investigate past incidents from logs"
    }
  }
}
```

### Tester Mode

**Primary Role**: Comprehensive validation and quality assurance

```json
{
  "tester_configuration": {
    "core_capabilities": [
      "test_automation",
      "coverage_analysis",
      "regression_testing",
      "performance_testing",
      "quality_reporting"
    ],
    "typical_workflows": {
      "continuous_testing": "Automated validation in CI/CD",
      "comprehensive_validation": "Full system testing",
      "targeted_testing": "Specific feature or fix validation"
    }
  }
}
```

## Cross-Mode Coordination

The modes work together in common patterns:

```mermaid
graph LR
    A[Analyzer] -->|Anomaly Detected| D[Debugger]
    D -->|Fix Ready| T[Tester]
    T -->|Metrics| A
    
    A -->|Performance Issue| O[Optimizer]
    D -->|Solution| C[Coder]
    T -->|Quality Report| R[Reviewer]
```

## Best Practices

### Effective Mode Usage

1. **Start with the right mode** - Choose based on your primary goal
2. **Leverage mode transitions** - Let modes hand off naturally
3. **Use shared memory** - Maintain context across modes
4. **Monitor progress** - Use TodoWrite for task tracking

### Common Workflows

```yaml
integrated_workflows:
  performance_issue_resolution:
    1. analyzer: "Detect performance degradation"
    2. debugger: "Investigate root cause"
    3. coder: "Implement optimization"
    4. tester: "Validate improvement"
    5. analyzer: "Confirm performance gain"
    
  quality_improvement_cycle:
    1. tester: "Identify quality gaps"
    2. analyzer: "Analyze failure patterns"
    3. debugger: "Fix systematic issues"
    4. tester: "Verify improvements"
```

## Tool Coordination

All modes share access to core tools with mode-specific usage patterns:

```yaml
shared_tools:
  Read:
    analyzer: "Bulk data ingestion"
    debugger: "Code and log analysis"
    tester: "Test case discovery"
    
  Bash:
    analyzer: "Metric collection scripts"
    debugger: "Debugging commands"
    tester: "Test runner execution"
    
  Memory:
    analyzer: "Pattern library"
    debugger: "Solution database"
    tester: "Test scenario repository"
```

## Memory Patterns

Shared memory structures enable cross-mode intelligence:

```json
{
  "shared_memory": {
    "performance_baselines": "Analyzer-generated, used by all",
    "bug_patterns": "Debugger-discovered, prevents regressions",
    "test_scenarios": "Tester-created, validates fixes",
    "system_knowledge": "Collectively built understanding"
  }
}
```

## Activation Commands

Use these commands to activate specific modes:

```bash
# Direct mode activation
sparc --mode analyzer "Monitor system performance"
sparc --mode debugger "Investigate login failures"  
sparc --mode tester "Run regression suite"

# Mode selection based on task
sparc "Find out why the API is slow"  # Auto-selects debugger/analyzer
sparc "Test the new payment feature"  # Auto-selects tester
```

## Performance Considerations

- **Analyzer**: Can consume significant resources during deep analysis
- **Debugger**: May require system access that impacts performance
- **Tester**: Test execution can be parallelized for efficiency

## Error Handling

Each mode includes graceful degradation:
- Partial results when resources are constrained
- Fallback strategies for tool failures  
- State recovery from checkpoints

## Quick Reference

| Mode | Primary Purpose | Key Outputs | Typical Duration |
|------|----------------|-------------|------------------|
| Analyzer | Insight Generation | Patterns, Anomalies, Predictions | Minutes to Hours |
| Debugger | Problem Resolution | Root Causes, Solutions | Hours to Days |
| Tester | Quality Validation | Test Results, Coverage | Minutes to Hours |

Remember: These modes are designed to work together. Use mode transitions and shared memory to maintain context and build comprehensive system understanding.