# Communication Message Formats

## Overview

This document defines the comprehensive message format specifications for the RUST-SS communication system. All messages follow standardized schemas to ensure type safety, interoperability, and maintainability across different agent implementations.

## Base Message Structure

### Core Message Envelope

```rust
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::time::Duration;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Message {
    /// Unique identifier for this message
    pub id: MessageId,
    
    /// Agent that sent this message
    pub sender: AgentId,
    
    /// Target agent (None for broadcast messages)
    pub recipient: Option<AgentId>,
    
    /// Topic for pub-sub pattern messages
    pub topic: Option<String>,
    
    /// Categorization of message content
    pub message_type: MessageType,
    
    /// Actual message content
    pub payload: MessagePayload,
    
    /// Processing priority
    pub priority: MessagePriority,
    
    /// When the message was created
    pub timestamp: DateTime<Utc>,
    
    /// Time-to-live for message expiration
    pub ttl: Option<Duration>,
    
    /// Delivery semantics required
    pub delivery_guarantee: DeliveryGuarantee,
    
    /// For request-response correlation
    pub correlation_id: Option<CorrelationId>,
    
    /// Security context and permissions
    pub security: SecurityContext,
    
    /// Additional metadata
    pub metadata: MessageMetadata,
    
    /// Message version for schema evolution
    pub schema_version: SchemaVersion,
}

/// Unique message identifier
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct MessageId(String);

impl MessageId {
    pub fn new() -> Self {
        Self(uuid::Uuid::new_v4().to_string())
    }
    
    pub fn from_string(id: String) -> Self {
        Self(id)
    }
}

/// Agent identifier
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct AgentId {
    pub agent_type: String,
    pub instance_id: String,
    pub node_id: Option<String>,
}

impl AgentId {
    pub fn new(agent_type: &str, instance_id: &str) -> Self {
        Self {
            agent_type: agent_type.to_string(),
            instance_id: instance_id.to_string(),
            node_id: None,
        }
    }
    
    pub fn with_node(agent_type: &str, instance_id: &str, node_id: &str) -> Self {
        Self {
            agent_type: agent_type.to_string(),
            instance_id: instance_id.to_string(),
            node_id: Some(node_id.to_string()),
        }
    }
    
    pub fn to_string(&self) -> String {
        match &self.node_id {
            Some(node) => format!("{}:{}@{}", self.agent_type, self.instance_id, node),
            None => format!("{}:{}", self.agent_type, self.instance_id),
        }
    }
}
```

### Message Classification

```rust
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MessageType {
    // System-level messages
    SystemNotification,
    SystemCommand,
    SystemStatus,
    SystemAlert,
    
    // Agent coordination
    TaskAssignment,
    TaskResult,
    TaskStatus,
    TaskCancellation,
    AgentStatus,
    AgentHeartbeat,
    
    // Swarm coordination
    SwarmFormation,
    SwarmDisbandment,
    SwarmStrategy,
    SwarmMetrics,
    SwarmCommand,
    
    // Resource management
    ResourceRequest,
    ResourceAllocation,
    ResourceRelease,
    ResourceUpdate,
    
    // Event notifications
    EventNotification,
    StateChange,
    AlertNotification,
    
    // Communication control
    MessageAcknowledgment,
    MessageDeliveryReceipt,
    MessageRetransmission,
    
    // Security messages
    AuthenticationRequest,
    AuthenticationResponse,
    AuthorizationUpdate,
    SecurityAlert,
    
    // Custom extensible messages
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MessagePriority {
    Critical,    // System-critical, highest priority
    High,        // Important operations
    Normal,      // Standard operations
    Low,         // Background tasks
    Bulk,        // Batch operations
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DeliveryGuarantee {
    AtMostOnce,     // Fire and forget
    AtLeastOnce,    // Guaranteed delivery with potential duplicates
    ExactlyOnce,    // Guaranteed delivery without duplicates
    BestEffort,     // Try to deliver but don't guarantee
}
```

### Security and Metadata

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityContext {
    /// Authentication token
    pub auth_token: Option<String>,
    
    /// Required permissions to process this message
    pub required_permissions: Vec<Permission>,
    
    /// Security level classification
    pub security_level: SecurityLevel,
    
    /// Message encryption state
    pub encrypted: bool,
    
    /// Digital signature for message integrity
    pub signature: Option<MessageSignature>,
    
    /// Session context
    pub session_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageMetadata {
    /// Trace ID for distributed tracing
    pub trace_id: Option<String>,
    
    /// Span ID for distributed tracing
    pub span_id: Option<String>,
    
    /// Custom metadata fields
    pub custom: HashMap<String, serde_json::Value>,
    
    /// Message routing hints
    pub routing_hints: Vec<RoutingHint>,
    
    /// Performance tracking
    pub performance: PerformanceMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetadata {
    /// Size of the message in bytes
    pub size_bytes: usize,
    
    /// Expected processing time
    pub expected_processing_time: Option<Duration>,
    
    /// Maximum allowed processing time
    pub max_processing_time: Option<Duration>,
    
    /// Resource requirements
    pub resource_requirements: ResourceRequirements,
}
```

## Message Payload Definitions

### Task-Related Messages

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskAssignmentPayload {
    /// Unique task identifier
    pub task_id: TaskId,
    
    /// Type of task to be executed
    pub task_type: TaskType,
    
    /// Task-specific parameters
    pub parameters: HashMap<String, serde_json::Value>,
    
    /// Task deadline
    pub deadline: Option<DateTime<Utc>>,
    
    /// Task dependencies
    pub dependencies: Vec<TaskId>,
    
    /// Required resources
    pub resources_required: Vec<ResourceRequirement>,
    
    /// Expected output format
    pub expected_output: Option<OutputSchema>,
    
    /// Retry policy
    pub retry_policy: RetryPolicy,
    
    /// Progress reporting requirements
    pub progress_reporting: ProgressReporting,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskResultPayload {
    /// Task that was completed
    pub task_id: TaskId,
    
    /// Execution status
    pub status: TaskStatus,
    
    /// Task output data
    pub result: Option<TaskOutput>,
    
    /// Error information if failed
    pub error: Option<TaskError>,
    
    /// Execution metadata
    pub execution_metadata: ExecutionMetadata,
    
    /// Resources that were used
    pub resources_used: Vec<ResourceUsage>,
    
    /// Performance metrics
    pub performance_metrics: PerformanceMetrics,
    
    /// Follow-up tasks if any
    pub follow_up_tasks: Vec<TaskAssignmentPayload>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatusPayload {
    /// Task being reported on
    pub task_id: TaskId,
    
    /// Current status
    pub status: TaskStatus,
    
    /// Progress percentage (0-100)
    pub progress: f32,
    
    /// Current phase or step
    pub current_phase: Option<String>,
    
    /// Estimated time remaining
    pub estimated_time_remaining: Option<Duration>,
    
    /// Status details
    pub details: Option<String>,
    
    /// Intermediate results
    pub intermediate_results: Vec<IntermediateResult>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskStatus {
    Pending,
    Assigned,
    InProgress,
    Suspended,
    Completed,
    Failed,
    Cancelled,
    TimedOut,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskType {
    Research,
    CodeGeneration,
    Testing,
    Analysis,
    Documentation,
    Coordination,
    Monitoring,
    Custom(String),
}
```

### Agent Coordination Messages

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentStatusPayload {
    /// Agent reporting status
    pub agent_id: AgentId,
    
    /// Current operational status
    pub status: AgentStatus,
    
    /// Current workload
    pub workload: WorkloadInfo,
    
    /// Available capabilities
    pub capabilities: Vec<AgentCapability>,
    
    /// Current tasks
    pub current_tasks: Vec<TaskId>,
    
    /// Resource utilization
    pub resource_utilization: ResourceUtilization,
    
    /// Health information
    pub health: HealthInfo,
    
    /// Location information for distributed systems
    pub location: Option<LocationInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AgentStatus {
    Initializing,
    Idle,
    Busy,
    Overloaded,
    Suspended,
    Terminating,
    Failed,
    Offline,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkloadInfo {
    /// Current number of active tasks
    pub active_tasks: usize,
    
    /// Maximum task capacity
    pub max_capacity: usize,
    
    /// CPU utilization percentage
    pub cpu_utilization: f32,
    
    /// Memory utilization percentage
    pub memory_utilization: f32,
    
    /// Queue depth
    pub queue_depth: usize,
    
    /// Average task completion time
    pub avg_completion_time: Duration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentHeartbeatPayload {
    /// Agent sending heartbeat
    pub agent_id: AgentId,
    
    /// Heartbeat sequence number
    pub sequence: u64,
    
    /// Timestamp of heartbeat
    pub timestamp: DateTime<Utc>,
    
    /// Quick health status
    pub health_status: HealthStatus,
    
    /// Number of active connections
    pub active_connections: usize,
    
    /// Uptime duration
    pub uptime: Duration,
}
```

### Swarm Coordination Messages

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwarmFormationPayload {
    /// Unique swarm identifier
    pub swarm_id: SwarmId,
    
    /// Swarm strategy to be used
    pub strategy: SwarmStrategy,
    
    /// Target agents for the swarm
    pub target_agents: Vec<AgentId>,
    
    /// Objective for the swarm
    pub objective: String,
    
    /// Coordination mode
    pub coordination_mode: CoordinationMode,
    
    /// Resource allocation
    pub resource_allocation: SwarmResourceAllocation,
    
    /// Communication patterns
    pub communication_patterns: Vec<CommunicationPattern>,
    
    /// Success criteria
    pub success_criteria: SuccessCriteria,
    
    /// Maximum duration
    pub max_duration: Option<Duration>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwarmMetricsPayload {
    /// Swarm being reported on
    pub swarm_id: SwarmId,
    
    /// Overall swarm performance
    pub performance: SwarmPerformance,
    
    /// Individual agent metrics
    pub agent_metrics: HashMap<AgentId, AgentMetrics>,
    
    /// Communication statistics
    pub communication_stats: CommunicationStats,
    
    /// Resource utilization
    pub resource_utilization: SwarmResourceUtilization,
    
    /// Progress towards objectives
    pub progress: SwarmProgress,
    
    /// Issues and bottlenecks
    pub issues: Vec<SwarmIssue>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SwarmStrategy {
    Research,
    Development,
    Testing,
    Analysis,
    Optimization,
    Maintenance,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CoordinationMode {
    Centralized,
    Distributed,
    Hierarchical,
    Mesh,
    Hybrid,
}
```

### System Messages

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemNotificationPayload {
    /// Type of system notification
    pub notification_type: SystemNotificationType,
    
    /// Notification message
    pub message: String,
    
    /// Severity level
    pub severity: NotificationSeverity,
    
    /// Affected components
    pub affected_components: Vec<ComponentId>,
    
    /// Additional details
    pub details: HashMap<String, serde_json::Value>,
    
    /// Recommended actions
    pub recommended_actions: Vec<RecommendedAction>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemCommandPayload {
    /// Command to execute
    pub command: SystemCommand,
    
    /// Command parameters
    pub parameters: HashMap<String, serde_json::Value>,
    
    /// Target components
    pub targets: Vec<ComponentId>,
    
    /// Execution mode
    pub execution_mode: ExecutionMode,
    
    /// Authorization token
    pub authorization: String,
    
    /// Timeout for command execution
    pub timeout: Option<Duration>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SystemCommand {
    Shutdown,
    Restart,
    Pause,
    Resume,
    Configure,
    UpdateSoftware,
    ScaleUp,
    ScaleDown,
    Backup,
    Restore,
    Diagnostics,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionMode {
    Immediate,
    Scheduled(DateTime<Utc>),
    Graceful,
    Force,
}
```

### Resource Management Messages

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceRequestPayload {
    /// Unique request identifier
    pub request_id: RequestId,
    
    /// Agent making the request
    pub requester: AgentId,
    
    /// Requested resources
    pub resources: Vec<ResourceSpec>,
    
    /// Duration needed
    pub duration: Option<Duration>,
    
    /// Priority of the request
    pub priority: ResourcePriority,
    
    /// Justification for the request
    pub justification: Option<String>,
    
    /// Fallback options
    pub fallback_options: Vec<ResourceSpec>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceAllocationPayload {
    /// Original request identifier
    pub request_id: RequestId,
    
    /// Allocated resources
    pub allocated_resources: Vec<AllocatedResource>,
    
    /// Allocation duration
    pub allocation_duration: Duration,
    
    /// Resource access tokens
    pub access_tokens: HashMap<String, String>,
    
    /// Usage guidelines
    pub usage_guidelines: Vec<UsageGuideline>,
    
    /// Monitoring requirements
    pub monitoring_requirements: MonitoringRequirements,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceSpec {
    /// Type of resource
    pub resource_type: ResourceType,
    
    /// Quantity requested
    pub quantity: ResourceQuantity,
    
    /// Quality requirements
    pub quality_requirements: QualityRequirements,
    
    /// Location preferences
    pub location_preferences: Vec<LocationPreference>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceType {
    Compute {
        cpu_cores: u32,
        memory_gb: u32,
        storage_gb: u32,
    },
    Network {
        bandwidth_mbps: u32,
        latency_ms: u32,
    },
    Storage {
        capacity_gb: u32,
        iops: u32,
        durability: DurabilityLevel,
    },
    Database {
        db_type: DatabaseType,
        connection_pool_size: u32,
    },
    ApiAccess {
        service_name: String,
        rate_limit: u32,
    },
    Custom(String),
}
```

### Event Notification Messages

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventNotificationPayload {
    /// Event identifier
    pub event_id: EventId,
    
    /// Event type
    pub event_type: EventType,
    
    /// Event source
    pub source: EventSource,
    
    /// Event data
    pub data: serde_json::Value,
    
    /// Event timestamp
    pub event_timestamp: DateTime<Utc>,
    
    /// Event severity
    pub severity: EventSeverity,
    
    /// Affected entities
    pub affected_entities: Vec<EntityId>,
    
    /// Correlation with other events
    pub correlation: Vec<EventId>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateChangePayload {
    /// Entity that changed
    pub entity_id: EntityId,
    
    /// Type of entity
    pub entity_type: EntityType,
    
    /// Previous state
    pub previous_state: serde_json::Value,
    
    /// New state
    pub new_state: serde_json::Value,
    
    /// Change reason
    pub change_reason: ChangeReason,
    
    /// Change metadata
    pub change_metadata: ChangeMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventType {
    AgentJoined,
    AgentLeft,
    TaskStarted,
    TaskCompleted,
    TaskFailed,
    SwarmFormed,
    SwarmDisbanded,
    ResourceAllocated,
    ResourceReleased,
    SystemAlert,
    SecurityIncident,
    PerformanceAnomaly,
    Custom(String),
}
```

## Message Serialization

### JSON Format

The primary serialization format is JSON for maximum interoperability:

```json
{
  "id": "msg_12345678-1234-1234-1234-123456789abc",
  "sender": {
    "agent_type": "researcher",
    "instance_id": "001",
    "node_id": "node-west-1"
  },
  "recipient": {
    "agent_type": "coordinator",
    "instance_id": "001",
    "node_id": null
  },
  "topic": null,
  "message_type": "TaskResult",
  "payload": {
    "type": "TaskResult",
    "data": {
      "task_id": "task_research_web_frameworks",
      "status": "Completed",
      "result": {
        "findings": [
          {
            "framework": "React",
            "popularity_score": 95,
            "performance_rating": "High"
          }
        ]
      },
      "execution_metadata": {
        "start_time": "2024-01-15T10:00:00Z",
        "end_time": "2024-01-15T10:30:00Z",
        "duration_seconds": 1800
      }
    }
  },
  "priority": "Normal",
  "timestamp": "2024-01-15T10:30:00Z",
  "ttl": null,
  "delivery_guarantee": "AtLeastOnce",
  "correlation_id": "req_87654321-4321-4321-4321-************",
  "security": {
    "auth_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
    "required_permissions": ["task.result.read"],
    "security_level": "Standard",
    "encrypted": false,
    "signature": null,
    "session_id": "session_abc123"
  },
  "metadata": {
    "trace_id": "trace_999888777666",
    "span_id": "span_123456",
    "custom": {},
    "routing_hints": [],
    "performance": {
      "size_bytes": 1024,
      "expected_processing_time": null,
      "max_processing_time": "PT30S",
      "resource_requirements": {
        "cpu_cores": 1,
        "memory_mb": 256
      }
    }
  },
  "schema_version": "1.0.0"
}
```

### Binary Format (MessagePack)

For high-performance scenarios, MessagePack binary serialization is supported:

```rust
use rmp_serde::{Serializer, Deserializer};

impl Message {
    pub fn to_messagepack(&self) -> Result<Vec<u8>, MessageError> {
        let mut buf = Vec::new();
        self.serialize(&mut Serializer::new(&mut buf))?;
        Ok(buf)
    }
    
    pub fn from_messagepack(data: &[u8]) -> Result<Self, MessageError> {
        let mut de = Deserializer::new(data);
        let message = Deserialize::deserialize(&mut de)?;
        Ok(message)
    }
}
```

### Protocol Buffers Support

For maximum performance and cross-language compatibility:

```protobuf
syntax = "proto3";

package rust_ss.communication;

message Message {
  string id = 1;
  AgentId sender = 2;
  optional AgentId recipient = 3;
  optional string topic = 4;
  MessageType message_type = 5;
  bytes payload = 6;
  MessagePriority priority = 7;
  int64 timestamp = 8;
  optional int64 ttl_seconds = 9;
  DeliveryGuarantee delivery_guarantee = 10;
  optional string correlation_id = 11;
  SecurityContext security = 12;
  MessageMetadata metadata = 13;
  string schema_version = 14;
}

message AgentId {
  string agent_type = 1;
  string instance_id = 2;
  optional string node_id = 3;
}

enum MessageType {
  MESSAGE_TYPE_UNSPECIFIED = 0;
  SYSTEM_NOTIFICATION = 1;
  SYSTEM_COMMAND = 2;
  TASK_ASSIGNMENT = 3;
  TASK_RESULT = 4;
  AGENT_STATUS = 5;
  // ... other types
}
```

## Schema Evolution

### Versioning Strategy

The message format supports schema evolution through semantic versioning:

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchemaVersion {
    pub major: u32,
    pub minor: u32,
    pub patch: u32,
}

impl SchemaVersion {
    pub fn current() -> Self {
        Self { major: 1, minor: 0, patch: 0 }
    }
    
    pub fn is_compatible(&self, other: &SchemaVersion) -> bool {
        // Major version must match
        if self.major != other.major {
            return false;
        }
        
        // Minor version backward compatibility
        self.minor >= other.minor
    }
}
```

### Migration Support

Automatic message migration between schema versions:

```rust
pub trait MessageMigration {
    fn migrate_from(&self, old_version: &SchemaVersion) -> Result<Message, MigrationError>;
    fn can_migrate_from(&self, old_version: &SchemaVersion) -> bool;
}

impl MessageMigration for Message {
    fn migrate_from(&self, old_version: &SchemaVersion) -> Result<Message, MigrationError> {
        match (old_version.major, old_version.minor) {
            (0, 9) => self.migrate_from_0_9(),
            (1, 0) => Ok(self.clone()), // Current version
            _ => Err(MigrationError::UnsupportedVersion(*old_version)),
        }
    }
    
    fn can_migrate_from(&self, old_version: &SchemaVersion) -> bool {
        matches!((old_version.major, old_version.minor), (0, 9) | (1, 0))
    }
}
```

## Validation and Testing

### Message Validation

```rust
pub trait MessageValidator {
    fn validate(&self, message: &Message) -> Result<(), ValidationError>;
}

pub struct StandardMessageValidator;

impl MessageValidator for StandardMessageValidator {
    fn validate(&self, message: &Message) -> Result<(), ValidationError> {
        // Check required fields
        if message.id.0.is_empty() {
            return Err(ValidationError::MissingField("id".to_string()));
        }
        
        // Validate message type and payload consistency
        self.validate_payload_consistency(message)?;
        
        // Check security context
        self.validate_security_context(&message.security)?;
        
        // Validate metadata
        self.validate_metadata(&message.metadata)?;
        
        Ok(())
    }
}
```

### Test Message Generators

```rust
#[cfg(test)]
pub mod test_utils {
    use super::*;
    
    pub fn create_test_task_assignment() -> Message {
        Message {
            id: MessageId::new(),
            sender: AgentId::new("coordinator", "001"),
            recipient: Some(AgentId::new("researcher", "001")),
            topic: None,
            message_type: MessageType::TaskAssignment,
            payload: MessagePayload::TaskAssignment(TaskAssignmentPayload {
                task_id: TaskId::new("test_task"),
                task_type: TaskType::Research,
                parameters: HashMap::new(),
                deadline: None,
                dependencies: Vec::new(),
                resources_required: Vec::new(),
                expected_output: None,
                retry_policy: RetryPolicy::default(),
                progress_reporting: ProgressReporting::default(),
            }),
            priority: MessagePriority::Normal,
            timestamp: Utc::now(),
            ttl: None,
            delivery_guarantee: DeliveryGuarantee::AtLeastOnce,
            correlation_id: None,
            security: SecurityContext::default(),
            metadata: MessageMetadata::default(),
            schema_version: SchemaVersion::current(),
        }
    }
}
```

This comprehensive message format specification ensures type-safe, efficient, and extensible communication throughout the RUST-SS system.