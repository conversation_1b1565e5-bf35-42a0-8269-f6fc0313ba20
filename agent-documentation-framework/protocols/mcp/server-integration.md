# MCP Server Integration

## Overview

The MCP Server Integration module provides comprehensive server lifecycle management, transport abstraction, and protocol handling for the RUST-SS system. This implementation follows the claude-code-flow patterns while providing enhanced performance and reliability for high-scale Rust environments.

## Architecture

### Core Server Structure

```rust
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

pub struct MCPServer {
    config: Arc<MCPConfig>,
    transport: Box<dyn Transport + Send + Sync>,
    tool_registry: Arc<RwLock<ToolRegistry>>,
    session_manager: Arc<SessionManager>,
    auth_manager: Arc<AuthManager>,
    event_bus: Arc<EventBus>,
    performance_monitor: Arc<PerformanceMonitor>,
    lifecycle_manager: Arc<LifecycleManager>,
    protocol_manager: Arc<ProtocolManager>,
    state: Arc<RwLock<ServerState>>,
}

#[derive(Debug, Clone)]
pub struct MCPConfig {
    pub transport: TransportConfig,
    pub security: SecurityConfig,
    pub performance: PerformanceConfig,
    pub monitoring: MonitoringConfig,
}

#[derive(Debug, <PERSON>lone)]
pub struct TransportConfig {
    pub transport_type: TransportType,
    pub host: String,
    pub port: u16,
    pub tls_enabled: bool,
    pub max_connections: usize,
    pub request_timeout: Duration,
    pub max_request_size: usize,
    pub compression: CompressionConfig,
}

#[derive(Debug, Clone)]
pub enum TransportType {
    Stdio,
    Http,
    StreamableHttp,
    WebSocket,
}
```

### Server Lifecycle Management

#### Initialization Phase

```rust
impl MCPServer {
    pub async fn new(config: MCPConfig) -> Result<Self, MCPError> {
        let event_bus = Arc::new(EventBus::new());
        let session_manager = Arc::new(SessionManager::new(&config.security));
        let auth_manager = Arc::new(AuthManager::new(&config.security));
        let tool_registry = Arc::new(RwLock::new(ToolRegistry::new()));
        let performance_monitor = Arc::new(PerformanceMonitor::new(&config.monitoring));
        let lifecycle_manager = Arc::new(LifecycleManager::new(event_bus.clone()));
        let protocol_manager = Arc::new(ProtocolManager::new());
        
        // Initialize transport based on configuration
        let transport = Self::create_transport(&config.transport).await?;
        
        Ok(MCPServer {
            config: Arc::new(config),
            transport,
            tool_registry,
            session_manager,
            auth_manager,
            event_bus,
            performance_monitor,
            lifecycle_manager,
            protocol_manager,
            state: Arc::new(RwLock::new(ServerState::Initializing)),
        })
    }
    
    pub async fn start(&mut self) -> Result<(), MCPError> {
        // Phase 1: Pre-startup validation
        self.validate_configuration().await?;
        self.initialize_security().await?;
        
        // Phase 2: Transport initialization
        self.transport.initialize().await?;
        
        // Phase 3: Core service startup
        self.register_core_tools().await?;
        self.start_monitoring().await?;
        self.start_health_checks().await?;
        
        // Phase 4: Protocol handler setup
        self.setup_protocol_handlers().await?;
        
        // Phase 5: Begin accepting connections
        *self.state.write().await = ServerState::Running;
        self.event_bus.emit(SystemEvent::ServerStarted).await;
        
        // Phase 6: Main event loop
        self.run_message_loop().await?;
        
        Ok(())
    }
}
```

#### Transport Abstraction

```rust
#[async_trait]
pub trait Transport {
    async fn initialize(&mut self) -> Result<(), TransportError>;
    async fn listen(&mut self) -> Result<(), TransportError>;
    async fn handle_connection(&self, connection: Connection) -> Result<(), TransportError>;
    async fn send_message(&self, session_id: &str, message: MCPMessage) -> Result<(), TransportError>;
    async fn broadcast(&self, message: MCPMessage) -> Result<(), TransportError>;
    async fn shutdown(&mut self) -> Result<(), TransportError>;
}

// Stdio Transport Implementation
pub struct StdioTransport {
    reader: Option<BufReader<Stdin>>,
    writer: Option<BufWriter<Stdout>>,
    session_id: String,
    message_handler: Arc<dyn MessageHandler + Send + Sync>,
}

impl StdioTransport {
    pub fn new(message_handler: Arc<dyn MessageHandler + Send + Sync>) -> Self {
        Self {
            reader: None,
            writer: None,
            session_id: uuid::Uuid::new_v4().to_string(),
            message_handler,
        }
    }
}

#[async_trait]
impl Transport for StdioTransport {
    async fn initialize(&mut self) -> Result<(), TransportError> {
        self.reader = Some(BufReader::new(tokio::io::stdin()));
        self.writer = Some(BufWriter::new(tokio::io::stdout()));
        Ok(())
    }
    
    async fn listen(&mut self) -> Result<(), TransportError> {
        let reader = self.reader.take()
            .ok_or(TransportError::NotInitialized)?;
        
        let mut lines = reader.lines();
        while let Some(line) = lines.next_line().await? {
            if !line.trim().is_empty() {
                let message: MCPMessage = serde_json::from_str(&line)
                    .map_err(|e| TransportError::InvalidMessage(e.to_string()))?;
                
                self.message_handler.handle_message(&self.session_id, message).await?;
            }
        }
        
        Ok(())
    }
    
    async fn send_message(&self, _session_id: &str, message: MCPMessage) -> Result<(), TransportError> {
        let json = serde_json::to_string(&message)
            .map_err(|e| TransportError::SerializationError(e.to_string()))?;
        
        println!("{}", json);
        Ok(())
    }
}
```

#### HTTP Transport Implementation

```rust
pub struct HttpTransport {
    config: TransportConfig,
    app: Option<Router>,
    server_handle: Option<tokio::task::JoinHandle<()>>,
    message_handler: Arc<dyn MessageHandler + Send + Sync>,
    session_store: Arc<RwLock<HashMap<String, Session>>>,
}

impl HttpTransport {
    pub fn new(
        config: TransportConfig,
        message_handler: Arc<dyn MessageHandler + Send + Sync>,
    ) -> Self {
        Self {
            config,
            app: None,
            server_handle: None,
            message_handler,
            session_store: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    fn create_app(&self) -> Router {
        Router::new()
            .route("/mcp", post(Self::handle_post).get(Self::handle_sse))
            .route("/health", get(Self::health_check))
            .layer(cors_layer())
            .layer(compression_layer())
            .layer(request_id_layer())
            .layer(timeout_layer(Duration::from_secs(30)))
            .with_state(AppState {
                message_handler: self.message_handler.clone(),
                session_store: self.session_store.clone(),
            })
    }
    
    async fn handle_post(
        State(state): State<AppState>,
        headers: HeaderMap,
        Json(payload): Json<serde_json::Value>,
    ) -> Result<Response, MCPError> {
        // Extract session ID from headers
        let session_id = headers
            .get("mcp-session-id")
            .and_then(|v| v.to_str().ok())
            .unwrap_or_else(|| &uuid::Uuid::new_v4().to_string());
        
        // Parse message(s)
        let messages = if payload.is_array() {
            serde_json::from_value::<Vec<MCPMessage>>(payload)?
        } else {
            vec![serde_json::from_value::<MCPMessage>(payload)?]
        };
        
        // Process messages
        let mut responses = Vec::new();
        for message in messages {
            if let Some(response) = state.message_handler
                .handle_message(session_id, message).await? {
                responses.push(response);
            }
        }
        
        // Determine response type
        if responses.is_empty() {
            Ok(StatusCode::ACCEPTED.into_response())
        } else if responses.len() == 1 {
            Ok(Json(responses.into_iter().next().unwrap()).into_response())
        } else {
            Ok(Json(responses).into_response())
        }
    }
    
    async fn handle_sse(
        State(state): State<AppState>,
        headers: HeaderMap,
    ) -> Result<Sse<impl Stream<Item = Result<Event, Infallible>>>, MCPError> {
        let session_id = headers
            .get("mcp-session-id")
            .and_then(|v| v.to_str().ok())
            .unwrap_or_else(|| &uuid::Uuid::new_v4().to_string());
        
        // Create SSE stream for server-to-client messages
        let (tx, rx) = mpsc::unbounded_channel();
        
        // Register session for server-initiated messages
        {
            let mut sessions = state.session_store.write().await;
            sessions.insert(session_id.to_string(), Session {
                id: session_id.to_string(),
                sse_sender: Some(tx),
                created_at: Instant::now(),
                last_activity: Instant::now(),
            });
        }
        
        // Convert receiver to SSE stream
        let stream = ReceiverStream::new(rx)
            .map(|msg| Ok(Event::default().data(msg)));
        
        Ok(Sse::new(stream))
    }
}
```

### Protocol Handler Implementation

```rust
pub struct ProtocolHandler {
    tool_registry: Arc<RwLock<ToolRegistry>>,
    session_manager: Arc<SessionManager>,
    auth_manager: Arc<AuthManager>,
    performance_monitor: Arc<PerformanceMonitor>,
}

#[async_trait]
impl MessageHandler for ProtocolHandler {
    async fn handle_message(
        &self,
        session_id: &str,
        message: MCPMessage,
    ) -> Result<Option<MCPMessage>, MCPError> {
        // Record request start time for performance monitoring
        let request_id = self.performance_monitor.record_request_start(&message, session_id);
        
        let response = match &message {
            MCPMessage::Request { id, method, params } => {
                self.handle_request(session_id, id, method, params).await
            },
            MCPMessage::Notification { method, params } => {
                self.handle_notification(session_id, method, params).await?;
                None
            },
            MCPMessage::Response { id, result, error } => {
                self.handle_response(session_id, id, result, error).await?;
                None
            },
        };
        
        // Record request completion
        if let Some(ref resp) = response {
            self.performance_monitor.record_request_end(request_id, resp);
        }
        
        Ok(response)
    }
}

impl ProtocolHandler {
    async fn handle_request(
        &self,
        session_id: &str,
        id: &str,
        method: &str,
        params: &Option<serde_json::Value>,
    ) -> Option<MCPMessage> {
        let result = match method {
            "initialize" => self.handle_initialize(session_id, params).await,
            "tools/list" => self.handle_tools_list(session_id, params).await,
            "tools/call" => self.handle_tools_call(session_id, params).await,
            "resources/list" => self.handle_resources_list(session_id, params).await,
            "resources/read" => self.handle_resources_read(session_id, params).await,
            "prompts/list" => self.handle_prompts_list(session_id, params).await,
            "prompts/get" => self.handle_prompts_get(session_id, params).await,
            "logging/setLevel" => self.handle_logging_set_level(session_id, params).await,
            _ => Err(MCPError::MethodNotFound(method.to_string())),
        };
        
        match result {
            Ok(result) => Some(MCPMessage::Response {
                id: id.to_string(),
                result: Some(result),
                error: None,
            }),
            Err(error) => Some(MCPMessage::Response {
                id: id.to_string(),
                result: None,
                error: Some(MCPErrorResponse::from(error)),
            }),
        }
    }
    
    async fn handle_initialize(
        &self,
        session_id: &str,
        params: &Option<serde_json::Value>,
    ) -> Result<serde_json::Value, MCPError> {
        let init_params: InitializeParams = serde_json::from_value(
            params.clone().unwrap_or_default()
        )?;
        
        // Validate protocol version compatibility
        let protocol_version = self.negotiate_protocol_version(&init_params.protocol_version)?;
        
        // Create or update session
        self.session_manager.create_session(
            session_id,
            &init_params.client_info,
            &init_params.capabilities,
        ).await?;
        
        // Return server capabilities
        Ok(serde_json::to_value(InitializeResult {
            protocol_version,
            capabilities: ServerCapabilities {
                logging: Some(LoggingCapabilities {}),
                prompts: Some(PromptsCapabilities {
                    list_changed: Some(true),
                }),
                resources: Some(ResourcesCapabilities {
                    subscribe: Some(true),
                    list_changed: Some(true),
                }),
                tools: Some(ToolsCapabilities {
                    list_changed: Some(true),
                }),
                experimental: Some(HashMap::new()),
            },
            server_info: ServerInfo {
                name: "rust-ss-mcp-server".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
            },
        })?)
    }
}
```

### Performance Monitoring

```rust
pub struct PerformanceMonitor {
    metrics: Arc<RwLock<PerformanceMetrics>>,
    config: MonitoringConfig,
    alert_manager: AlertManager,
}

#[derive(Debug, Default)]
pub struct PerformanceMetrics {
    pub request_count: HashMap<String, u64>,
    pub response_times: HashMap<String, Vec<Duration>>,
    pub error_rates: HashMap<String, f64>,
    pub connection_count: u64,
    pub active_sessions: u64,
    pub memory_usage: u64,
    pub cpu_usage: f64,
}

impl PerformanceMonitor {
    pub fn record_request_start(&self, message: &MCPMessage, session_id: &str) -> RequestId {
        let request_id = RequestId::new();
        
        // Track metrics
        tokio::spawn({
            let metrics = self.metrics.clone();
            let method = Self::extract_method(message).to_string();
            async move {
                let mut metrics = metrics.write().await;
                *metrics.request_count.entry(method).or_insert(0) += 1;
            }
        });
        
        request_id
    }
    
    pub fn record_request_end(&self, request_id: RequestId, response: &MCPMessage) {
        let duration = request_id.elapsed();
        
        tokio::spawn({
            let metrics = self.metrics.clone();
            let method = Self::extract_method_from_response(response).to_string();
            async move {
                let mut metrics = metrics.write().await;
                metrics.response_times
                    .entry(method)
                    .or_insert_with(Vec::new)
                    .push(duration);
            }
        });
    }
    
    pub async fn get_metrics_snapshot(&self) -> PerformanceMetrics {
        self.metrics.read().await.clone()
    }
}
```

### Error Recovery and Circuit Breaking

```rust
pub struct CircuitBreaker {
    state: Arc<RwLock<CircuitState>>,
    config: CircuitBreakerConfig,
    metrics: Arc<RwLock<CircuitMetrics>>,
}

#[derive(Debug, Clone)]
pub enum CircuitState {
    Closed,
    Open { opened_at: Instant },
    HalfOpen,
}

impl CircuitBreaker {
    pub async fn call<F, T, E>(&self, operation: F) -> Result<T, CircuitBreakerError<E>>
    where
        F: Future<Output = Result<T, E>>,
        E: std::error::Error,
    {
        // Check circuit state
        let state = self.state.read().await.clone();
        match state {
            CircuitState::Open { opened_at } => {
                if opened_at.elapsed() > self.config.timeout {
                    // Transition to half-open
                    *self.state.write().await = CircuitState::HalfOpen;
                } else {
                    return Err(CircuitBreakerError::CircuitOpen);
                }
            },
            CircuitState::HalfOpen => {
                // Allow limited traffic through
            },
            CircuitState::Closed => {
                // Normal operation
            },
        }
        
        // Execute operation
        let start_time = Instant::now();
        let result = operation.await;
        let duration = start_time.elapsed();
        
        // Update metrics and state
        match &result {
            Ok(_) => self.record_success(duration).await,
            Err(_) => self.record_failure(duration).await,
        }
        
        result.map_err(CircuitBreakerError::OperationFailed)
    }
    
    async fn record_success(&self, duration: Duration) {
        let mut metrics = self.metrics.write().await;
        metrics.success_count += 1;
        metrics.total_duration += duration;
        
        // Check if we should close the circuit
        if matches!(*self.state.read().await, CircuitState::HalfOpen) {
            if metrics.success_rate() > self.config.success_threshold {
                *self.state.write().await = CircuitState::Closed;
            }
        }
    }
    
    async fn record_failure(&self, duration: Duration) {
        let mut metrics = self.metrics.write().await;
        metrics.failure_count += 1;
        metrics.total_duration += duration;
        
        // Check if we should open the circuit
        if metrics.failure_rate() > self.config.failure_threshold {
            *self.state.write().await = CircuitState::Open {
                opened_at: Instant::now(),
            };
        }
    }
}
```

## Configuration Examples

### Development Configuration

```json
{
  "transport": {
    "transport_type": "Http",
    "host": "localhost",
    "port": 3000,
    "tls_enabled": false,
    "max_connections": 100,
    "request_timeout": "30s",
    "max_request_size": "10MB",
    "compression": {
      "enabled": true,
      "algorithm": "gzip",
      "threshold": "1KB"
    }
  },
  "security": {
    "authentication": {
      "enabled": true,
      "method": "token",
      "token_validation": "strict",
      "token_expiry": "24h",
      "refresh_tokens": true
    },
    "authorization": {
      "enabled": true,
      "default_policy": "deny",
      "policies": [
        {
          "name": "agent-access",
          "resources": ["tools/*", "files/read", "files/write"],
          "principals": ["agent:*"],
          "actions": ["read", "write", "execute"]
        }
      ]
    },
    "rate_limiting": {
      "enabled": true,
      "requests_per_minute": 100,
      "burst_size": 20
    }
  },
  "performance": {
    "connection_pool": {
      "max_size": 50,
      "min_idle": 5,
      "max_lifetime": "1h"
    },
    "circuit_breaker": {
      "failure_threshold": 0.5,
      "success_threshold": 0.8,
      "timeout": "60s"
    }
  },
  "monitoring": {
    "metrics": {
      "enabled": true,
      "endpoint": "/metrics",
      "format": "prometheus"
    },
    "logging": {
      "level": "info",
      "format": "json",
      "audit": true
    },
    "tracing": {
      "enabled": true,
      "sampler": "probabilistic",
      "sampler_param": 0.1
    }
  }
}
```

### Production Configuration

```json
{
  "transport": {
    "transport_type": "StreamableHttp",
    "host": "0.0.0.0",
    "port": 443,
    "tls_enabled": true,
    "max_connections": 1000,
    "request_timeout": "60s",
    "max_request_size": "50MB",
    "compression": {
      "enabled": true,
      "algorithm": "br",
      "threshold": "512B"
    }
  },
  "security": {
    "authentication": {
      "enabled": true,
      "method": "mutual_tls",
      "certificate_validation": "strict",
      "revocation_check": true
    },
    "authorization": {
      "enabled": true,
      "default_policy": "deny",
      "rbac_enabled": true
    },
    "encryption": {
      "enabled": true,
      "algorithm": "AES-256-GCM",
      "key_rotation_interval": "24h"
    },
    "rate_limiting": {
      "enabled": true,
      "requests_per_minute": 1000,
      "burst_size": 100,
      "distributed": true
    }
  },
  "performance": {
    "load_balancer": {
      "enabled": true,
      "strategy": "least_connections",
      "health_check_interval": "30s"
    },
    "connection_pool": {
      "max_size": 200,
      "min_idle": 20,
      "max_lifetime": "2h"
    },
    "circuit_breaker": {
      "failure_threshold": 0.1,
      "success_threshold": 0.9,
      "timeout": "30s"
    }
  }
}
```

## Testing Strategy

### Unit Tests

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    
    #[tokio::test]
    async fn test_server_initialization() {
        let config = MCPConfig::default();
        let server = MCPServer::new(config).await.unwrap();
        
        assert_eq!(server.state.read().await.clone(), ServerState::Initializing);
    }
    
    #[tokio::test]
    async fn test_protocol_negotiation() {
        let handler = ProtocolHandler::new(/* dependencies */);
        
        let init_params = serde_json::json!({
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        });
        
        let result = handler.handle_initialize("test-session", &Some(init_params)).await;
        assert!(result.is_ok());
    }
}
```

### Integration Tests

```rust
#[tokio::test]
async fn test_full_mcp_workflow() {
    // Start test server
    let config = test_config();
    let mut server = MCPServer::new(config).await.unwrap();
    
    tokio::spawn(async move {
        server.start().await.unwrap();
    });
    
    // Wait for server to start
    tokio::time::sleep(Duration::from_millis(100)).await;
    
    // Create test client
    let client = MCPClient::new("http://localhost:3000/mcp").await.unwrap();
    
    // Test initialization
    let init_result = client.initialize(InitializeParams {
        protocol_version: "2024-11-05".to_string(),
        capabilities: ClientCapabilities::default(),
        client_info: ClientInfo {
            name: "test-client".to_string(),
            version: "1.0.0".to_string(),
        },
    }).await.unwrap();
    
    assert_eq!(init_result.protocol_version, "2024-11-05");
    
    // Test tool listing
    let tools = client.list_tools().await.unwrap();
    assert!(!tools.is_empty());
    
    // Test tool calling
    let tool_result = client.call_tool("echo", json!({"message": "hello"})).await.unwrap();
    assert!(tool_result.is_success());
}
```

This server integration provides a robust foundation for MCP protocol handling in the RUST-SS system, with comprehensive error handling, performance monitoring, and security features.