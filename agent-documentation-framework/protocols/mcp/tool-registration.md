# MCP Tool Registration System

## Overview

The Tool Registration System provides dynamic tool discovery, registration, and execution capabilities for the RUST-SS MCP implementation. This system supports runtime tool registration, capability negotiation, schema validation, and secure tool execution with comprehensive monitoring and error handling.

## Architecture

### Core Components

```rust
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use schemars::JsonSchema;

pub struct ToolRegistry {
    tools: HashMap<String, ToolRegistration>,
    capabilities: HashMap<String, ToolCapability>,
    validators: HashMap<String, Box<dyn SchemaValidator + Send + Sync>>,
    executors: HashMap<String, Box<dyn ToolExecutor + Send + Sync>>,
    metrics: ToolMetrics,
    security_policies: HashMap<String, SecurityPolicy>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolRegistration {
    pub name: String,
    pub version: String,
    pub description: String,
    pub category: String,
    pub tags: Vec<String>,
    pub input_schema: JsonSchema,
    pub output_schema: Option<JsonSchema>,
    pub capabilities: Vec<ToolCapability>,
    pub security_requirements: SecurityRequirements,
    pub performance_hints: PerformanceHints,
    pub metadata: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCapability {
    pub name: String,
    pub version: String,
    pub required: bool,
    pub supported_protocol_versions: Vec<ProtocolVersion>,
    pub dependencies: Vec<String>,
    pub feature_flags: HashMap<String, bool>,
}
```

### Tool Discovery and Registration

#### Dynamic Tool Registration

```rust
impl ToolRegistry {
    pub async fn register_tool<T>(&mut self, tool: T) -> Result<(), ToolRegistrationError>
    where
        T: Tool + Send + Sync + 'static,
    {
        let registration = tool.registration();
        
        // Validate tool registration
        self.validate_registration(&registration).await?;
        
        // Check for conflicts
        self.check_conflicts(&registration).await?;
        
        // Create executor wrapper
        let executor = Arc::new(ToolExecutorWrapper::new(Box::new(tool)));
        
        // Register tool components
        self.tools.insert(registration.name.clone(), registration.clone());
        self.executors.insert(registration.name.clone(), executor);
        
        // Update capabilities
        self.update_capabilities(&registration).await;
        
        // Emit registration event
        self.emit_tool_registered(&registration).await;
        
        log::info!("Tool registered: {} v{}", registration.name, registration.version);
        Ok(())
    }
    
    pub async fn discover_tools(&self, criteria: &DiscoveryQuery) -> Vec<ToolInfo> {
        let mut results = Vec::new();
        
        for (name, registration) in &self.tools {
            if self.matches_criteria(registration, criteria) {
                results.push(ToolInfo {
                    name: name.clone(),
                    description: registration.description.clone(),
                    version: registration.version.clone(),
                    category: registration.category.clone(),
                    tags: registration.tags.clone(),
                    capabilities: registration.capabilities.clone(),
                    input_schema: registration.input_schema.clone(),
                    output_schema: registration.output_schema.clone(),
                });
            }
        }
        
        // Sort by relevance score
        results.sort_by(|a, b| {
            self.calculate_relevance_score(a, criteria)
                .partial_cmp(&self.calculate_relevance_score(b, criteria))
                .unwrap_or(std::cmp::Ordering::Equal)
                .reverse()
        });
        
        results
    }
    
    async fn validate_registration(&self, registration: &ToolRegistration) -> Result<(), ToolRegistrationError> {
        // Name validation
        if registration.name.is_empty() {
            return Err(ToolRegistrationError::InvalidName("Name cannot be empty".to_string()));
        }
        
        // Version validation
        semver::Version::parse(&registration.version)
            .map_err(|e| ToolRegistrationError::InvalidVersion(e.to_string()))?;
        
        // Schema validation
        self.validate_schema(&registration.input_schema).await?;
        if let Some(ref output_schema) = registration.output_schema {
            self.validate_schema(output_schema).await?;
        }
        
        // Capability validation
        for capability in &registration.capabilities {
            self.validate_capability(capability).await?;
        }
        
        // Security validation
        self.validate_security_requirements(&registration.security_requirements).await?;
        
        Ok(())
    }
}
```

#### Tool Interface Definition

```rust
#[async_trait]
pub trait Tool {
    fn registration(&self) -> ToolRegistration;
    
    async fn execute(
        &self,
        input: ToolInput,
        context: ToolContext,
    ) -> Result<ToolOutput, ToolExecutionError>;
    
    async fn validate_input(&self, input: &ToolInput) -> Result<(), ValidationError> {
        // Default implementation using JSON schema
        let schema = self.registration().input_schema;
        validate_against_schema(input, &schema)
    }
    
    async fn cleanup(&self) -> Result<(), ToolExecutionError> {
        // Default: no cleanup needed
        Ok(())
    }
    
    fn health_check(&self) -> ToolHealthStatus {
        ToolHealthStatus::Healthy
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolInput {
    pub arguments: serde_json::Value,
    pub context: HashMap<String, serde_json::Value>,
    pub metadata: ToolMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolOutput {
    pub content: Vec<ContentBlock>,
    pub is_error: bool,
    pub metadata: HashMap<String, serde_json::Value>,
    pub performance_info: Option<PerformanceInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentBlock {
    pub content_type: ContentType,
    pub data: serde_json::Value,
    pub encoding: Option<String>,
    pub mime_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContentType {
    Text,
    Image,
    File,
    Json,
    Binary,
}
```

### Example Tool Implementations

#### File System Tool

```rust
pub struct FileSystemTool {
    base_path: PathBuf,
    security_policy: SecurityPolicy,
    performance_monitor: Arc<PerformanceMonitor>,
}

impl FileSystemTool {
    pub fn new(base_path: PathBuf, security_policy: SecurityPolicy) -> Self {
        Self {
            base_path,
            security_policy,
            performance_monitor: Arc::new(PerformanceMonitor::new()),
        }
    }
}

#[async_trait]
impl Tool for FileSystemTool {
    fn registration(&self) -> ToolRegistration {
        ToolRegistration {
            name: "filesystem".to_string(),
            version: "1.0.0".to_string(),
            description: "File system operations with security controls".to_string(),
            category: "system".to_string(),
            tags: vec!["file", "io", "system".to_string()],
            input_schema: json_schema! {
                "type": "object",
                "properties": {
                    "action": {
                        "type": "string",
                        "enum": ["read", "write", "list", "delete", "stat"]
                    },
                    "path": {
                        "type": "string",
                        "description": "File or directory path"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content for write operations"
                    },
                    "encoding": {
                        "type": "string",
                        "enum": ["utf8", "binary", "base64"],
                        "default": "utf8"
                    }
                },
                "required": ["action", "path"]
            },
            output_schema: Some(json_schema! {
                "type": "object",
                "properties": {
                    "success": {"type": "boolean"},
                    "data": {"type": "string"},
                    "metadata": {"type": "object"},
                    "error": {"type": "string"}
                },
                "required": ["success"]
            }),
            capabilities: vec![
                ToolCapability {
                    name: "file-operations".to_string(),
                    version: "1.0.0".to_string(),
                    required: true,
                    supported_protocol_versions: vec![
                        ProtocolVersion::new(2024, 11, 5)
                    ],
                    dependencies: vec![],
                    feature_flags: hashmap! {
                        "sandboxing" => true,
                        "audit_logging" => true,
                        "rate_limiting" => true,
                    },
                }
            ],
            security_requirements: SecurityRequirements {
                authentication_required: true,
                authorization_required: true,
                audit_logging: true,
                sandboxing: true,
                allowed_paths: vec![],
                blocked_paths: vec!["/etc".to_string(), "/root".to_string()],
                max_file_size: 10 * 1024 * 1024, // 10MB
                allowed_operations: vec!["read".to_string(), "write".to_string(), "list".to_string()],
            },
            performance_hints: PerformanceHints {
                expected_duration: Duration::from_millis(100),
                max_duration: Duration::from_secs(30),
                memory_usage: 1024 * 1024, // 1MB
                cpu_intensive: false,
                io_intensive: true,
            },
            metadata: hashmap! {
                "author" => json!("RUST-SS Team"),
                "license" => json!("MIT"),
                "documentation" => json!("https://docs.rust-ss.dev/tools/filesystem"),
            },
        }
    }
    
    async fn execute(
        &self,
        input: ToolInput,
        context: ToolContext,
    ) -> Result<ToolOutput, ToolExecutionError> {
        let start_time = Instant::now();
        
        // Extract parameters
        let action = input.arguments["action"]
            .as_str()
            .ok_or_else(|| ToolExecutionError::InvalidInput("Missing action".to_string()))?;
        
        let path = input.arguments["path"]
            .as_str()
            .ok_or_else(|| ToolExecutionError::InvalidInput("Missing path".to_string()))?;
        
        // Security validation
        self.validate_path(path, &context).await?;
        self.validate_operation(action, &context).await?;
        
        // Execute operation
        let result = match action {
            "read" => self.read_file(path, &input).await?,
            "write" => self.write_file(path, &input).await?,
            "list" => self.list_directory(path).await?,
            "delete" => self.delete_file(path, &context).await?,
            "stat" => self.stat_file(path).await?,
            _ => return Err(ToolExecutionError::UnsupportedOperation(action.to_string())),
        };
        
        // Record performance metrics
        let duration = start_time.elapsed();
        self.performance_monitor.record_execution(action, duration, result.is_success());
        
        Ok(result)
    }
}

impl FileSystemTool {
    async fn read_file(&self, path: &str, input: &ToolInput) -> Result<ToolOutput, ToolExecutionError> {
        let full_path = self.resolve_path(path)?;
        let encoding = input.arguments.get("encoding")
            .and_then(|e| e.as_str())
            .unwrap_or("utf8");
        
        let content = match encoding {
            "utf8" => {
                let bytes = tokio::fs::read(&full_path).await
                    .map_err(|e| ToolExecutionError::IoError(e.to_string()))?;
                String::from_utf8(bytes)
                    .map_err(|e| ToolExecutionError::EncodingError(e.to_string()))?
            },
            "binary" => {
                let bytes = tokio::fs::read(&full_path).await
                    .map_err(|e| ToolExecutionError::IoError(e.to_string()))?;
                base64::encode(bytes)
            },
            "base64" => {
                let bytes = tokio::fs::read(&full_path).await
                    .map_err(|e| ToolExecutionError::IoError(e.to_string()))?;
                base64::encode(bytes)
            },
            _ => return Err(ToolExecutionError::InvalidInput("Invalid encoding".to_string())),
        };
        
        Ok(ToolOutput {
            content: vec![ContentBlock {
                content_type: ContentType::Text,
                data: json!(content),
                encoding: Some(encoding.to_string()),
                mime_type: Some(Self::detect_mime_type(&full_path)),
            }],
            is_error: false,
            metadata: hashmap! {
                "file_size" => json!(content.len()),
                "encoding" => json!(encoding),
                "path" => json!(path),
            },
            performance_info: None,
        })
    }
    
    async fn validate_path(&self, path: &str, context: &ToolContext) -> Result<(), ToolExecutionError> {
        let full_path = self.resolve_path(path)?;
        
        // Check if path is within allowed base path
        if !full_path.starts_with(&self.base_path) {
            return Err(ToolExecutionError::SecurityViolation(
                "Path outside allowed base directory".to_string()
            ));
        }
        
        // Check blocked paths
        for blocked in &self.security_policy.blocked_paths {
            if full_path.starts_with(blocked) {
                return Err(ToolExecutionError::SecurityViolation(
                    format!("Access to {} is blocked", blocked)
                ));
            }
        }
        
        // Check user permissions
        if let Some(user_id) = context.user_id {
            if !self.security_policy.check_user_access(user_id, &full_path) {
                return Err(ToolExecutionError::SecurityViolation(
                    "Insufficient permissions".to_string()
                ));
            }
        }
        
        Ok(())
    }
}
```

#### Web Request Tool

```rust
pub struct WebRequestTool {
    client: reqwest::Client,
    security_policy: WebSecurityPolicy,
    rate_limiter: RateLimiter,
}

#[async_trait]
impl Tool for WebRequestTool {
    fn registration(&self) -> ToolRegistration {
        ToolRegistration {
            name: "web".to_string(),
            version: "1.0.0".to_string(),
            description: "HTTP/HTTPS web requests with security controls".to_string(),
            category: "network".to_string(),
            tags: vec!["http", "web", "api".to_string()],
            input_schema: json_schema! {
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "format": "uri",
                        "description": "Target URL"
                    },
                    "method": {
                        "type": "string",
                        "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD"],
                        "default": "GET"
                    },
                    "headers": {
                        "type": "object",
                        "description": "HTTP headers"
                    },
                    "body": {
                        "type": "string",
                        "description": "Request body"
                    },
                    "timeout": {
                        "type": "number",
                        "description": "Request timeout in seconds",
                        "default": 30
                    },
                    "follow_redirects": {
                        "type": "boolean",
                        "default": true
                    },
                    "validate_ssl": {
                        "type": "boolean",
                        "default": true
                    }
                },
                "required": ["url"]
            },
            capabilities: vec![
                ToolCapability {
                    name: "http-requests".to_string(),
                    version: "1.0.0".to_string(),
                    required: true,
                    supported_protocol_versions: vec![ProtocolVersion::new(2024, 11, 5)],
                    dependencies: vec![],
                    feature_flags: hashmap! {
                        "ssl_validation" => true,
                        "rate_limiting" => true,
                        "domain_filtering" => true,
                    },
                }
            ],
            security_requirements: SecurityRequirements {
                authentication_required: true,
                authorization_required: true,
                audit_logging: true,
                sandboxing: true,
                allowed_domains: vec!["*.example.com".to_string()],
                blocked_domains: vec!["malicious.com".to_string()],
                max_request_size: 10 * 1024 * 1024, // 10MB
                allowed_schemes: vec!["https".to_string()],
            },
            performance_hints: PerformanceHints {
                expected_duration: Duration::from_secs(5),
                max_duration: Duration::from_secs(60),
                memory_usage: 5 * 1024 * 1024, // 5MB
                cpu_intensive: false,
                io_intensive: true,
            },
            metadata: hashmap! {
                "rate_limit" => json!("100/hour"),
                "max_response_size" => json!("50MB"),
            },
        }
    }
    
    async fn execute(
        &self,
        input: ToolInput,
        context: ToolContext,
    ) -> Result<ToolOutput, ToolExecutionError> {
        // Rate limiting check
        self.rate_limiter.check(&context.session_id).await?;
        
        // Extract parameters
        let url = input.arguments["url"]
            .as_str()
            .ok_or_else(|| ToolExecutionError::InvalidInput("Missing URL".to_string()))?;
        
        // Security validation
        self.validate_url(url).await?;
        
        // Build request
        let method = input.arguments.get("method")
            .and_then(|m| m.as_str())
            .unwrap_or("GET");
        
        let mut request_builder = match method {
            "GET" => self.client.get(url),
            "POST" => self.client.post(url),
            "PUT" => self.client.put(url),
            "DELETE" => self.client.delete(url),
            "PATCH" => self.client.patch(url),
            "HEAD" => self.client.head(url),
            _ => return Err(ToolExecutionError::UnsupportedOperation(method.to_string())),
        };
        
        // Add headers
        if let Some(headers) = input.arguments.get("headers") {
            if let Some(headers_obj) = headers.as_object() {
                for (key, value) in headers_obj {
                    if let Some(value_str) = value.as_str() {
                        request_builder = request_builder.header(key, value_str);
                    }
                }
            }
        }
        
        // Add body
        if let Some(body) = input.arguments.get("body") {
            if let Some(body_str) = body.as_str() {
                request_builder = request_builder.body(body_str);
            }
        }
        
        // Set timeout
        let timeout = input.arguments.get("timeout")
            .and_then(|t| t.as_u64())
            .unwrap_or(30);
        request_builder = request_builder.timeout(Duration::from_secs(timeout));
        
        // Execute request
        let start_time = Instant::now();
        let response = request_builder.send().await
            .map_err(|e| ToolExecutionError::NetworkError(e.to_string()))?;
        
        let status = response.status();
        let headers: HashMap<String, String> = response.headers()
            .iter()
            .map(|(k, v)| (k.to_string(), v.to_str().unwrap_or("").to_string()))
            .collect();
        
        let body = response.text().await
            .map_err(|e| ToolExecutionError::NetworkError(e.to_string()))?;
        
        let duration = start_time.elapsed();
        
        Ok(ToolOutput {
            content: vec![ContentBlock {
                content_type: ContentType::Json,
                data: json!({
                    "status": status.as_u16(),
                    "headers": headers,
                    "body": body,
                    "url": url,
                    "method": method
                }),
                encoding: Some("utf8".to_string()),
                mime_type: Some("application/json".to_string()),
            }],
            is_error: !status.is_success(),
            metadata: hashmap! {
                "status_code" => json!(status.as_u16()),
                "response_time_ms" => json!(duration.as_millis()),
                "response_size" => json!(body.len()),
            },
            performance_info: Some(PerformanceInfo {
                duration,
                memory_used: body.len(),
                cpu_time: Duration::from_millis(10),
            }),
        })
    }
}
```

### Tool Execution Framework

#### Secure Tool Executor

```rust
pub struct SecureToolExecutor {
    sandboxes: HashMap<String, Sandbox>,
    monitors: HashMap<String, ExecutionMonitor>,
    security_policies: HashMap<String, SecurityPolicy>,
}

impl SecureToolExecutor {
    pub async fn execute_tool(
        &self,
        tool_name: &str,
        input: ToolInput,
        context: ToolContext,
    ) -> Result<ToolOutput, ToolExecutionError> {
        // Get tool and policies
        let tool = self.get_tool(tool_name)?;
        let security_policy = self.security_policies.get(tool_name)
            .ok_or_else(|| ToolExecutionError::SecurityPolicyNotFound(tool_name.to_string()))?;
        
        // Pre-execution security checks
        self.validate_execution_context(&context, security_policy).await?;
        self.validate_input_security(&input, security_policy).await?;
        
        // Create execution environment
        let sandbox = self.create_sandbox(tool_name, security_policy).await?;
        let monitor = ExecutionMonitor::new(&context);
        
        // Execute with monitoring and timeout
        let execution_future = async {
            monitor.start().await;
            let result = tool.execute(input, context).await;
            monitor.stop().await;
            result
        };
        
        let timeout_duration = security_policy.max_execution_time
            .unwrap_or(Duration::from_secs(300));
        
        let result = tokio::time::timeout(timeout_duration, execution_future)
            .await
            .map_err(|_| ToolExecutionError::Timeout(timeout_duration))?;
        
        // Post-execution validation
        if let Ok(ref output) = result {
            self.validate_output_security(output, security_policy).await?;
        }
        
        // Cleanup
        sandbox.cleanup().await?;
        
        result
    }
}
```

### Tool Registry Management

#### Registry Operations

```rust
impl ToolRegistry {
    pub async fn list_tools(&self, query: Option<&ToolQuery>) -> Vec<ToolInfo> {
        let mut tools: Vec<_> = self.tools.values()
            .map(|reg| ToolInfo::from(reg))
            .collect();
        
        // Apply filters
        if let Some(query) = query {
            tools.retain(|tool| self.matches_query(tool, query));
        }
        
        // Sort by name
        tools.sort_by(|a, b| a.name.cmp(&b.name));
        
        tools
    }
    
    pub async fn get_tool_schema(&self, tool_name: &str) -> Option<ToolSchema> {
        self.tools.get(tool_name).map(|reg| ToolSchema {
            input_schema: reg.input_schema.clone(),
            output_schema: reg.output_schema.clone(),
            capabilities: reg.capabilities.clone(),
        })
    }
    
    pub async fn health_check_tools(&self) -> HashMap<String, ToolHealthStatus> {
        let mut results = HashMap::new();
        
        for (name, tool) in &self.tools {
            let status = if let Some(executor) = self.executors.get(name) {
                executor.health_check()
            } else {
                ToolHealthStatus::Unavailable
            };
            
            results.insert(name.clone(), status);
        }
        
        results
    }
    
    pub async fn unregister_tool(&mut self, tool_name: &str) -> Result<(), ToolRegistrationError> {
        // Check for active executions
        if self.has_active_executions(tool_name).await {
            return Err(ToolRegistrationError::ToolInUse(tool_name.to_string()));
        }
        
        // Remove from registry
        self.tools.remove(tool_name);
        self.executors.remove(tool_name);
        self.capabilities.remove(tool_name);
        self.security_policies.remove(tool_name);
        
        // Emit unregistration event
        self.emit_tool_unregistered(tool_name).await;
        
        log::info!("Tool unregistered: {}", tool_name);
        Ok(())
    }
}
```

## Testing Framework

### Tool Testing Infrastructure

```rust
pub struct ToolTestFramework {
    registry: ToolRegistry,
    mock_context: ToolContext,
    test_data: TestDataManager,
}

impl ToolTestFramework {
    pub async fn test_tool_registration(&mut self, tool: impl Tool + Send + Sync + 'static) -> TestResult {
        // Test registration
        let registration_result = self.registry.register_tool(tool).await;
        assert!(registration_result.is_ok(), "Tool registration failed");
        
        // Test discovery
        let tools = self.registry.list_tools(None).await;
        assert!(!tools.is_empty(), "Tool not discoverable after registration");
        
        TestResult::Passed
    }
    
    pub async fn test_tool_execution(&self, tool_name: &str, test_cases: Vec<ToolTestCase>) -> Vec<TestResult> {
        let mut results = Vec::new();
        
        for test_case in test_cases {
            let result = self.execute_test_case(tool_name, test_case).await;
            results.push(result);
        }
        
        results
    }
    
    async fn execute_test_case(&self, tool_name: &str, test_case: ToolTestCase) -> TestResult {
        match self.registry.execute_tool(tool_name, test_case.input, self.mock_context.clone()).await {
            Ok(output) => {
                if test_case.expected_success {
                    if self.validate_output(&output, &test_case.expected_output) {
                        TestResult::Passed
                    } else {
                        TestResult::Failed("Output validation failed".to_string())
                    }
                } else {
                    TestResult::Failed("Expected failure but got success".to_string())
                }
            },
            Err(error) => {
                if test_case.expected_success {
                    TestResult::Failed(format!("Unexpected error: {}", error))
                } else {
                    TestResult::Passed
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_file_system_tool() {
        let mut framework = ToolTestFramework::new();
        let tool = FileSystemTool::new(
            PathBuf::from("/tmp/test"),
            SecurityPolicy::default()
        );
        
        // Test registration
        framework.test_tool_registration(tool).await;
        
        // Test execution
        let test_cases = vec![
            ToolTestCase {
                name: "read_existing_file".to_string(),
                input: ToolInput {
                    arguments: json!({
                        "action": "read",
                        "path": "test.txt"
                    }),
                    context: HashMap::new(),
                    metadata: ToolMetadata::default(),
                },
                expected_success: true,
                expected_output: Some(json!({
                    "content": [{"content_type": "Text", "data": "test content"}],
                    "is_error": false
                })),
            }
        ];
        
        let results = framework.test_tool_execution("filesystem", test_cases).await;
        assert!(results.iter().all(|r| matches!(r, TestResult::Passed)));
    }
}
```

This tool registration system provides a comprehensive foundation for dynamic tool management in the RUST-SS MCP implementation, with strong security, performance monitoring, and testing capabilities.