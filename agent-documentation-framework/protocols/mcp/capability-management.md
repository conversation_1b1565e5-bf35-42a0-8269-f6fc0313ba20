# MCP Capability Management

## Overview

The Capability Management system handles protocol version negotiation, feature discovery, and capability matching between MCP clients and servers in the RUST-SS ecosystem. This system ensures backward compatibility, graceful degradation, and optimal feature utilization across different protocol versions and implementations.

## Architecture

### Core Components

```rust
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use semver::Version;
use serde::{Deserialize, Serialize};

pub struct CapabilityManager {
    protocol_versions: Vec<ProtocolVersion>,
    server_capabilities: ServerCapabilities,
    client_capabilities: HashMap<String, ClientCapabilities>,
    compatibility_matrix: CompatibilityMatrix,
    feature_flags: FeatureFlags,
    negotiation_cache: NegotiationCache,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct ProtocolVersion {
    pub major: u16,
    pub minor: u16,
    pub patch: u16,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerCapabilities {
    pub logging: Option<LoggingCapabilities>,
    pub prompts: Option<PromptsCapabilities>,
    pub resources: Option<ResourcesCapabilities>,
    pub tools: Option<ToolsCapabilities>,
    pub sampling: Option<SamplingCapabilities>,
    pub experimental: Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientCapabilities {
    pub roots: Option<RootsCapabilities>,
    pub sampling: Option<SamplingCapabilities>,
    pub experimental: Option<HashMap<String, serde_json::Value>>,
}
```

### Protocol Version Negotiation

#### Version Compatibility Engine

```rust
impl CapabilityManager {
    pub fn new() -> Self {
        Self {
            protocol_versions: vec![
                ProtocolVersion::new(2024, 11, 5),
                ProtocolVersion::new(2024, 10, 1),
                ProtocolVersion::new(2024, 9, 1),
            ],
            server_capabilities: Self::default_server_capabilities(),
            client_capabilities: HashMap::new(),
            compatibility_matrix: CompatibilityMatrix::new(),
            feature_flags: FeatureFlags::new(),
            negotiation_cache: NegotiationCache::new(),
        }
    }
    
    pub async fn negotiate_protocol(
        &mut self,
        client_version: &str,
        client_capabilities: &ClientCapabilities,
        client_info: &ClientInfo,
    ) -> Result<NegotiationResult, CapabilityError> {
        // Parse client protocol version
        let requested_version = self.parse_protocol_version(client_version)?;
        
        // Find compatible version
        let negotiated_version = self.find_compatible_version(&requested_version)?;
        
        // Negotiate capabilities
        let negotiated_capabilities = self.negotiate_capabilities(
            &negotiated_version,
            client_capabilities,
        ).await?;
        
        // Create session-specific capability set
        let session_id = uuid::Uuid::new_v4().to_string();
        self.client_capabilities.insert(session_id.clone(), client_capabilities.clone());
        
        // Cache negotiation result
        let result = NegotiationResult {
            session_id,
            protocol_version: negotiated_version.to_string(),
            server_capabilities: negotiated_capabilities,
            server_info: ServerInfo {
                name: "rust-ss-mcp-server".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
            },
            feature_compatibility: self.calculate_feature_compatibility(
                &negotiated_version,
                client_capabilities,
            ),
        };
        
        self.negotiation_cache.insert(session_id.clone(), result.clone());
        
        Ok(result)
    }
    
    fn find_compatible_version(&self, requested: &ProtocolVersion) -> Result<ProtocolVersion, CapabilityError> {
        // Try exact match first
        if self.protocol_versions.contains(requested) {
            return Ok(requested.clone());
        }
        
        // Find compatible version using semver rules
        let mut compatible_versions: Vec<_> = self.protocol_versions
            .iter()
            .filter(|v| self.is_version_compatible(v, requested))
            .collect();
        
        if compatible_versions.is_empty() {
            return Err(CapabilityError::IncompatibleProtocolVersion {
                requested: requested.to_string(),
                supported: self.protocol_versions.iter()
                    .map(|v| v.to_string())
                    .collect(),
            });
        }
        
        // Sort by preference (highest compatible version)
        compatible_versions.sort_by(|a, b| b.cmp(a));
        
        Ok(compatible_versions[0].clone())
    }
    
    fn is_version_compatible(&self, server_version: &ProtocolVersion, client_version: &ProtocolVersion) -> bool {
        // Major version must match
        if server_version.major != client_version.major {
            return false;
        }
        
        // Minor version compatibility
        match server_version.major {
            2024 => {
                // Server can support older minor versions
                server_version.minor >= client_version.minor
            },
            _ => {
                // Future major versions might have different compatibility rules
                server_version == client_version
            }
        }
    }
}
```

#### Capability Negotiation

```rust
impl CapabilityManager {
    async fn negotiate_capabilities(
        &self,
        protocol_version: &ProtocolVersion,
        client_capabilities: &ClientCapabilities,
    ) -> Result<ServerCapabilities, CapabilityError> {
        let mut negotiated = self.server_capabilities.clone();
        
        // Adjust capabilities based on protocol version
        self.adjust_for_protocol_version(&mut negotiated, protocol_version);
        
        // Adjust capabilities based on client support
        self.adjust_for_client_capabilities(&mut negotiated, client_capabilities);
        
        // Apply feature flags
        self.apply_feature_flags(&mut negotiated).await;
        
        // Validate final capability set
        self.validate_capability_consistency(&negotiated)?;
        
        Ok(negotiated)
    }
    
    fn adjust_for_protocol_version(
        &self,
        capabilities: &mut ServerCapabilities,
        version: &ProtocolVersion,
    ) {
        // Sampling capability introduced in 2024.11.5
        if version < &ProtocolVersion::new(2024, 11, 5) {
            capabilities.sampling = None;
        }
        
        // Enhanced resource subscriptions in 2024.10.1
        if version < &ProtocolVersion::new(2024, 10, 1) {
            if let Some(ref mut resources) = capabilities.resources {
                resources.subscribe = Some(false);
            }
        }
        
        // List change notifications in 2024.9.1
        if version < &ProtocolVersion::new(2024, 9, 1) {
            if let Some(ref mut tools) = capabilities.tools {
                tools.list_changed = Some(false);
            }
            if let Some(ref mut resources) = capabilities.resources {
                resources.list_changed = Some(false);
            }
            if let Some(ref mut prompts) = capabilities.prompts {
                prompts.list_changed = Some(false);
            }
        }
    }
    
    fn adjust_for_client_capabilities(
        &self,
        capabilities: &mut ServerCapabilities,
        client_capabilities: &ClientCapabilities,
    ) {
        // Disable sampling if client doesn't support it
        if client_capabilities.sampling.is_none() {
            capabilities.sampling = None;
        }
        
        // Adjust resource capabilities based on client roots support
        if let Some(ref roots) = client_capabilities.roots {
            if let Some(ref mut resources) = capabilities.resources {
                // Enable advanced resource features if client supports roots
                if roots.list_changed.unwrap_or(false) {
                    resources.list_changed = Some(true);
                }
            }
        }
    }
    
    async fn apply_feature_flags(&self, capabilities: &mut ServerCapabilities) {
        // Apply runtime feature flags
        if !self.feature_flags.is_enabled("experimental_features").await {
            capabilities.experimental = None;
        }
        
        if !self.feature_flags.is_enabled("advanced_logging").await {
            capabilities.logging = None;
        }
        
        if !self.feature_flags.is_enabled("sampling_support").await {
            capabilities.sampling = None;
        }
    }
}
```

### Capability-Specific Implementations

#### Logging Capabilities

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingCapabilities {
    pub levels: Vec<LogLevel>,
    pub structured_logging: bool,
    pub audit_logging: bool,
    pub real_time_streaming: bool,
}

impl Default for LoggingCapabilities {
    fn default() -> Self {
        Self {
            levels: vec![
                LogLevel::Error,
                LogLevel::Warn,
                LogLevel::Info,
                LogLevel::Debug,
            ],
            structured_logging: true,
            audit_logging: true,
            real_time_streaming: false,
        }
    }
}

pub struct LoggingCapabilityHandler {
    current_level: LogLevel,
    structured_enabled: bool,
    audit_enabled: bool,
    stream_handlers: HashMap<String, LogStreamHandler>,
}

impl LoggingCapabilityHandler {
    pub async fn set_level(&mut self, session_id: &str, level: LogLevel) -> Result<(), CapabilityError> {
        // Validate capability
        let capabilities = self.get_session_capabilities(session_id)?;
        if !capabilities.logging.as_ref()
            .map(|l| l.levels.contains(&level))
            .unwrap_or(false) {
            return Err(CapabilityError::UnsupportedFeature(
                format!("Log level {:?} not supported", level)
            ));
        }
        
        self.current_level = level;
        self.notify_level_change(session_id, level).await;
        
        Ok(())
    }
    
    pub async fn enable_structured_logging(&mut self, session_id: &str, enabled: bool) -> Result<(), CapabilityError> {
        let capabilities = self.get_session_capabilities(session_id)?;
        if enabled && !capabilities.logging.as_ref()
            .map(|l| l.structured_logging)
            .unwrap_or(false) {
            return Err(CapabilityError::UnsupportedFeature(
                "Structured logging not supported".to_string()
            ));
        }
        
        self.structured_enabled = enabled;
        Ok(())
    }
}
```

#### Tools Capabilities

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolsCapabilities {
    pub list_changed: Option<bool>,
    pub dynamic_registration: Option<bool>,
    pub schema_validation: Option<bool>,
    pub execution_monitoring: Option<bool>,
    pub parallel_execution: Option<bool>,
}

pub struct ToolsCapabilityHandler {
    registry: Arc<ToolRegistry>,
    change_listeners: HashMap<String, ChangeListener>,
    execution_monitor: ExecutionMonitor,
}

impl ToolsCapabilityHandler {
    pub async fn list_tools(&self, session_id: &str) -> Result<Vec<ToolInfo>, CapabilityError> {
        let capabilities = self.get_session_capabilities(session_id)?;
        
        let tools = self.registry.list_tools(None).await;
        
        // Filter tools based on client capabilities
        let filtered_tools = if capabilities.tools.as_ref()
            .and_then(|t| t.schema_validation)
            .unwrap_or(false) {
            // Include full schema information
            tools
        } else {
            // Basic tool information only
            tools.into_iter()
                .map(|mut tool| {
                    tool.input_schema = JsonSchema::Bool(true); // Simplified schema
                    tool.output_schema = None;
                    tool
                })
                .collect()
        };
        
        Ok(filtered_tools)
    }
    
    pub async fn call_tool(
        &self,
        session_id: &str,
        tool_name: &str,
        arguments: serde_json::Value,
    ) -> Result<ToolCallResult, CapabilityError> {
        let capabilities = self.get_session_capabilities(session_id)?;
        
        // Check execution monitoring capability
        let monitor_execution = capabilities.tools.as_ref()
            .and_then(|t| t.execution_monitoring)
            .unwrap_or(false);
        
        let execution_id = if monitor_execution {
            Some(self.execution_monitor.start_execution(session_id, tool_name).await?)
        } else {
            None
        };
        
        // Execute tool
        let result = self.registry.execute_tool(
            tool_name,
            ToolInput {
                arguments,
                context: HashMap::new(),
                metadata: ToolMetadata::default(),
            },
            ToolContext {
                session_id: session_id.to_string(),
                user_id: None,
                permissions: Vec::new(),
            },
        ).await;
        
        // Update execution monitoring
        if let Some(execution_id) = execution_id {
            self.execution_monitor.complete_execution(execution_id, &result).await?;
        }
        
        match result {
            Ok(output) => Ok(ToolCallResult::Success {
                content: output.content,
                metadata: output.metadata,
            }),
            Err(error) => Ok(ToolCallResult::Error {
                code: error.error_code(),
                message: error.to_string(),
                data: error.error_data(),
            }),
        }
    }
    
    pub async fn register_for_changes(&mut self, session_id: &str) -> Result<(), CapabilityError> {
        let capabilities = self.get_session_capabilities(session_id)?;
        
        if !capabilities.tools.as_ref()
            .and_then(|t| t.list_changed)
            .unwrap_or(false) {
            return Err(CapabilityError::UnsupportedFeature(
                "Tool list change notifications not supported".to_string()
            ));
        }
        
        let listener = ChangeListener::new(session_id);
        self.change_listeners.insert(session_id.to_string(), listener);
        
        Ok(())
    }
}
```

#### Resources Capabilities

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourcesCapabilities {
    pub subscribe: Option<bool>,
    pub list_changed: Option<bool>,
    pub streaming: Option<bool>,
    pub templates: Option<bool>,
}

pub struct ResourcesCapabilityHandler {
    resource_manager: Arc<ResourceManager>,
    subscriptions: HashMap<String, Vec<ResourceSubscription>>,
    change_listeners: HashMap<String, ChangeListener>,
}

impl ResourcesCapabilityHandler {
    pub async fn list_resources(&self, session_id: &str) -> Result<Vec<ResourceInfo>, CapabilityError> {
        let capabilities = self.get_session_capabilities(session_id)?;
        
        let resources = self.resource_manager.list_resources().await;
        
        // Filter based on capabilities
        let filtered_resources = if capabilities.resources.as_ref()
            .and_then(|r| r.templates)
            .unwrap_or(false) {
            // Include template resources
            resources
        } else {
            // Exclude template resources
            resources.into_iter()
                .filter(|r| !r.is_template)
                .collect()
        };
        
        Ok(filtered_resources)
    }
    
    pub async fn read_resource(
        &self,
        session_id: &str,
        uri: &str,
    ) -> Result<ResourceContent, CapabilityError> {
        let capabilities = self.get_session_capabilities(session_id)?;
        
        let content = self.resource_manager.read_resource(uri).await
            .map_err(|e| CapabilityError::ResourceError(e.to_string()))?;
        
        // Apply streaming if supported
        if capabilities.resources.as_ref()
            .and_then(|r| r.streaming)
            .unwrap_or(false) && content.size > 1024 * 1024 { // 1MB threshold
            
            Ok(ResourceContent::Stream {
                uri: uri.to_string(),
                content_type: content.content_type,
                stream_id: self.create_resource_stream(session_id, uri).await?,
            })
        } else {
            Ok(ResourceContent::Direct {
                uri: uri.to_string(),
                content_type: content.content_type,
                data: content.data,
            })
        }
    }
    
    pub async fn subscribe_to_resource(
        &mut self,
        session_id: &str,
        uri: &str,
    ) -> Result<SubscriptionId, CapabilityError> {
        let capabilities = self.get_session_capabilities(session_id)?;
        
        if !capabilities.resources.as_ref()
            .and_then(|r| r.subscribe)
            .unwrap_or(false) {
            return Err(CapabilityError::UnsupportedFeature(
                "Resource subscriptions not supported".to_string()
            ));
        }
        
        let subscription = ResourceSubscription {
            id: SubscriptionId::new(),
            session_id: session_id.to_string(),
            uri: uri.to_string(),
            created_at: Instant::now(),
        };
        
        self.subscriptions
            .entry(session_id.to_string())
            .or_default()
            .push(subscription.clone());
        
        // Register with resource manager
        self.resource_manager.add_subscription(subscription.clone()).await?;
        
        Ok(subscription.id)
    }
}
```

### Feature Flag Management

```rust
pub struct FeatureFlags {
    flags: Arc<RwLock<HashMap<String, FeatureFlag>>>,
    evaluator: FeatureFlagEvaluator,
}

#[derive(Debug, Clone)]
pub struct FeatureFlag {
    pub name: String,
    pub enabled: bool,
    pub conditions: Vec<FeatureCondition>,
    pub rollout_percentage: f32,
    pub user_targeting: UserTargeting,
}

#[derive(Debug, Clone)]
pub enum FeatureCondition {
    ProtocolVersion(ProtocolVersionCondition),
    ClientType(ClientTypeCondition),
    SessionAttribute(SessionAttributeCondition),
    TimeWindow(TimeWindowCondition),
}

impl FeatureFlags {
    pub async fn is_enabled(&self, flag_name: &str) -> bool {
        self.is_enabled_for_context(flag_name, &EvaluationContext::default()).await
    }
    
    pub async fn is_enabled_for_context(
        &self,
        flag_name: &str,
        context: &EvaluationContext,
    ) -> bool {
        let flags = self.flags.read().await;
        
        if let Some(flag) = flags.get(flag_name) {
            self.evaluator.evaluate(flag, context).await
        } else {
            false
        }
    }
    
    pub async fn set_flag(&self, name: String, flag: FeatureFlag) {
        let mut flags = self.flags.write().await;
        flags.insert(name, flag);
    }
}

pub struct FeatureFlagEvaluator;

impl FeatureFlagEvaluator {
    pub async fn evaluate(&self, flag: &FeatureFlag, context: &EvaluationContext) -> bool {
        // Base enabled check
        if !flag.enabled {
            return false;
        }
        
        // Evaluate conditions
        for condition in &flag.conditions {
            if !self.evaluate_condition(condition, context).await {
                return false;
            }
        }
        
        // Rollout percentage check
        if flag.rollout_percentage < 100.0 {
            let hash = self.hash_context(context);
            let percentage = (hash % 100) as f32;
            if percentage >= flag.rollout_percentage {
                return false;
            }
        }
        
        // User targeting
        self.evaluate_user_targeting(&flag.user_targeting, context).await
    }
    
    async fn evaluate_condition(
        &self,
        condition: &FeatureCondition,
        context: &EvaluationContext,
    ) -> bool {
        match condition {
            FeatureCondition::ProtocolVersion(cond) => {
                if let Some(version) = &context.protocol_version {
                    self.evaluate_version_condition(cond, version)
                } else {
                    false
                }
            },
            FeatureCondition::ClientType(cond) => {
                if let Some(client_type) = &context.client_type {
                    cond.allowed_types.contains(client_type)
                } else {
                    false
                }
            },
            FeatureCondition::SessionAttribute(cond) => {
                if let Some(value) = context.session_attributes.get(&cond.attribute_name) {
                    self.evaluate_attribute_condition(cond, value)
                } else {
                    false
                }
            },
            FeatureCondition::TimeWindow(cond) => {
                let now = chrono::Utc::now();
                now >= cond.start_time && now <= cond.end_time
            },
        }
    }
}
```

### Compatibility Matrix

```rust
pub struct CompatibilityMatrix {
    version_compatibility: HashMap<ProtocolVersion, Vec<ProtocolVersion>>,
    feature_compatibility: HashMap<String, FeatureCompatibility>,
    deprecation_schedule: HashMap<String, DeprecationInfo>,
}

#[derive(Debug, Clone)]
pub struct FeatureCompatibility {
    pub introduced_version: ProtocolVersion,
    pub deprecated_version: Option<ProtocolVersion>,
    pub removed_version: Option<ProtocolVersion>,
    pub replacement_feature: Option<String>,
}

impl CompatibilityMatrix {
    pub fn new() -> Self {
        let mut matrix = Self {
            version_compatibility: HashMap::new(),
            feature_compatibility: HashMap::new(),
            deprecation_schedule: HashMap::new(),
        };
        
        matrix.initialize_compatibility_rules();
        matrix
    }
    
    fn initialize_compatibility_rules(&mut self) {
        // Version compatibility rules
        self.version_compatibility.insert(
            ProtocolVersion::new(2024, 11, 5),
            vec![
                ProtocolVersion::new(2024, 11, 5),
                ProtocolVersion::new(2024, 10, 1),
                ProtocolVersion::new(2024, 9, 1),
            ]
        );
        
        // Feature compatibility
        self.feature_compatibility.insert(
            "sampling".to_string(),
            FeatureCompatibility {
                introduced_version: ProtocolVersion::new(2024, 11, 5),
                deprecated_version: None,
                removed_version: None,
                replacement_feature: None,
            }
        );
        
        self.feature_compatibility.insert(
            "resource_subscriptions".to_string(),
            FeatureCompatibility {
                introduced_version: ProtocolVersion::new(2024, 10, 1),
                deprecated_version: None,
                removed_version: None,
                replacement_feature: None,
            }
        );
        
        self.feature_compatibility.insert(
            "list_change_notifications".to_string(),
            FeatureCompatibility {
                introduced_version: ProtocolVersion::new(2024, 9, 1),
                deprecated_version: None,
                removed_version: None,
                replacement_feature: None,
            }
        );
    }
    
    pub fn is_feature_available(
        &self,
        feature: &str,
        protocol_version: &ProtocolVersion,
    ) -> bool {
        if let Some(compatibility) = self.feature_compatibility.get(feature) {
            // Check if feature is introduced
            if protocol_version < &compatibility.introduced_version {
                return false;
            }
            
            // Check if feature is removed
            if let Some(removed_version) = &compatibility.removed_version {
                if protocol_version >= removed_version {
                    return false;
                }
            }
            
            true
        } else {
            false
        }
    }
    
    pub fn get_deprecation_info(&self, feature: &str) -> Option<&DeprecationInfo> {
        self.deprecation_schedule.get(feature)
    }
}
```

## Testing Framework

### Capability Testing

```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_protocol_negotiation() {
        let mut manager = CapabilityManager::new();
        
        let client_capabilities = ClientCapabilities {
            roots: Some(RootsCapabilities {
                list_changed: Some(true),
            }),
            sampling: None,
            experimental: None,
        };
        
        let result = manager.negotiate_protocol(
            "2024-11-05",
            &client_capabilities,
            &ClientInfo {
                name: "test-client".to_string(),
                version: "1.0.0".to_string(),
            },
        ).await;
        
        assert!(result.is_ok());
        let negotiation = result.unwrap();
        assert_eq!(negotiation.protocol_version, "2024-11-05");
    }
    
    #[tokio::test]
    async fn test_backward_compatibility() {
        let mut manager = CapabilityManager::new();
        
        // Test with older protocol version
        let result = manager.negotiate_protocol(
            "2024-09-01",
            &ClientCapabilities::default(),
            &ClientInfo {
                name: "legacy-client".to_string(),
                version: "0.9.0".to_string(),
            },
        ).await;
        
        assert!(result.is_ok());
        let negotiation = result.unwrap();
        
        // Sampling should not be available
        assert!(negotiation.server_capabilities.sampling.is_none());
    }
    
    #[tokio::test]
    async fn test_feature_flags() {
        let feature_flags = FeatureFlags::new();
        
        // Set experimental feature flag
        feature_flags.set_flag(
            "experimental_features".to_string(),
            FeatureFlag {
                name: "experimental_features".to_string(),
                enabled: true,
                conditions: vec![],
                rollout_percentage: 50.0,
                user_targeting: UserTargeting::default(),
            }
        ).await;
        
        let context = EvaluationContext {
            session_id: "test-session".to_string(),
            protocol_version: Some(ProtocolVersion::new(2024, 11, 5)),
            client_type: Some("test-client".to_string()),
            session_attributes: HashMap::new(),
        };
        
        // Feature should be enabled based on rollout
        let enabled = feature_flags.is_enabled_for_context("experimental_features", &context).await;
        // Note: This test might be flaky due to random rollout percentage
    }
}
```

This capability management system provides comprehensive protocol negotiation, feature discovery, and compatibility management for the RUST-SS MCP implementation.