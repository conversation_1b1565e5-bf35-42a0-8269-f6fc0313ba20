# MCP Error Handling

> **📖 Primary Reference**: See [RUST-SS Error Handling Patterns](../../architectural-concerns/error-handling.md) for the complete unified error handling framework.

## Overview

This document provides MCP-specific error handling patterns and JSON-RPC 2.0 compliance details. For comprehensive error handling strategies, circuit breakers, retry policies, and recovery mechanisms, refer to the [centralized error handling documentation](../../architectural-concerns/error-handling.md).

## Architecture

### Error Type Hierarchy

```rust
use std::fmt;
use std::error::Error as StdError;
use serde::{Deserialize, Serialize};
use thiserror::Error;

#[derive(Debug, Error)]
pub enum MCPError {
    // JSON-RPC 2.0 Standard Errors
    #[error("Parse error: {0}")]
    ParseError(String),
    
    #[error("Invalid request: {0}")]
    InvalidRequest(String),
    
    #[error("Method not found: {0}")]
    MethodNotFound(String),
    
    #[error("Invalid params: {0}")]
    InvalidParams(String),
    
    #[error("Internal error: {0}")]
    InternalError(String),
    
    // MCP-Specific Errors
    #[error("Protocol error: {0}")]
    ProtocolError(#[from] ProtocolError),
    
    #[error("Authentication error: {0}")]
    AuthenticationError(#[from] AuthenticationError),
    
    #[error("Authorization error: {0}")]
    AuthorizationError(#[from] AuthorizationError),
    
    #[error("Tool execution error: {0}")]
    ToolExecutionError(#[from] ToolExecutionError),
    
    #[error("Resource error: {0}")]
    ResourceError(#[from] ResourceError),
    
    #[error("Transport error: {0}")]
    TransportError(#[from] TransportError),
    
    #[error("Session error: {0}")]
    SessionError(#[from] SessionError),
    
    #[error("Capability error: {0}")]
    CapabilityError(#[from] CapabilityError),
    
    // System Errors
    #[error("Configuration error: {0}")]
    ConfigurationError(String),
    
    #[error("Rate limit exceeded")]
    RateLimitExceeded,
    
    #[error("Timeout: operation took longer than {timeout:?}")]
    Timeout { timeout: std::time::Duration },
    
    #[error("Circuit breaker open")]
    CircuitBreakerOpen,
    
    #[error("Service unavailable: {reason}")]
    ServiceUnavailable { reason: String },
}

#[derive(Debug, Error)]
pub enum ProtocolError {
    #[error("Incompatible protocol version: client={client}, server={server}")]
    IncompatibleVersion { client: String, server: String },
    
    #[error("Invalid message format: {0}")]
    InvalidMessageFormat(String),
    
    #[error("Unsupported capability: {0}")]
    UnsupportedCapability(String),
    
    #[error("Protocol violation: {0}")]
    ProtocolViolation(String),
}

#[derive(Debug, Error)]
pub enum ToolExecutionError {
    #[error("Tool not found: {0}")]
    ToolNotFound(String),
    
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    
    #[error("Execution failed: {0}")]
    ExecutionFailed(String),
    
    #[error("Security violation: {0}")]
    SecurityViolation(String),
    
    #[error("Resource limit exceeded: {0}")]
    ResourceLimitExceeded(String),
    
    #[error("Tool unavailable: {0}")]
    ToolUnavailable(String),
}
```

### JSON-RPC Error Response Format

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MCPErrorResponse {
    pub code: i32,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

impl MCPError {
    pub fn to_jsonrpc_error(&self) -> MCPErrorResponse {
        match self {
            MCPError::ParseError(msg) => MCPErrorResponse {
                code: -32700,
                message: "Parse error".to_string(),
                data: Some(json!({ "details": msg })),
            },
            MCPError::InvalidRequest(msg) => MCPErrorResponse {
                code: -32600,
                message: "Invalid Request".to_string(),
                data: Some(json!({ "details": msg })),
            },
            MCPError::MethodNotFound(method) => MCPErrorResponse {
                code: -32601,
                message: "Method not found".to_string(),
                data: Some(json!({ "method": method })),
            },
            MCPError::InvalidParams(msg) => MCPErrorResponse {
                code: -32602,
                message: "Invalid params".to_string(),
                data: Some(json!({ "details": msg })),
            },
            MCPError::InternalError(msg) => MCPErrorResponse {
                code: -32603,
                message: "Internal error".to_string(),
                data: Some(json!({ "details": msg, "request_id": uuid::Uuid::new_v4() })),
            },
            MCPError::AuthenticationError(_) => MCPErrorResponse {
                code: -32000,
                message: "Authentication required".to_string(),
                data: None,
            },
            MCPError::AuthorizationError(_) => MCPErrorResponse {
                code: -32001,
                message: "Insufficient permissions".to_string(),
                data: None,
            },
            MCPError::RateLimitExceeded => MCPErrorResponse {
                code: -32002,
                message: "Rate limit exceeded".to_string(),
                data: Some(json!({ "retry_after": 60 })),
            },
            MCPError::Timeout { timeout } => MCPErrorResponse {
                code: -32003,
                message: "Request timeout".to_string(),
                data: Some(json!({ "timeout_ms": timeout.as_millis() })),
            },
            MCPError::ToolExecutionError(err) => MCPErrorResponse {
                code: -32004,
                message: "Tool execution failed".to_string(),
                data: Some(json!({ 
                    "tool_error": err.to_string(),
                    "error_type": Self::classify_tool_error(err)
                })),
            },
            MCPError::ServiceUnavailable { reason } => MCPErrorResponse {
                code: -32005,
                message: "Service unavailable".to_string(),
                data: Some(json!({ "reason": reason })),
            },
            _ => MCPErrorResponse {
                code: -32603,
                message: "Internal error".to_string(),
                data: Some(json!({ "error": self.to_string() })),
            },
        }
    }
    
    fn classify_tool_error(error: &ToolExecutionError) -> &'static str {
        match error {
            ToolExecutionError::ToolNotFound(_) => "tool_not_found",
            ToolExecutionError::InvalidInput(_) => "invalid_input",
            ToolExecutionError::ExecutionFailed(_) => "execution_failed",
            ToolExecutionError::SecurityViolation(_) => "security_violation",
            ToolExecutionError::ResourceLimitExceeded(_) => "resource_limit",
            ToolExecutionError::ToolUnavailable(_) => "tool_unavailable",
        }
    }
}
```

### MCP-Specific Error Recovery

For comprehensive error recovery strategies including circuit breakers, retry policies, and fallback mechanisms, see the [centralized error handling documentation](../../architectural-concerns/error-handling.md).

MCP-specific recovery considerations:

```rust
// MCP-specific retry logic for tool execution
impl MCPToolExecutionRecovery {
    pub async fn handle_tool_error(
        &self,
        error: ToolExecutionError,
        context: &ErrorContext,
    ) -> Result<RecoveryResult, MCPError> {
        match error {
            ToolExecutionError::ToolNotFound(tool_name) => {
                // Attempt tool fallback
                self.try_fallback_tool(&tool_name, context).await
            },
            ToolExecutionError::SecurityViolation(_) => {
                // No retry for security violations
                Err(MCPError::ToolExecutionError(error))
            },
            ToolExecutionError::ResourceLimitExceeded(_) => {
                // Apply backoff and retry with reduced resources
                self.retry_with_reduced_resources(context).await
            },
            _ => {
                // Delegate to general retry strategy
                self.apply_general_retry(error, context).await
            }
        }
    }
}
```

## Testing Framework

### Error Simulation and Testing

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    
    #[tokio::test]
    async fn test_circuit_breaker_transitions() {
        let circuit_breaker = CircuitBreaker::new(CircuitBreakerConfig {
            failure_threshold: 0.5,
            success_threshold: 0.8,
            timeout: Duration::from_millis(100),
            min_request_count: 2,
            half_open_max_requests: 1,
        });
        
        // Simulate failures to open circuit
        for _ in 0..3 {
            let result = circuit_breaker.call(async { 
                Err::<(), String>("test error".to_string()) 
            }).await;
            assert!(result.is_err());
        }
        
        // Circuit should be open now
        let result = circuit_breaker.call(async { Ok::<(), String>(()) }).await;
        assert!(matches!(result, Err(CircuitBreakerError::CircuitOpen)));
        
        // Wait for timeout
        tokio::time::sleep(Duration::from_millis(150)).await;
        
        // Should allow one request in half-open state
        let result = circuit_breaker.call(async { Ok::<(), String>(()) }).await;
        assert!(result.is_ok());
    }
    
    #[tokio::test]
    async fn test_error_recovery_strategies() {
        let event_bus = Arc::new(EventBus::new());
        let framework = ErrorRecoveryFramework::new(event_bus);
        
        let error = MCPError::TransportError(TransportError::ConnectionLost);
        let context = ErrorContext {
            error_id: "test-error".to_string(),
            session_id: Some("test-session".to_string()),
            request_id: Some("test-request".to_string()),
            timestamp: chrono::Utc::now(),
            user_id: None,
            operation: Some("test_operation".to_string()),
            metadata: HashMap::new(),
            stack_trace: None,
        };
        
        let result = framework.handle_error_with_recovery(error, context).await;
        // Should attempt recovery for transient transport errors
        assert!(result.is_ok() || matches!(result, Err(MCPError::TransportError(_))));
    }
    
    #[tokio::test]
    async fn test_error_classification() {
        let error_manager = ErrorManager::new();
        
        let critical_error = MCPError::InternalError("Database connection failed".to_string());
        let severity = error_manager.classify_error_severity(&critical_error);
        assert_eq!(severity, ErrorSeverity::Critical);
        
        let auth_error = MCPError::AuthenticationError(AuthenticationError::InvalidToken);
        let severity = error_manager.classify_error_severity(&auth_error);
        assert_eq!(severity, ErrorSeverity::High);
        
        let param_error = MCPError::InvalidParams("Missing required field".to_string());
        let severity = error_manager.classify_error_severity(&param_error);
        assert_eq!(severity, ErrorSeverity::Low);
    }
}
```

## Integration Examples

### HTTP Transport Error Handling

```rust
impl HttpTransport {
    async fn handle_request_with_error_recovery(
        &self,
        request: Request<Body>,
    ) -> Result<Response<Body>, Infallible> {
        let error_context = ErrorContext {
            error_id: uuid::Uuid::new_v4().to_string(),
            session_id: self.extract_session_id(&request),
            request_id: self.extract_request_id(&request),
            timestamp: chrono::Utc::now(),
            user_id: self.extract_user_id(&request),
            operation: Some("http_request".to_string()),
            metadata: HashMap::new(),
            stack_trace: None,
        };
        
        match self.process_request(request).await {
            Ok(response) => Ok(response),
            Err(error) => {
                let mcp_error = MCPError::from(error);
                let error_response = self.error_manager
                    .handle_error(mcp_error, error_context)
                    .await;
                
                Ok(self.create_error_response(error_response))
            }
        }
    }
}
```

This MCP-specific error handling integrates with the [unified RUST-SS error handling framework](../../architectural-concerns/error-handling.md) to provide comprehensive error management, recovery, and resilience patterns essential for production-grade MCP implementations.