# Concepts & Coordination Overview

## RUST-SS Agent Guide & Documentation Framework

**Purpose**: This is a comprehensive documentation reference framework for building a Rust-based swarm system. This directory contains documentation specifications and architectural patterns - NOT the actual system code. Agents use this documentation to understand requirements and build the real RUST-SS system.

## Quick Start: Basic-Memory MCP Commands

### Essential Commands for Agent Coordination
```bash
# Search for project context using search_notes
# search_notes with query: "RUST-SS project coordination"

# Store findings and progress using write_note
# write_note with:
#   title: "Agent Task Progress"
#   content: "## Findings\n...\n## Next Steps\n..."
#   folder: "coordination"
#   tags: ["agent-work", "progress"]

# Build context from previous work using build_context
# build_context with url: "memory://coordination/agent-task-progress"

# Read specific documentation using read_note
# read_note with identifier: "[note-title-or-permalink]"
```

### Current Project Context
- **Project**: `mister-smith` (default basic-memory project)
- **Coordination Database**: Contains 25+ agent coordination records
- **Status**: Documentation framework complete, implementation phase ready

## Core System Concepts

### Fundamental Concepts
1. **Memory Sharing**: Distributed state synchronization across agents
2. **Agent Spawning**: Dynamic creation and lifecycle management
3. **State Persistence**: Durable storage of system state
4. **Session Management**: Interactive and long-running operations
5. **Multi-Tenancy**: Resource isolation and fair sharing
6. **Batch Operations**: Efficient bulk processing

### System Philosophy
- **Distributed by Design**: No single points of failure
- **Stateful Operations**: Full context preservation
- **Performance Critical**: Sub-millisecond coordination
- **Enterprise Ready**: Multi-tenant from day one

## Agent Architecture & Coordination

### Agent Architecture
- **Process Isolation**: Each agent runs in its own process
- **Resource Limits**: CPU, memory, and I/O quotas
- **Health Monitoring**: Continuous liveness checks
- **Graceful Shutdown**: Clean termination handling

### State Management
- **Event Sourcing**: All state changes as events
- **Snapshot + Delta**: Efficient state reconstruction
- **Distributed Consensus**: Consistent state across nodes
- **Conflict Resolution**: Automated merge strategies

### Communication Model
- **Asynchronous Messaging**: Default communication pattern
- **Request-Reply**: When synchronous needed
- **Event Streaming**: For real-time updates
- **Batch Processing**: For efficiency

## SPARC Modes Configuration

```json
{
  "sparc_modes": {
    "orchestrator": {
      "purpose": "Multi-agent coordination and task distribution",
      "capabilities": ["task_delegation", "agent_management", "workflow_coordination"],
      "use_cases": ["complex_projects", "multi_phase_development"],
      "coordination_pattern": "centralized"
    },
    "researcher": {
      "purpose": "Information gathering and analysis",
      "capabilities": ["data_collection", "trend_analysis", "documentation_review"],
      "use_cases": ["requirement_analysis", "technology_research"],
      "coordination_pattern": "distributed"
    },
    "coder": {
      "purpose": "Implementation and development",
      "capabilities": ["code_generation", "refactoring", "optimization"],
      "use_cases": ["feature_development", "bug_fixes"],
      "coordination_pattern": "hierarchical"
    },
    "architect": {
      "purpose": "System design and architectural decisions",
      "capabilities": ["system_design", "pattern_selection", "scalability_planning"],
      "use_cases": ["system_architecture", "design_patterns"],
      "coordination_pattern": "centralized"
    },
    "tester": {
      "purpose": "Quality assurance and testing",
      "capabilities": ["test_design", "validation", "performance_testing"],
      "use_cases": ["quality_validation", "regression_testing"],
      "coordination_pattern": "distributed"
    }
  }
}
```

## Coordination Patterns

### Agent Coordination Patterns

#### 1. Centralized Coordination
- Single coordinator manages all agents
- Clear command structure and decision making
- Best for: Complex workflows requiring tight coordination
- Trade-offs: Single point of failure, coordinator bottleneck

#### 2. Distributed Coordination
- Agents coordinate through peer-to-peer communication
- Consensus mechanisms for decision making
- Best for: Resilient operations, high-throughput scenarios
- Trade-offs: Complex consensus, potential conflicts

#### 3. Hierarchical Coordination
- Tree-like structure with multiple coordination levels
- Clear authority chain and responsibility delegation
- Best for: Large-scale operations with clear structure
- Trade-offs: Coordination overhead, potential bottlenecks

#### 4. Mesh Coordination
- Fully connected network of agent communications
- Dynamic coordination based on task requirements
- Best for: Adaptive workflows, dynamic task allocation
- Trade-offs: Communication overhead, complexity

#### 5. Hybrid Coordination
- Combination of patterns based on context
- Adaptive coordination mode selection
- Best for: Complex systems with varying requirements
- Trade-offs: Implementation complexity, decision overhead

## Integration Patterns

### Memory-Driven Coordination
Use Memory to coordinate information across multiple SPARC modes and swarm operations:

```bash
# Store architecture decisions
./claude-flow memory store "system_architecture" "Microservices with API Gateway pattern"

# All subsequent operations can reference this decision
./claude-flow sparc run coder "Implement user service based on system_architecture in memory"
./claude-flow sparc run tester "Create integration tests for microservices architecture"
```

### Multi-Stage Development
Coordinate complex development through staged execution:

```bash
# Stage 1: Research and planning
./claude-flow sparc run researcher "Research authentication best practices"
./claude-flow sparc run architect "Design authentication system architecture"

# Stage 2: Implementation
./claude-flow sparc tdd "User registration and login functionality"
./claude-flow sparc run coder "Implement JWT token management"

# Stage 3: Testing and deployment
./claude-flow sparc run tester "Comprehensive security testing"
./claude-flow swarm "Deploy authentication system" --strategy maintenance --mode centralized
```

## Agent Coordination Workflows

### Starting a New Implementation Task

1. **Gather Context**
```bash
# Use search_notes to find current implementation status
# Use build_context to gather relevant work context from memory
```

2. **Review Requirements**
- Navigate to relevant `/features/` or `/services/` documentation
- Read CLAUDE.md files for specific components
- Check existing implementation patterns

3. **Document Approach**
```bash
# Use write_note to document your implementation plan
# write_note with:
#   title: "Implementation Plan - [Component]"
#   folder: "plans"
#   content: "## Approach\n...\n## Dependencies\n...\n## Timeline\n..."
```

4. **Execute and Update**
- Implement according to documentation specifications
- Store progress updates in basic-memory
- Share findings and patterns with team

### Coordinating with Other Agents

1. **Check Team Status**
```bash
# Use search_notes to find agent progress and current phase
```

2. **Share Work Context**
```bash
# Use write_note to share coordination needs
# write_note with:
#   title: "Coordination - [Your-Work] needs [Other-Work]"
#   folder: "coordination"
#   tags: ["dependency", "coordination", "urgent"]
```

3. **Monitor Dependencies**
```bash
# Use search_notes to monitor coordination dependencies
```

## MCP Command Patterns

```json
{
  "common_patterns": {
    "start_work_session": [
      "search_notes - query: current project status",
      "build_context - url: memory://[relevant-project-context]"
    ],
    "store_progress": [
      "write_note - title: [Agent-ID] Progress Update, folder: progress, content: ## Completed\n...\n## Next Steps\n...",
      "edit_note - identifier: project-status, operation: append, content: - [timestamp] Agent progress update"
    ],
    "coordinate_with_team": [
      "search_notes - query: agent coordination current phase",
      "write_note - title: Coordination Request, folder: coordination, tags: [request, collaboration]"
    ]
  }
}
```

## Directory Navigation Index

```json
{
  "directory_index": {
    "concepts/": {
      "purpose": "Core system concepts and patterns",
      "key_files": ["agent-spawning/", "memory-sharing/", "session-management/"],
      "priority": "high",
      "agent_relevance": "foundational_understanding"
    },
    "features/sparc-modes/": {
      "purpose": "Detailed SPARC mode implementations",
      "key_files": ["orchestrator/", "coder/", "researcher/", "architect/"],
      "priority": "high",
      "agent_relevance": "mode_specific_guidance"
    },
    "architecture/": {
      "purpose": "System architecture and design patterns",
      "key_files": ["system-design/", "scalability/", "security/"],
      "priority": "medium",
      "agent_relevance": "implementation_guidance"
    },
    "coordination-modes/": {
      "purpose": "Agent coordination strategies",
      "key_files": ["centralized/", "distributed/", "hierarchical/", "mesh/"],
      "priority": "high",
      "agent_relevance": "coordination_patterns"
    },
    "services/": {
      "purpose": "Service architecture and implementation",
      "key_files": ["agent-management/", "coordination/", "memory/"],
      "priority": "medium",
      "agent_relevance": "service_implementation"
    }
  }
}
```

## Agent Coordination Protocols

### Agent Identification
- **Format**: `[SPARC-Mode]-[Specialization]-[Session-ID]`
- **Example**: `coder-rust-backend-12345`

### Status Updates
- **Frequency**: At task completion or major milestones
- **Location**: `progress/` folder in basic-memory
- **Format**: Structured markdown with completed/in-progress/blocked sections

### Decision Records
- **Location**: `decisions/` folder
- **Format**: ADR (Architecture Decision Record) template
- **Sharing**: Tag with relevant team members and components

## Framework Navigation Tips

1. **Start with Overview**: Read main CLAUDE.md files in each major directory
2. **Follow Dependencies**: Use the Relations sections in basic-memory notes
3. **Search Effectively**: Use specific terms like "coordination", "implementation", "pattern"
4. **Build Context**: Use build_context tool for comprehensive understanding of related work
5. **Stay Updated**: Regular search for "progress" and "status" updates from team

## System Constraints & Requirements

### System Constraints
- **Agent Limits**: 100+ concurrent per instance
- **Memory Limits**: Configurable per tenant
- **Network Bandwidth**: Traffic shaping enabled
- **Storage Quotas**: Per-namespace limits

### Performance Targets
- **Agent Spawn Time**: <500ms
- **State Sync Latency**: <10ms
- **Memory Operation**: <1ms
- **Session Recovery**: <5s

### Reliability Goals
- **99.9% Uptime**: Service availability
- **Zero Data Loss**: For committed state
- **Automatic Recovery**: Self-healing
- **Graceful Degradation**: Partial failures

---

**Remember**: This is a documentation framework for building the actual RUST-SS system. Your role is to implement the specifications and patterns documented here, coordinating with other agents through the basic-memory system for optimal team collaboration.