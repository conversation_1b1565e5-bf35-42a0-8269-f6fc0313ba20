# Subcommand Management and Routing

## Overview

This document details subcommand organization patterns from claude-code-flow, showing how nested commands are structured, registered, and routed to appropriate handlers.

## Subcommand Architecture Patterns

### Fluent Command Builder Pattern

The system uses method chaining for subcommand definition:

```typescript
export const sessionCommand = new Command()
  .description('Manage Claude-Flow sessions')
  .action(() => {
    sessionCommand.showHelp();  // Default action shows help
  })
  .command('list', Command)
    .description('List all saved sessions')
    .option('-a, --active', 'Show only active sessions')
    .option('--format <format:string>', 'Output format (table, json)', { default: 'table' })
    .action(async (options: any) => {
      await listSessions(options);
    })
  .command('save', Command)
    .description('Save current session state')
    .arguments('[name:string]')
    .option('-d, --description <desc:string>', 'Session description')
    .option('-t, --tags <tags:string>', 'Comma-separated tags')
    .option('--auto', 'Auto-generate session name')
    .action(async (options: any, name: string | undefined) => {
      await saveSession(name, options);
    });
```

### Manual Routing Pattern

For complex command structures, manual routing provides more control:

```typescript
export async function sparcAction(ctx: CommandContext): Promise<void> {
  const subcommand = ctx.args[0];

  // Default behavior when no subcommand is provided
  if (!subcommand || (subcommand && !['modes', 'info', 'run', 'tdd', 'workflow'].includes(subcommand))) {
    await runFullSparcDevelopment(ctx);
    return;
  }

  // Explicit routing to subcommand handlers
  switch (subcommand) {
    case "modes":
      await listSparcModes(ctx);
      break;
    case "info":
      await showModeInfo(ctx);
      break;
    case "run":
      await runSparcMode(ctx);
      break;
    case "tdd":
      await runTddFlow(ctx);
      break;
    case "workflow":
      await runSparcWorkflow(ctx);
      break;
    default:
      await showSparcHelp();
      break;
  }
}
```

## Command Registration Patterns

### Nested Command Structure

Commands support arbitrary nesting through the subcommands property:

```typescript
interface CommandHandler {
  action: (ctx: CommandContext) => Promise<void>;
  description: string;
  options?: CommandOption[];
  subcommands?: Record<string, CommandHandler>;  // Recursive nesting
  examples?: string[];
  prerequisites?: string[];
}
```

### Registry-Based Subcommand Registration

The UnifiedCommandRegistry supports nested command registration:

```typescript
const agentCommands: Record<string, CommandHandler> = {
  spawn: {
    action: async (ctx: CommandContext) => await spawnAgent(ctx),
    description: 'Create a new agent instance',
    options: [
      { flag: '--type', description: 'Agent type (researcher, coder, analyst)', hasValue: true },
      { flag: '--name', description: 'Custom agent name', hasValue: true }
    ],
    examples: [
      'claude-flow agent spawn --type researcher',
      'claude-flow agent spawn --type coder --name backend-dev'
    ]
  },
  list: {
    action: async (ctx: CommandContext) => await listAgents(ctx),
    description: 'List all active agents',
    options: [
      { flag: '--format', description: 'Output format (table, json)', hasValue: true, defaultValue: 'table' }
    ]
  },
  terminate: {
    action: async (ctx: CommandContext) => await terminateAgent(ctx),
    description: 'Terminate a specific agent',
    options: [
      { flag: '--force', description: 'Force termination without confirmation' }
    ]
  }
};

// Register with nested structure
registry.register('agent', {
  action: async (ctx: CommandContext) => {
    if (ctx.args.length === 0) {
      await showAgentHelp();
      return;
    }
    
    const subcommand = ctx.args[0];
    const handler = agentCommands[subcommand];
    
    if (handler) {
      // Create new context for subcommand
      const subCtx = {
        ...ctx,
        args: ctx.args.slice(1)  // Remove subcommand from args
      };
      await handler.action(subCtx);
    } else {
      throw new CLIError(`Unknown subcommand: agent ${subcommand}`, 'UNKNOWN_SUBCOMMAND', 1, true);
    }
  },
  description: 'Manage agents (spawn, list, terminate, info)',
  subcommands: agentCommands
});
```

## Argument Flow in Subcommands

### Context Propagation Pattern

Subcommands receive modified contexts with adjusted argument arrays:

```typescript
async function runSparcMode(ctx: CommandContext): Promise<void> {
  // ctx.args[0] is the subcommand name ('run')
  const modeSlug = ctx.args[1];      // First actual argument
  const taskDescription = ctx.args.slice(2).join(" ");  // Remaining arguments

  if (!modeSlug || !taskDescription) {
    error("Usage: sparc run <mode-slug> <task-description>");
    return;
  }

  // Process subcommand-specific flags
  const options = {
    dryRun: ctx.flags.dryRun || ctx.flags["dry-run"],
    namespace: ctx.flags.namespace,
    verbose: ctx.flags.verbose,
    noPermissions: ctx.flags.noPermissions || ctx.flags["no-permissions"],
    config: ctx.flags.config
  };
}
```

### Subcommand with Complex Arguments

Session restore command demonstrates multiple argument processing:

```typescript
.command('restore', Command)
  .description('Restore a saved session')
  .arguments('<session-id:string>')
  .option('-f, --force', 'Force restore without confirmation')
  .option('--merge', 'Merge with current session instead of replacing')
  .action(async (options: any, sessionId: string) => {
    await restoreSession(sessionId, options);
  })

async function restoreSession(sessionId: string, options: any): Promise<void> {
  try {
    const session = await loadSession(sessionId);
    
    if (!session) {
      console.error(colors.red(`Session '${sessionId}' not found`));
      return;
    }

    // Show session info before restoration
    console.log(colors.cyan.bold('Session to restore:'));
    console.log(`${colors.white('Name:')} ${session.name}`);
    console.log(`${colors.white('Agents:')} ${session.state.agents.length}`);
    console.log(`${colors.white('Tasks:')} ${session.state.tasks.length}`);

    // Handle confirmation unless --force is used
    if (!options.force) {
      const action = options.merge ? 'merge with current session' : 'replace current session';
      const confirmed = await Confirm.prompt({
        message: `Are you sure you want to ${action}?`,
        default: false,
      });

      if (!confirmed) {
        console.log(colors.gray('Restore cancelled'));
        return;
      }
    }

    // Perform restoration based on --merge flag
    if (options.merge) {
      await mergeSession(session);
    } else {
      await replaceSession(session);
    }
  } catch (error) {
    console.error(colors.red('Failed to restore session:'), (error as Error).message);
  }
}
```

## Help Generation for Subcommands

### Hierarchical Help System

The registry generates help text that reflects the command structure:

```typescript
getHelp(commandName?: string): string {
  if (commandName) {
    return this.getCommandHelp(commandName);
  }
  
  return this.getGlobalHelp();
}

private getCommandHelp(commandName: string): string {
  const handler = this.commands.get(commandName);
  
  if (!handler) {
    return `Unknown command: ${commandName}`;
  }

  let help = `${chalk.cyan(commandName)} - ${handler.description}\n\n`;

  // Add usage section
  help += `${chalk.yellow('USAGE:')}\n`;
  help += `  claude-flow ${commandName}`;
  
  if (handler.options && handler.options.length > 0) {
    help += ' [options]';
  }
  
  if (handler.subcommands && Object.keys(handler.subcommands).length > 0) {
    help += ' <subcommand>';
  }
  
  help += '\n\n';

  // Add subcommands section
  if (handler.subcommands && Object.keys(handler.subcommands).length > 0) {
    help += `${chalk.yellow('SUBCOMMANDS:')}\n`;
    for (const [name, subHandler] of Object.entries(handler.subcommands)) {
      help += `  ${chalk.green(name.padEnd(20))} ${subHandler.description}\n`;
    }
    help += '\n';
  }

  // Add options section
  if (handler.options && handler.options.length > 0) {
    help += `${chalk.yellow('OPTIONS:')}\n`;
    for (const option of handler.options) {
      const aliases = option.aliases ? option.aliases.join(', ') : '';
      const flags = aliases ? `${option.flag}, ${aliases}` : option.flag;
      help += `  ${chalk.green(flags.padEnd(20))} ${option.description}\n`;
    }
    help += '\n';
  }

  return help;
}
```

### Context-Sensitive Help

Commands can provide different help based on context:

```typescript
async function showSparcHelp(): Promise<void> {
  console.log(`${cyan("SPARC")} - ${green("Specification, Pseudocode, Architecture, Refinement, Completion")}`);
  console.log();
  console.log("SPARC development methodology with TDD and multi-agent coordination.");
  console.log();
  console.log(blue("Commands:"));
  console.log("  modes                    List all available SPARC modes");
  console.log("  info <mode>              Show detailed information about a mode");
  console.log("  run <mode> <task>        Execute a task using a specific SPARC mode");
  console.log("  tdd <task>               Run full TDD workflow using SPARC methodology");
  console.log("  workflow <file>          Execute a custom SPARC workflow from JSON file");
  console.log();
  console.log(blue("Common Modes:"));
  console.log("  spec-pseudocode          Create specifications and pseudocode");
  console.log("  architect                Design system architecture");
  console.log("  code                     Implement code solutions");
  console.log("  tdd                      Test-driven development");
  console.log("  debug                    Debug and troubleshoot issues");
  console.log();
  console.log(blue("Examples:"));
  console.log(`  ${yellow("claude-flow sparc modes")}                              # List all modes`);
  console.log(`  ${yellow("claude-flow sparc run code")} "implement user auth"      # Run specific mode`);
  console.log(`  ${yellow("claude-flow sparc tdd")} "payment processing system"    # Full TDD workflow`);
  console.log();
}
```

## Workflow and Pipeline Subcommands

### Sequential Execution Pattern

The TDD workflow demonstrates sequential subcommand execution:

```typescript
async function runTddFlow(ctx: CommandContext): Promise<void> {
  const taskDescription = ctx.args.slice(1).join(" ");

  if (!taskDescription) {
    error("Usage: sparc tdd <task-description>");
    return;
  }

  const workflow = [
    { mode: "spec-pseudocode", phase: "Specification", description: `Create detailed spec and pseudocode for: ${taskDescription}` },
    { mode: "tdd", phase: "Red", description: `Write failing tests for: ${taskDescription}` },
    { mode: "code", phase: "Green", description: `Implement minimal code to pass tests for: ${taskDescription}` },
    { mode: "refinement-optimization-mode", phase: "Refactor", description: `Refactor and optimize implementation for: ${taskDescription}` },
    { mode: "integration", phase: "Integration", description: `Integrate and verify complete solution for: ${taskDescription}` }
  ];

  success("Starting SPARC TDD Workflow");
  console.log("Following Test-Driven Development with SPARC methodology");
  console.log();

  for (let i = 0; i < workflow.length; i++) {
    const step = workflow[i];
    const mode = config.customModes.find(m => m.slug === step.mode);

    if (!mode) {
      warning(`Mode not found: ${step.mode}, skipping step`);
      continue;
    }

    info(`Phase ${i + 1}/5: ${step.phase} (${mode.name})`);
    console.log(`📋 ${step.description}`);
    console.log();

    // Execute the subcommand for this phase
    await executePhaseSubcommand(mode, step, ctx.flags, i + 1, workflow.length);

    // Interactive progression control
    if (ctx.flags.sequential !== false) {
      console.log("Phase completed. Press Enter to continue to next phase, or Ctrl+C to stop...");
      await waitForUserInput();
    }
  }

  success("SPARC TDD Workflow completed!");
}
```

### Dynamic Subcommand Loading

The system supports loading subcommands from configuration:

```typescript
async function runSparcWorkflow(ctx: CommandContext): Promise<void> {
  const workflowFile = ctx.args[1];

  if (!workflowFile) {
    error("Usage: sparc workflow <workflow-file.json>");
    return;
  }

  try {
    const { readFile } = await import("fs/promises");
    const workflowContent = await readFile(workflowFile, "utf-8");
    const workflow = JSON.parse(workflowContent);

    if (!workflow.steps || !Array.isArray(workflow.steps)) {
      error("Invalid workflow file: missing 'steps' array");
      return;
    }

    for (let i = 0; i < workflow.steps.length; i++) {
      const step = workflow.steps[i];
      
      // Dynamically create subcommand context
      const stepCtx = {
        ...ctx,
        args: [step.mode, ...(step.args || [])],
        flags: {
          ...ctx.flags,
          workflowStep: i + 1,
          totalSteps: workflow.steps.length,
          workflowName: workflow.name
        }
      };

      // Execute as if it were a regular subcommand
      await executeWorkflowStep(stepCtx, step);
    }
  } catch (err) {
    error(`Failed to run workflow: ${err instanceof Error ? err.message : String(err)}`);
  }
}
```

## Error Handling in Subcommands

### Subcommand-Specific Errors

```typescript
// Standard subcommand not found error
throw new CLIError(
  `Unknown subcommand: ${parentCommand} ${subcommand}`,
  'UNKNOWN_SUBCOMMAND',
  1,
  true  // Show usage
);

// Missing required subcommand arguments
if (!modeSlug || !taskDescription) {
  throw new CLIError(
    "Usage: sparc run <mode-slug> <task-description>",
    'MISSING_ARGUMENTS',
    1,
    true
  );
}

// Subcommand execution failure
catch (error) {
  const errorMessage = error instanceof Error ? error.message : String(error);
  throw new CLIError(
    `Subcommand '${subcommand}' failed: ${errorMessage}`,
    'SUBCOMMAND_EXECUTION_ERROR',
    1
  );
}
```

## Implementation Guidelines

### Subcommand Design Principles

1. **Consistent Structure**: All subcommands should follow the same argument and option patterns
2. **Clear Scope**: Each subcommand should have a well-defined, single responsibility
3. **Composable**: Subcommands should work well together in workflows
4. **Discoverable**: Help text should make subcommand relationships clear
5. **Context Preservation**: Parent command context should flow to subcommands appropriately

### Best Practices

- Use the fluent builder pattern for simple subcommand structures
- Use manual routing for complex logic and dynamic subcommands
- Always provide meaningful help text for each subcommand level
- Validate subcommand arguments early and provide clear error messages
- Support both interactive and non-interactive execution modes
- Consider command composition and workflow patterns for related operations

This subcommand management system provides a flexible foundation for building complex CLI applications with clear command hierarchies and proper routing.