# Command Structure and Hierarchy

## Overview

This document details the command structure patterns from claude-code-flow, showing how commands are organized hierarchically and registered in the system.

## Rust Implementation with clap

### Basic Command Structure

```rust
use clap::{Parser, Subcommand};

#[derive(Parser)]
#[clap(
    name = "claude-flow",
    about = "A comprehensive orchestration system",
    version = env!("CARGO_PKG_VERSION"),
    author = env!("CARGO_PKG_AUTHORS")
)]
pub struct Cli {
    /// Enable verbose output
    #[clap(short, long, global = true)]
    pub verbose: bool,

    /// Working directory
    #[clap(short = 'd', long, global = true)]
    pub working_dir: Option<String>,

    /// Configuration file path
    #[clap(short, long, global = true)]
    pub config: Option<String>,

    #[clap(subcommand)]
    pub command: Commands,
}

#[derive(Subcommand)]
pub enum Commands {
    /// Initialize Claude Code integration files
    Init {
        /// Initialize with SPARC development environment
        #[clap(long)]
        sparc: bool,
        
        /// Skip interactive prompts
        #[clap(long)]
        yes: bool,
    },
    
    /// Start the orchestration system
    Start {
        /// Run as daemon in background
        #[clap(short = 'd', long)]
        daemon: bool,
        
        /// MCP server port
        #[clap(short, long, default_value = "3000")]
        port: u16,
        
        /// Launch interactive UI
        #[clap(short, long)]
        ui: bool,
    },
    
    /// Manage agents
    Agent(AgentCommand),
    
    /// Manage tasks
    Task(TaskCommand),
    
    /// Manage memory
    Memory(MemoryCommand),
    
    /// SPARC development methodology commands
    Sparc(SparcCommand),
}
```

### Nested Subcommand Structure

```rust
#[derive(Parser)]
pub struct AgentCommand {
    #[clap(subcommand)]
    pub action: AgentActions,
}

#[derive(Subcommand)]
pub enum AgentActions {
    /// Spawn a new agent
    Spawn {
        /// Agent type to spawn
        agent_type: String,
        
        /// Custom agent name
        #[clap(short, long)]
        name: Option<String>,
        
        /// Agent capabilities
        #[clap(short, long, value_delimiter = ',')]
        capabilities: Vec<String>,
    },
    
    /// List all active agents
    List {
        /// Show only active agents
        #[clap(short, long)]
        active: bool,
        
        /// Output format
        #[clap(long, value_parser = ["table", "json", "yaml"], default_value = "table")]
        format: String,
    },
    
    /// Terminate an agent
    Terminate {
        /// Agent ID or name
        agent_id: String,
        
        /// Force termination without confirmation
        #[clap(short, long)]
        force: bool,
    },
    
    /// Show agent information
    Info {
        /// Agent ID or name
        agent_id: String,
        
        /// Include performance metrics
        #[clap(long)]
        metrics: bool,
    },
}
```

## Core Command Registry Implementation

### Rust Command Registry Pattern

```rust
use std::collections::HashMap;
use std::sync::Arc;
use async_trait::async_trait;
use anyhow::{Result, anyhow};

// Command handler trait
#[async_trait]
pub trait CommandHandler: Send + Sync {
    async fn execute(&self, ctx: CommandContext) -> Result<()>;
    fn description(&self) -> &str;
    fn prerequisites(&self) -> Vec<Prerequisite> {
        vec![]
    }
}

// Command context for passing state
#[derive(Clone)]
pub struct CommandContext {
    pub args: Vec<String>,
    pub flags: HashMap<String, serde_json::Value>,
    pub working_dir: String,
    pub config: Option<Config>,
    pub runtime: Arc<RuntimeAdapter>,
}

// Command registry
pub struct CommandRegistry {
    commands: HashMap<String, Arc<dyn CommandHandler>>,
    logger: Arc<dyn Logger>,
}

impl CommandRegistry {
    pub fn new(logger: Arc<dyn Logger>) -> Self {
        Self {
            commands: HashMap::new(),
            logger,
        }
    }

    pub fn register(&mut self, name: &str, handler: Arc<dyn CommandHandler>) {
        if self.commands.contains_key(name) {
            self.logger.warn(&format!("Command '{}' is being overridden", name));
        }
        self.commands.insert(name.to_string(), handler);
        self.logger.debug(&format!("Registered command: {}", name));
    }

    pub fn register_multiple(&mut self, commands: Vec<(&str, Arc<dyn CommandHandler>)>) {
        for (name, handler) in commands {
            self.register(name, handler);
        }
    }

    pub async fn execute(&self, command_name: &str, ctx: CommandContext) -> Result<()> {
        let handler = self.commands
            .get(command_name)
            .ok_or_else(|| anyhow!("Unknown command: {}", command_name))?;

        // Check prerequisites
        for prereq in handler.prerequisites() {
            prereq.check(&ctx).await?;
        }

        handler.execute(ctx).await
    }
}

// Prerequisite checking
#[derive(Clone)]
pub enum Prerequisite {
    File(String),
    EnvVar(String),
    Command(String),
}

impl Prerequisite {
    pub async fn check(&self, ctx: &CommandContext) -> Result<()> {
        match self {
            Prerequisite::File(path) => {
                if !ctx.runtime.exists(path).await? {
                    return Err(anyhow!("Required file not found: {}", path));
                }
            }
            Prerequisite::EnvVar(var) => {
                if ctx.runtime.get_env_var(var).is_none() {
                    return Err(anyhow!("Required environment variable not set: {}", var));
                }
            }
            Prerequisite::Command(cmd) => {
                let result = ctx.runtime.spawn("which", vec![cmd.clone()], None).await?;
                if !result.success {
                    return Err(anyhow!("Required command not found: {}", cmd));
                }
            }
        }
        Ok(())
    }
}
```

### UnifiedCommandRegistry Class

The central registry maintains a Map-based command system:

```typescript
export class UnifiedCommandRegistry {
  private commands: Map<string, CommandHandler> = new Map();
  private logger: Logger;
  private runtime: NodeRuntimeAdapter;

  constructor(logger?: Logger) {
    this.runtime = new NodeRuntimeAdapter();
    this.logger = logger || new DefaultLogger();
  }

  register(name: string, handler: CommandHandler): void {
    if (this.commands.has(name)) {
      this.logger.warn(`Command '${name}' is being overridden`);
    }
    this.commands.set(name, handler);
    this.logger.debug(`Registered command: ${name}`);
  }

  registerMultiple(commands: Record<string, CommandHandler>): void {
    for (const [name, handler] of Object.entries(commands)) {
      this.register(name, handler);
    }
  }
}
```

### Command Handler Interface Structure

All commands implement a standardized interface:

```typescript
interface CommandHandler {
  action: (ctx: CommandContext) => Promise<void>;
  description: string;
  options?: CommandOption[];
  subcommands?: Record<string, CommandHandler>;
  examples?: string[];
  prerequisites?: string[];
}

interface CommandOption {
  flag: string;
  description: string;
  hasValue?: boolean;
  defaultValue?: any;
  aliases?: string[];
}
```

## Command Hierarchy Patterns

### Root Level Commands

The system defines these primary command categories:

```typescript
const ROOT_COMMANDS = {
  'init': 'Initialize Claude Code integration files',
  'start': 'Start the orchestration system', 
  'agent': 'Manage agents (spawn, list, terminate, info)',
  'task': 'Manage tasks (create, list, status, cancel, workflow)',
  'memory': 'Manage memory (query, export, import, stats, cleanup)',
  'mcp': 'Manage MCP server (status, tools, start, stop)',
  'config': 'Manage configuration (show, get, set, init, validate)',
  'status': 'Show system status',
  'monitor': 'Monitor system in real-time',
  'session': 'Manage terminal sessions',
  'workflow': 'Execute workflow files',
  'sparc': 'SPARC development methodology commands',
  'claude': 'Spawn Claude instances with specific configurations'
};
```

### Subcommand Structure Example

The `session` command demonstrates nested subcommand organization:

```typescript
export const sessionCommand = new Command()
  .description('Manage Claude-Flow sessions')
  .action(() => {
    sessionCommand.showHelp();
  })
  .command('list', Command)
    .description('List all saved sessions')
    .option('-a, --active', 'Show only active sessions')
    .option('--format <format:string>', 'Output format (table, json)', { default: 'table' })
    .action(async (options: any) => {
      await listSessions(options);
    })
  .command('save', Command)
    .description('Save current session state')
    .arguments('[name:string]')
    .option('-d, --description <desc:string>', 'Session description')
    .option('-t, --tags <tags:string>', 'Comma-separated tags')
    .option('--auto', 'Auto-generate session name')
    .action(async (options: any, name: string | undefined) => {
      await saveSession(name, options);
    })
  .command('restore', Command)
    .description('Restore a saved session')
    .arguments('<session-id:string>')
    .option('-f, --force', 'Force restore without confirmation')
    .option('--merge', 'Merge with current session instead of replacing')
    .action(async (options: any, sessionId: string) => {
      await restoreSession(sessionId, options);
    });
```

### Complex Command Structure - SPARC System (Rust)

```rust
#[derive(Parser)]
pub struct SparcCommand {
    /// Task description (when running without subcommand)
    #[clap(value_name = "TASK", index = 1)]
    pub task: Option<String>,

    #[clap(subcommand)]
    pub subcommand: Option<SparcSubcommands>,
}

#[derive(Subcommand)]
pub enum SparcSubcommands {
    /// List all available SPARC modes
    Modes,
    
    /// Show information about a specific mode
    Info {
        /// Mode slug to get info about
        mode_slug: String,
    },
    
    /// Run a specific SPARC mode
    Run {
        /// Mode to run
        mode_slug: String,
        
        /// Task description
        #[clap(required = true)]
        task: Vec<String>,
        
        /// Dry run mode
        #[clap(long)]
        dry_run: bool,
        
        /// Custom namespace
        #[clap(long)]
        namespace: Option<String>,
    },
    
    /// Test-driven development flow
    Tdd {
        /// Feature to develop
        feature: Vec<String>,
        
        /// Skip initial tests
        #[clap(long)]
        skip_tests: bool,
        
        /// Test framework
        #[clap(long, default_value = "jest")]
        framework: String,
    },
    
    /// Execute workflow file
    Workflow {
        /// Workflow file path
        file: String,
        
        /// Workflow variables
        #[clap(long, value_delimiter = ',')]
        vars: Vec<String>,
    },
}

// Implementation
pub struct SparcCommandHandler;

#[async_trait]
impl CommandHandler for SparcCommandHandler {
    async fn execute(&self, ctx: CommandContext) -> Result<()> {
        let cli = ctx.get_parsed::<SparcCommand>()?;
        
        match &cli.subcommand {
            None => {
                // Run full SPARC development if task is provided
                if let Some(task) = &cli.task {
                    self.run_full_sparc_development(task, &ctx).await
                } else {
                    self.show_help()
                }
            }
            Some(subcommand) => match subcommand {
                SparcSubcommands::Modes => self.list_sparc_modes(&ctx).await,
                SparcSubcommands::Info { mode_slug } => {
                    self.show_mode_info(mode_slug, &ctx).await
                }
                SparcSubcommands::Run { mode_slug, task, dry_run, namespace } => {
                    let task_desc = task.join(" ");
                    self.run_sparc_mode(mode_slug, &task_desc, &ctx).await
                }
                SparcSubcommands::Tdd { feature, skip_tests, framework } => {
                    let feature_desc = feature.join(" ");
                    self.run_tdd_flow(&feature_desc, skip_tests, framework, &ctx).await
                }
                SparcSubcommands::Workflow { file, vars } => {
                    self.run_sparc_workflow(file, vars, &ctx).await
                }
            }
        }
    }

    fn description(&self) -> &str {
        "SPARC development methodology commands"
    }
}
```

### Complex Command Structure - SPARC System

The SPARC command shows advanced subcommand routing:

```typescript
export async function sparcAction(ctx: CommandContext): Promise<void> {
  const subcommand = ctx.args[0];

  // If no subcommand, run full SPARC development workflow
  if (!subcommand || (subcommand && !['modes', 'info', 'run', 'tdd', 'workflow'].includes(subcommand))) {
    await runFullSparcDevelopment(ctx);
    return;
  }

  switch (subcommand) {
    case "modes":
      await listSparcModes(ctx);
      break;
    case "info":
      await showModeInfo(ctx);
      break;
    case "run":
      await runSparcMode(ctx);
      break;
    case "tdd":
      await runTddFlow(ctx);
      break;
    case "workflow":
      await runSparcWorkflow(ctx);
      break;
    default:
      await showSparcHelp();
      break;
  }
}
```

## Command Context Pattern

### Context Creation and Propagation

Commands receive a standardized context object:

```typescript
const context: CommandContext = {
  args,
  flags,
  workingDir: this.runtime.getCurrentDir(),
  config: null, // Will be set when config system is unified
  runtime: this.runtime
};
```

### Runtime Adapter Interface

The runtime adapter provides environment abstraction:

```typescript
interface RuntimeAdapter {
  readFile(path: string): Promise<string>;
  writeFile(path: string, content: string): Promise<void>;
  exists(path: string): Promise<boolean>;
  getCurrentDir(): string;
  getEnvVar(name: string): string | undefined;
  spawn(command: string, args: string[], options?: SpawnOptions): Promise<ProcessResult>;
  resolvePath(...segments: string[]): string;
  getStats(path: string): Promise<FileStats>;
}
```

## Command Registration Patterns

### Single Command Registration

```typescript
registry.register('status', {
  action: async (ctx: CommandContext) => {
    await showSystemStatus(ctx);
  },
  description: 'Show comprehensive system status',
  options: [
    {
      flag: '--json',
      description: 'Output in JSON format',
      hasValue: false
    }
  ],
  examples: [
    'claude-flow status',
    'claude-flow status --json'
  ]
});
```

### Batch Registration Pattern

```typescript
const commands = {
  init: initCommand,
  start: startCommand,
  agent: agentCommand,
  task: taskCommand,
  memory: memoryCommand,
  sparc: sparcCommand
};

registry.registerMultiple(commands);
```

## Command Execution Flow

### Execution Pipeline

```typescript
async execute(commandName: string, args: string[], flags: Record<string, any>): Promise<void> {
  const handler = this.commands.get(commandName);
  
  if (!handler) {
    throw new CLIError(
      `Unknown command: ${commandName}`,
      'UNKNOWN_COMMAND',
      1,
      true
    );
  }

  // Check prerequisites
  if (handler.prerequisites) {
    await this.checkPrerequisites(handler.prerequisites);
  }

  // Create command context
  const context: CommandContext = {
    args,
    flags,
    workingDir: this.runtime.getCurrentDir(),
    config: null,
    runtime: this.runtime
  };

  try {
    await handler.action(context);
  } catch (error) {
    if (error instanceof CLIError) {
      throw error;
    }
    
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new CLIError(
      `Command '${commandName}' failed: ${errorMessage}`,
      'COMMAND_EXECUTION_ERROR',
      1
    );
  }
}
```

### Prerequisites Checking

```typescript
private async checkPrerequisites(prerequisites: string[]): Promise<void> {
  for (const prereq of prerequisites) {
    if (prereq.startsWith('file:')) {
      const filePath = prereq.substring(5);
      if (!(await this.runtime.exists(filePath))) {
        throw new CLIError(
          `Required file not found: ${filePath}`,
          'PREREQUISITE_NOT_MET',
          1
        );
      }
    } else if (prereq.startsWith('env:')) {
      const envVar = prereq.substring(4);
      if (!this.runtime.getEnvVar(envVar)) {
        throw new CLIError(
          `Required environment variable not set: ${envVar}`,
          'PREREQUISITE_NOT_MET',
          1
        );
      }
    } else if (prereq.startsWith('command:')) {
      const command = prereq.substring(8);
      try {
        await this.runtime.spawn('which', [command], { stdio: 'ignore' });
      } catch {
        throw new CLIError(
          `Required command not found: ${command}`,
          'PREREQUISITE_NOT_MET',
          1
        );
      }
    }
  }
}
```

## Rust Error Handling Patterns

### Custom Error Types for CLI

```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum CliError {
    #[error("Unknown command: {0}")]
    UnknownCommand(String),
    
    #[error("Invalid argument: {0}")]
    InvalidArgument(String),
    
    #[error("Missing required argument: {0}")]
    MissingArgument(String),
    
    #[error("Command execution failed: {0}")]
    CommandExecutionError(String),
    
    #[error("Prerequisite not met: {0}")]
    PrerequisiteNotMet(String),
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error(transparent)]
    IoError(#[from] std::io::Error),
    
    #[error(transparent)]
    Other(#[from] anyhow::Error),
}

// Result type alias
pub type CliResult<T> = Result<T, CliError>;

// Error handling in command execution
impl CommandRegistry {
    pub async fn execute_with_error_handling(
        &self,
        command_name: &str,
        ctx: CommandContext,
    ) -> CliResult<()> {
        let handler = self.commands
            .get(command_name)
            .ok_or_else(|| CliError::UnknownCommand(command_name.to_string()))?;

        // Check prerequisites with proper error conversion
        for prereq in handler.prerequisites() {
            if let Err(e) = prereq.check(&ctx).await {
                return Err(CliError::PrerequisiteNotMet(e.to_string()));
            }
        }

        // Execute with error mapping
        handler.execute(ctx)
            .await
            .map_err(|e| CliError::CommandExecutionError(e.to_string()))
    }
}
```

### Async Command Pattern with Error Handling

```rust
use tokio::runtime::Runtime;

// Main CLI entry point
fn main() {
    let rt = Runtime::new().expect("Failed to create runtime");
    
    let result = rt.block_on(async {
        run_cli().await
    });

    match result {
        Ok(_) => std::process::exit(0),
        Err(e) => {
            eprintln!("Error: {}", e);
            
            // Show usage for specific errors
            if matches!(e, CliError::UnknownCommand(_) | CliError::InvalidArgument(_)) {
                eprintln!("\nRun 'claude-flow --help' for usage information");
            }
            
            std::process::exit(1);
        }
    }
}

async fn run_cli() -> CliResult<()> {
    let cli = Cli::parse();
    let logger = create_logger(cli.verbose);
    let mut registry = CommandRegistry::new(logger);
    
    // Register all commands
    register_commands(&mut registry);
    
    // Create runtime adapter
    let runtime = Arc::new(RuntimeAdapter::new());
    
    // Build command context
    let ctx = CommandContext {
        args: std::env::args().collect(),
        flags: extract_flags(&cli),
        working_dir: cli.working_dir.unwrap_or_else(|| ".".to_string()),
        config: load_config(&cli.config)?,
        runtime,
    };
    
    // Execute command
    match &cli.command {
        Commands::Init { sparc, yes } => {
            registry.execute_with_error_handling("init", ctx).await
        }
        Commands::Start { daemon, port, ui } => {
            registry.execute_with_error_handling("start", ctx).await
        }
        // ... handle other commands
    }
}
```

### Command Validation and Help Generation

```rust
use clap::{CommandFactory, ErrorKind};

// Custom validation
fn validate_args(cli: &Cli) -> CliResult<()> {
    match &cli.command {
        Commands::Start { port, .. } => {
            if *port == 0 {
                return Err(CliError::InvalidArgument(
                    "Port must be greater than 0".to_string()
                ));
            }
        }
        Commands::Agent(agent_cmd) => {
            if let AgentActions::Spawn { capabilities, .. } = &agent_cmd.action {
                for cap in capabilities {
                    if !is_valid_capability(cap) {
                        return Err(CliError::InvalidArgument(
                            format!("Invalid capability: {}", cap)
                        ));
                    }
                }
            }
        }
        _ => {}
    }
    Ok(())
}

// Generate help programmatically
fn show_command_help(command: &str) -> CliResult<()> {
    let mut cmd = Cli::command();
    
    match command {
        "agent" => {
            cmd.find_subcommand_mut("agent")
                .ok_or_else(|| CliError::UnknownCommand("agent".to_string()))?
                .print_help()?;
        }
        _ => cmd.print_help()?,
    }
    
    Ok(())
}
```

## Implementation Guidelines

### Command Naming Conventions
- Use kebab-case for command names (`memory-stats`, not `memoryStats`)
- Group related commands under common prefixes (`sparc run`, `sparc tdd`)
- Keep names descriptive but concise

### Error Handling Standards
- Always use CLIError for command failures
- Include specific error codes for programmatic handling
- Provide helpful error messages with context

### Context Utilization
- Access runtime services through the runtime adapter
- Use flags for optional behavior modification
- Parse arguments in order of expected parameters

This structure provides a robust foundation for CLI command organization and execution in any implementation language.