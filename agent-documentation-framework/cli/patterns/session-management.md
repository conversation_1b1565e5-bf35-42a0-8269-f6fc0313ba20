# Session Management and Persistent Context

## Overview

This document details session management patterns from claude-code-flow, showing how to implement persistent CLI sessions, state preservation, and context recovery mechanisms.

## Core Session Architecture

### Session Data Structure

The system defines a comprehensive session structure that captures complete CLI state:

```typescript
interface SessionData {
  id: string;
  name: string;
  description?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  state: {
    agents: any[];
    tasks: any[];
    memory: any[];
    configuration: any;
  };
  metadata: {
    version: string;
    platform: string;
    checksum: string;
  };
}
```

### Session Storage Management

Sessions are persisted to disk with atomic operations and integrity checking:

```typescript
const SESSION_DIR = paths.join('.claude-flow', 'sessions');

async function ensureSessionDir(): Promise<void> {
  try {
    await fs.mkdir(SESSION_DIR, { recursive: true });
  } catch (error: any) {
    if (error.code !== 'EEXIST') {
      throw error;
    }
  }
}

async function saveSession(name: string | undefined, options: any): Promise<void> {
  try {
    // Get current session state
    const currentState = await getCurrentSessionState();
    
    // Generate or collect session name
    if (!name) {
      if (options.auto) {
        name = `session-${new Date().toISOString().split('T')[0]}-${Date.now().toString().slice(-4)}`;
      } else {
        name = await Input.prompt({
          message: 'Enter session name:',
          default: `session-${new Date().toISOString().split('T')[0]}`,
        });
      }
    }

    const session: SessionData = {
      id: generateId('session'),
      name,
      description: options.description,
      tags: options.tags ? options.tags.split(',').map((t: string) => t.trim()) : [],
      createdAt: new Date(),
      updatedAt: new Date(),
      state: currentState,
      metadata: {
        version: '1.0.0',
        platform: os.platform(),
        checksum: await calculateChecksum(currentState)
      }
    };

    await ensureSessionDir();
    const filePath = paths.join(SESSION_DIR, `${session.id}.json`);
    
    // Atomic write operation
    const tempPath = `${filePath}.tmp`;
    await fs.writeFile(tempPath, JSON.stringify(session, null, 2), 'utf8');
    await fs.rename(tempPath, filePath);

    console.log(colors.green('✓ Session saved successfully'));
    console.log(`${colors.white('ID:')} ${session.id}`);
    console.log(`${colors.white('Name:')} ${session.name}`);
    console.log(`${colors.white('File:')} ${filePath}`);
    
  } catch (error) {
    console.error(colors.red('Failed to save session:'), (error as Error).message);
  }
}
```

## State Capture Patterns

### Current Session State Collection

Capturing complete system state for session persistence:

```typescript
async function getCurrentSessionState(): Promise<any> {
  const state = {
    agents: [],
    tasks: [],
    memory: [],
    configuration: {},
    processes: [],
    environmentState: {}
  };

  try {
    // Capture active agents
    state.agents = await captureAgentState();
    
    // Capture running tasks
    state.tasks = await captureTaskState();
    
    // Capture memory contents
    state.memory = await captureMemoryState();
    
    // Capture configuration
    state.configuration = await captureConfigurationState();
    
    // Capture process state
    state.processes = await captureProcessState();
    
    // Capture environment state
    state.environmentState = await captureEnvironmentState();
    
  } catch (error) {
    console.warn(colors.yellow('Warning: Failed to capture complete session state'), error);
  }

  return state;
}

async function captureAgentState(): Promise<any[]> {
  // In production, this would connect to the orchestrator
  return [
    { 
      id: 'agent-001', 
      type: 'coordinator', 
      status: 'active',
      memory: await captureAgentMemory('agent-001'),
      configuration: await getAgentConfiguration('agent-001'),
      currentTask: await getAgentCurrentTask('agent-001')
    },
    { 
      id: 'agent-002', 
      type: 'researcher', 
      status: 'active',
      memory: await captureAgentMemory('agent-002'),
      configuration: await getAgentConfiguration('agent-002')
    }
  ];
}

async function captureTaskState(): Promise<any[]> {
  return [
    { 
      id: 'task-001', 
      type: 'research', 
      status: 'running',
      progress: await getTaskProgress('task-001'),
      assignedAgent: 'agent-002',
      context: await getTaskContext('task-001'),
      startTime: Date.now() - 3600000, // 1 hour ago
      dependencies: []
    },
    { 
      id: 'task-002', 
      type: 'analysis', 
      status: 'pending',
      dependencies: ['task-001'],
      assignedAgent: null
    }
  ];
}

async function captureMemoryState(): Promise<any[]> {
  return [
    { 
      id: 'memory-001', 
      type: 'conversation', 
      agentId: 'agent-001',
      namespace: 'sparc-workflow',
      data: await getMemoryData('memory-001'),
      timestamp: Date.now()
    },
    { 
      id: 'memory-002', 
      type: 'result', 
      agentId: 'agent-002',
      namespace: 'research-findings',
      data: await getMemoryData('memory-002'),
      timestamp: Date.now() - 1800000 // 30 minutes ago
    }
  ];
}
```

## Session Restoration Patterns

### Complete Session Recovery

Restoring a session with integrity checking and validation:

```typescript
async function restoreSession(sessionId: string, options: any): Promise<void> {
  try {
    const session = await loadSession(sessionId);
    
    if (!session) {
      console.error(colors.red(`Session '${sessionId}' not found`));
      return;
    }

    // Display session information
    console.log(colors.cyan.bold('Session to restore:'));
    console.log(`${colors.white('Name:')} ${session.name}`);
    console.log(`${colors.white('Description:')} ${session.description || 'None'}`);
    console.log(`${colors.white('Agents:')} ${session.state.agents.length}`);
    console.log(`${colors.white('Tasks:')} ${session.state.tasks.length}`);
    console.log(`${colors.white('Memory Entries:')} ${session.state.memory.length}`);
    console.log(`${colors.white('Created:')} ${session.createdAt.toLocaleString()}`);

    // Integrity validation
    const expectedChecksum = await calculateChecksum(session.state);
    if (session.metadata.checksum !== expectedChecksum) {
      console.log(colors.yellow('⚠ Warning: Session checksum mismatch. Data may be corrupted.'));
      
      if (!options.force) {
        const proceed = await Confirm.prompt({
          message: 'Continue anyway?',
          default: false,
        });
        
        if (!proceed) {
          console.log(colors.gray('Restore cancelled'));
          return;
        }
      }
    }

    // User confirmation
    if (!options.force) {
      const action = options.merge ? 'merge with current session' : 'replace current session';
      const confirmed = await Confirm.prompt({
        message: `Are you sure you want to ${action}?`,
        default: false,
      });

      if (!confirmed) {
        console.log(colors.gray('Restore cancelled'));
        return;
      }
    }

    // Perform restoration
    console.log(colors.yellow('Restoring session...'));
    
    if (options.merge) {
      await mergeSession(session);
    } else {
      await replaceSession(session);
    }

    // Update session metadata
    session.updatedAt = new Date();
    const filePath = paths.join(SESSION_DIR, `${session.id}.json`);
    await fs.writeFile(filePath, JSON.stringify(session, null, 2), 'utf8');

    console.log(colors.green('✓ Session restored successfully'));
    
  } catch (error) {
    console.error(colors.red('Failed to restore session:'), (error as Error).message);
  }
}
```

### Incremental Session Merging

Merging session state with current system state:

```typescript
async function mergeSession(session: SessionData): Promise<void> {
  console.log(colors.blue('• Merging session state...'));
  
  // Merge agents
  console.log(colors.blue('• Merging agents...'));
  const currentAgents = await getCurrentAgents();
  const mergedAgents = await mergeAgents(currentAgents, session.state.agents);
  await updateAgentState(mergedAgents);

  // Merge tasks
  console.log(colors.blue('• Merging tasks...'));
  const currentTasks = await getCurrentTasks();
  const mergedTasks = await mergeTasks(currentTasks, session.state.tasks);
  await updateTaskState(mergedTasks);

  // Merge memory
  console.log(colors.blue('• Merging memory...'));
  const currentMemory = await getCurrentMemory();
  const mergedMemory = await mergeMemory(currentMemory, session.state.memory);
  await updateMemoryState(mergedMemory);

  // Merge configuration
  console.log(colors.blue('• Merging configuration...'));
  const currentConfig = await getCurrentConfiguration();
  const mergedConfig = await mergeConfiguration(currentConfig, session.state.configuration);
  await updateConfiguration(mergedConfig);
}

async function mergeAgents(current: any[], session: any[]): Promise<any[]> {
  const merged = [...current];
  
  for (const sessionAgent of session) {
    const existingIndex = merged.findIndex(agent => agent.id === sessionAgent.id);
    
    if (existingIndex >= 0) {
      // Agent exists, merge state
      merged[existingIndex] = {
        ...merged[existingIndex],
        memory: [...merged[existingIndex].memory, ...sessionAgent.memory],
        configuration: {
          ...merged[existingIndex].configuration,
          ...sessionAgent.configuration
        }
      };
    } else {
      // New agent, add to merged list
      merged.push({
        ...sessionAgent,
        id: generateId('agent'), // Generate new ID to avoid conflicts
        status: 'restored'
      });
    }
  }
  
  return merged;
}

async function mergeTasks(current: any[], session: any[]): Promise<any[]> {
  const merged = [...current];
  
  for (const sessionTask of session) {
    const existingIndex = merged.findIndex(task => 
      task.type === sessionTask.type && 
      task.context && sessionTask.context &&
      task.context.objective === sessionTask.context.objective
    );
    
    if (existingIndex < 0) {
      // Task doesn't exist, add it
      merged.push({
        ...sessionTask,
        id: generateId('task'),
        status: 'restored',
        restoredFrom: sessionTask.id
      });
    }
  }
  
  return merged;
}
```

## Process-Aware Session Management

### Daemon Session Persistence

Managing sessions in daemon mode with automatic persistence:

```typescript
export const startCommand = new Command()
  .option('-d, --daemon', 'Run as daemon in background')
  .action(async (options: StartOptions) => {
    if (options.daemon) {
      console.log(colors.yellow('Starting in daemon mode...'));
      
      // Start daemon session
      const daemonSession = await initializeDaemonSession(options);
      
      // Auto-save session state periodically
      const saveInterval = setInterval(async () => {
        try {
          await autoSaveSession(daemonSession.id);
        } catch (error) {
          console.error(colors.red('Auto-save failed:'), error);
        }
      }, 300000); // Every 5 minutes

      // Create PID file with session metadata
      const pid = process.pid;
      const pidData = {
        pid,
        sessionId: daemonSession.id,
        startTime: Date.now(),
        config: options.config || 'default',
        processes: processManager.getAllProcesses().map(p => ({ id: p.id, status: p.status }))
      };
      await writeFile('.claude-flow.pid', JSON.stringify(pidData, null, 2), 'utf-8');

      // Setup graceful shutdown with session saving
      const shutdownHandler = async () => {
        console.log(colors.yellow('Daemon shutting down, saving session...'));
        clearInterval(saveInterval);
        await saveCurrentSession(daemonSession.id);
        await cleanupDaemonSession(daemonSession.id);
        process.exit(0);
      };
      
      process.on('SIGTERM', shutdownHandler);
      process.on('SIGINT', shutdownHandler);
    }
  });

async function initializeDaemonSession(options: any): Promise<{ id: string }> {
  const sessionId = generateId('daemon-session');
  
  const daemonSession = {
    id: sessionId,
    name: `daemon-${new Date().toISOString().split('T')[0]}`,
    description: 'Automatically created daemon session',
    tags: ['daemon', 'auto'],
    type: 'daemon',
    pid: process.pid,
    startTime: Date.now(),
    autoSave: true,
    options
  };

  // Save initial session
  await saveDaemonSession(daemonSession);
  
  console.log(colors.gray(`Daemon session created: ${sessionId}`));
  return { id: sessionId };
}

async function autoSaveSession(sessionId: string): Promise<void> {
  const currentState = await getCurrentSessionState();
  const timestamp = Date.now();
  
  // Update existing session
  const session = await loadSession(sessionId);
  if (session) {
    session.state = currentState;
    session.updatedAt = new Date(timestamp);
    session.metadata.checksum = await calculateChecksum(currentState);
    
    const filePath = paths.join(SESSION_DIR, `${session.id}.json`);
    await fs.writeFile(filePath, JSON.stringify(session, null, 2), 'utf8');
    
    console.log(colors.gray(`Auto-saved session at ${new Date(timestamp).toLocaleTimeString()}`));
  }
}
```

## Session Recovery and Cleanup

### Automatic Session Recovery

Detecting and recovering from interrupted sessions:

```typescript
async function detectInterruptedSessions(): Promise<SessionData[]> {
  const sessions = await loadAllSessions();
  const interrupted: SessionData[] = [];
  
  for (const session of sessions) {
    // Check for daemon sessions with stale PIDs
    if (session.metadata && (session.metadata as any).type === 'daemon') {
      const pid = (session.metadata as any).pid;
      if (pid && !isProcessRunning(pid)) {
        interrupted.push(session);
      }
    }
    
    // Check for sessions with active processes that aren't running
    if (session.state.processes) {
      const hasStaleProcesses = session.state.processes.some((proc: any) => 
        proc.status === 'running' && !isProcessRunning(proc.pid)
      );
      
      if (hasStaleProcesses) {
        interrupted.push(session);
      }
    }
  }
  
  return interrupted;
}

async function recoverInterruptedSessions(): Promise<void> {
  const interrupted = await detectInterruptedSessions();
  
  if (interrupted.length === 0) {
    return;
  }
  
  console.log(colors.yellow(`Found ${interrupted.length} interrupted session(s)`));
  
  for (const session of interrupted) {
    console.log(`\n${colors.white('Session:')} ${session.name} (${session.id.substring(0, 8)}...)`);
    console.log(`${colors.white('Last updated:')} ${session.updatedAt.toLocaleString()}`);
    
    const action = await Select.prompt({
      message: 'What would you like to do with this session?',
      options: [
        { name: 'Restore and continue', value: 'restore' },
        { name: 'Mark as recovered (no restore)', value: 'mark' },
        { name: 'Delete corrupted session', value: 'delete' },
        { name: 'Skip for now', value: 'skip' }
      ]
    });
    
    switch (action) {
      case 'restore':
        await restoreSession(session.id, { force: true });
        break;
      case 'mark':
        await markSessionAsRecovered(session.id);
        break;
      case 'delete':
        await deleteSession(session.id, { force: true });
        break;
      case 'skip':
        continue;
    }
  }
}

function isProcessRunning(pid: number): boolean {
  try {
    process.kill(pid, 0); // Signal 0 to check if process exists
    return true;
  } catch (error) {
    return false;
  }
}

async function markSessionAsRecovered(sessionId: string): Promise<void> {
  const session = await loadSession(sessionId);
  if (session) {
    // Mark all processes as stopped
    if (session.state.processes) {
      session.state.processes.forEach((proc: any) => {
        if (proc.status === 'running') {
          proc.status = 'interrupted';
          proc.endTime = Date.now();
        }
      });
    }
    
    // Update metadata
    session.metadata = {
      ...session.metadata,
      recovered: true,
      recoveryTime: Date.now()
    };
    
    session.updatedAt = new Date();
    
    const filePath = paths.join(SESSION_DIR, `${session.id}.json`);
    await fs.writeFile(filePath, JSON.stringify(session, null, 2), 'utf8');
    
    console.log(colors.green(`✓ Session marked as recovered: ${session.name}`));
  }
}
```

### Session Cleanup Utilities

Automated cleanup of old and orphaned sessions:

```typescript
async function cleanSessions(options: any): Promise<void> {
  try {
    await ensureSessionDir();
    const sessions = await loadAllSessions();
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - options.olderThan);
    
    let toDelete = sessions.filter(session => session.createdAt < cutoffDate);
    
    if (options.orphaned) {
      // Additional filtering for orphaned sessions
      toDelete = toDelete.filter(session => {
        // Check if session has references to non-existent resources
        return isSessionOrphaned(session);
      });
    }

    if (toDelete.length === 0) {
      console.log(colors.gray('No sessions to clean'));
      return;
    }

    console.log(colors.cyan.bold(`Sessions to clean (${toDelete.length})`));
    console.log('─'.repeat(50));
    
    for (const session of toDelete) {
      const age = Math.floor((Date.now() - session.createdAt.getTime()) / (1000 * 60 * 60 * 24));
      console.log(`• ${session.name} (${colors.gray(session.id.substring(0, 8) + '...')}) - ${age} days old`);
    }

    if (options.dryRun) {
      console.log('\n' + colors.yellow('Dry run mode - no files were deleted'));
      return;
    }

    console.log();
    const confirmed = await Confirm.prompt({
      message: `Delete ${toDelete.length} sessions?`,
      default: false,
    });

    if (!confirmed) {
      console.log(colors.gray('Clean cancelled'));
      return;
    }

    let deleted = 0;
    for (const session of toDelete) {
      try {
        await deleteSessionFiles(session.id);
        deleted++;
      } catch (error) {
        console.error(colors.red(`Failed to delete ${session.name}:`), (error as Error).message);
      }
    }

    console.log(colors.green(`✓ Cleaned ${deleted} sessions`));
  } catch (error) {
    console.error(colors.red('Failed to clean sessions:'), (error as Error).message);
  }
}

function isSessionOrphaned(session: SessionData): boolean {
  // Check if session references non-existent agents, tasks, or resources
  try {
    // This would implement actual orphan detection logic
    // For example, checking if referenced files, processes, or memory exists
    return false; // Placeholder
  } catch {
    return true; // If we can't verify, consider it orphaned
  }
}

async function deleteSessionFiles(sessionId: string): Promise<void> {
  const filePath = paths.join(SESSION_DIR, `${sessionId}.json`);
  await fs.unlink(filePath);
  
  // Also clean up any associated files
  const associatedFiles = [
    paths.join(SESSION_DIR, `${sessionId}.backup`),
    paths.join(SESSION_DIR, `${sessionId}.log`),
    paths.join('.claude-flow', 'session-data', sessionId)
  ];
  
  for (const file of associatedFiles) {
    try {
      await fs.unlink(file);
    } catch {
      // Ignore errors for optional files
    }
  }
}
```

## Session Import/Export

### Cross-Platform Session Portability

Exporting and importing sessions across different environments:

```typescript
async function exportSession(sessionId: string, outputFile: string, options: any): Promise<void> {
  try {
    const session = await loadSession(sessionId);
    
    if (!session) {
      console.error(colors.red(`Session '${sessionId}' not found`));
      return;
    }

    let exportData = session;
    
    // Apply export options
    if (!options.includeMemory) {
      exportData = {
        ...session,
        state: {
          ...session.state,
          memory: [] // Exclude memory data
        }
      };
    }
    
    if (options.anonymize) {
      exportData = anonymizeSession(exportData);
    }

    // Add export metadata
    const exportMetadata = {
      exportedAt: new Date().toISOString(),
      exportedBy: os.userInfo().username,
      exportVersion: '1.0.0',
      originalPlatform: session.metadata.platform,
      exportOptions: options
    };

    const exportPackage = {
      metadata: exportMetadata,
      session: exportData,
      version: '1.0.0'
    };

    let content: string;
    if (options.format === 'yaml') {
      // Would use a YAML library in production
      console.log(colors.yellow('YAML export not implemented yet, using JSON'));
      content = JSON.stringify(exportPackage, null, 2);
    } else {
      content = JSON.stringify(exportPackage, null, 2);
    }

    await fs.writeFile(outputFile, content, 'utf8');

    console.log(colors.green('✓ Session exported successfully'));
    console.log(`${colors.white('File:')} ${outputFile}`);
    console.log(`${colors.white('Format:')} ${options.format}`);
    console.log(`${colors.white('Size:')} ${Buffer.from(content).length} bytes`);
    
  } catch (error) {
    console.error(colors.red('Failed to export session:'), (error as Error).message);
  }
}

async function importSession(inputFile: string, options: any): Promise<void> {
  try {
    const content = await fs.readFile(inputFile, 'utf8');
    const importPackage = JSON.parse(content);

    // Validate import package structure
    if (!importPackage.session || !importPackage.metadata) {
      throw new Error('Invalid session export file format');
    }

    const sessionData = importPackage.session as SessionData;

    // Validate session data structure
    if (!sessionData.id || !sessionData.name || !sessionData.state) {
      throw new Error('Invalid session data structure');
    }

    // Handle cross-platform compatibility
    if (importPackage.metadata.originalPlatform !== os.platform()) {
      console.log(colors.yellow(`⚠ Cross-platform import: ${importPackage.metadata.originalPlatform} → ${os.platform()}`));
      sessionData.state = await adaptSessionForPlatform(sessionData.state, os.platform());
    }

    // Generate new ID if not overwriting
    if (!options.overwrite) {
      sessionData.id = generateId('session');
    }

    // Update name if specified
    if (options.name) {
      sessionData.name = options.name;
    }

    // Check if session already exists
    const existingSession = await loadSession(sessionData.id);
    if (existingSession && !options.overwrite) {
      console.error(colors.red('Session with this ID already exists'));
      console.log(colors.gray('Use --overwrite to replace it'));
      return;
    }

    // Update timestamps and metadata
    if (options.overwrite && existingSession) {
      sessionData.updatedAt = new Date();
    } else {
      sessionData.createdAt = new Date();
      sessionData.updatedAt = new Date();
    }

    // Update metadata for current environment
    sessionData.metadata = {
      ...sessionData.metadata,
      platform: os.platform(),
      importedAt: Date.now(),
      importedFrom: inputFile,
      checksum: await calculateChecksum(sessionData.state)
    };

    await ensureSessionDir();
    const filePath = paths.join(SESSION_DIR, `${sessionData.id}.json`);
    await fs.writeFile(filePath, JSON.stringify(sessionData, null, 2), 'utf8');

    console.log(colors.green('✓ Session imported successfully'));
    console.log(`${colors.white('ID:')} ${sessionData.id}`);
    console.log(`${colors.white('Name:')} ${sessionData.name}`);
    console.log(`${colors.white('Action:')} ${options.overwrite ? 'Overwritten' : 'Created'}`);
    
  } catch (error) {
    console.error(colors.red('Failed to import session:'), (error as Error).message);
  }
}

function anonymizeSession(session: SessionData): SessionData {
  // Remove or anonymize sensitive information
  return {
    ...session,
    name: `anonymous-session-${Date.now()}`,
    description: session.description ? '[ANONYMIZED]' : undefined,
    state: {
      ...session.state,
      agents: session.state.agents.map((agent: any) => ({
        ...agent,
        id: generateId('agent'),
        memory: agent.memory.map((mem: any) => ({
          ...mem,
          data: '[ANONYMIZED]'
        }))
      })),
      memory: session.state.memory.map((mem: any) => ({
        ...mem,
        data: '[ANONYMIZED]'
      }))
    }
  };
}

async function adaptSessionForPlatform(state: any, targetPlatform: string): Promise<any> {
  // Platform-specific adaptations
  const adapted = { ...state };
  
  // Adapt file paths
  if (adapted.configuration && adapted.configuration.paths) {
    adapted.configuration.paths = adaptPaths(adapted.configuration.paths, targetPlatform);
  }
  
  // Adapt process references
  if (adapted.processes) {
    adapted.processes = adapted.processes.map((proc: any) => ({
      ...proc,
      command: adaptCommand(proc.command, targetPlatform),
      env: adaptEnvironmentVariables(proc.env, targetPlatform)
    }));
  }
  
  return adapted;
}
```

## Implementation Guidelines

### Session Management Design Principles

1. **Atomicity**: Session operations should be atomic to prevent corruption
2. **Integrity**: Use checksums and validation to ensure session integrity
3. **Portability**: Design sessions to work across different environments
4. **Recovery**: Implement robust recovery mechanisms for interrupted sessions
5. **Performance**: Optimize for frequent auto-save operations

### Best Practices

- Use atomic file operations for session persistence
- Implement comprehensive state capture for all relevant system components
- Provide flexible merge strategies for session restoration
- Include integrity checking with checksums
- Support cross-platform session portability
- Implement automatic cleanup of old and orphaned sessions
- Provide clear user feedback during session operations
- Handle interrupted sessions gracefully with recovery options

This session management system provides a robust foundation for preserving and restoring complex CLI application state across different execution contexts and environments.