# Agent Documentation Navigation

## Consolidated Overview Files

The documentation has been consolidated from 72+ CLAUDE.md files into 3 domain-specific overview files plus the main project CLAUDE.md:

### 1. Core System & Features
📄 **[CORE-SYSTEM-FEATURES.md](./CORE-SYSTEM-FEATURES.md)**
- CLI commands and quick start guide
- SPARC modes and swarm coordination
- Enterprise features and workflows
- Batch tool patterns and memory management
- All primary system capabilities

### 2. Architecture & Services
📄 **[ARCHITECTURE-SERVICES.md](./ARCHITECTURE-SERVICES.md)**
- System architecture and design patterns
- Microservices architecture and communication
- Infrastructure requirements and technology stack
- Security, monitoring, and deployment
- Performance and scalability considerations

### 3. Concepts & Coordination
📄 **[CONCEPTS-COORDINATION.md](./CONCEPTS-COORDINATION.md)**
- Core system concepts and agent architecture
- Coordination patterns and communication models
- Agent workflows and team collaboration
- MCP integration and basic-memory usage
- Framework navigation and best practices

### 4. Main Project Configuration
📄 **[Main CLAUDE.md](../CLAUDE.md)**
- Build commands and development workflow
- Claude-Flow command reference
- Integration patterns and coding standards
- Quick start workflows for different use cases

## Detailed Documentation Structure

For specific implementation details, refer to the detailed documentation in subdirectories:

- **features/**: SPARC modes, swarm strategies, and core capabilities
- **services/**: Individual service documentation and APIs
- **architecture/**: System design patterns and scalability
- **infrastructure/**: Deployment, monitoring, and operations
- **integration/**: External system integration patterns
- **enterprise/**: Enterprise features and compliance
- **protocols/**: Communication protocols and standards
- **concepts/**: Foundational concepts and patterns

## Quick Navigation

### For New Users
1. Start with [CORE-SYSTEM-FEATURES.md](./CORE-SYSTEM-FEATURES.md) for system overview
2. Review [CONCEPTS-COORDINATION.md](./CONCEPTS-COORDINATION.md) for fundamental concepts
3. Check [Main CLAUDE.md](../CLAUDE.md) for development setup

### For Developers
1. Review [ARCHITECTURE-SERVICES.md](./ARCHITECTURE-SERVICES.md) for system design
2. Explore `features/` and `services/` for implementation details
3. Check `infrastructure/` for deployment requirements

### For Operations Teams
1. Focus on [ARCHITECTURE-SERVICES.md](./ARCHITECTURE-SERVICES.md) infrastructure section
2. Review `infrastructure/` for detailed operational guides
3. Check `enterprise/` for compliance and security requirements

## Consolidation Results

- **Before**: 72+ CLAUDE.md files with significant redundancy
- **After**: 4 domain-specific files (75%+ reduction)
- **Benefits**: Clearer navigation, reduced duplication, improved maintainability
- **Coverage**: All original content preserved and organized by domain

This consolidation eliminates documentation sprawl while maintaining comprehensive coverage of all system capabilities and implementation guidance.