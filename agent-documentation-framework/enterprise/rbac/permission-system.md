# Role and Permission Management System

## Overview

This document outlines the comprehensive role and permission management system for claude-code-flow enterprise deployments, providing fine-grained access control and dynamic permission evaluation.

## Rust RBAC Implementation

### Core Permission Traits and Types

```rust
use std::collections::{HashMap, HashSet};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use anyhow::{Result, bail};

// Core permission types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Role {
    pub name: String,
    pub permissions: Vec<Permission>,
    pub description: String,
    pub hierarchy: Vec<String>,  // Parent roles for inheritance
    pub conditions: Vec<Condition>,
    pub tenant_scoped: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Permission {
    pub resource: Resource,
    pub actions: HashSet<Action>,
    pub conditions: Vec<Condition>,
    pub scope: Option<PermissionScope>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum Resource {
    All,
    Memory,
    Agents,
    Tasks,
    Projects,
    Tenants,
    Analytics,
    AuditLogs,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum Action {
    Read,
    Write,
    Create,
    Delete,
    Execute,
    Spawn,
    Monitor,
    Terminate,
    Export,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Condition {
    pub field: String,
    pub operator: ConditionOperator,
    pub value: serde_json::Value,
    pub context: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConditionOperator {
    Equals,
    In,
    Regex,
    Range,
    GreaterThan,
    LessThan,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionScope {
    pub tenant_id: Option<String>,
    pub project_id: Option<String>,
    pub namespaces: Option<Vec<String>>,
    pub time_window: Option<TimeWindow>,
    pub ip_ranges: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeWindow {
    pub start: String,  // "06:00"
    pub end: String,    // "22:00"
    pub days: Option<Vec<String>>,  // ["Mon", "Tue", "Wed", "Thu", "Fri"]
}

// Principal types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Principal {
    pub id: String,
    pub principal_type: PrincipalType,
    pub attributes: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PrincipalType {
    User,
    Agent,
    Service,
    ApiKey,
}

// Authorization context
#[derive(Clone)]
pub struct AuthContext {
    pub principal: Principal,
    pub tenant_id: Option<String>,
    pub session_id: String,
    pub ip_address: String,
    pub timestamp: DateTime<Utc>,
    pub mfa_verified: bool,
    pub metadata: HashMap<String, serde_json::Value>,
}

// RBAC trait
#[async_trait]
pub trait RbacAuthorizer: Send + Sync {
    async fn authorize(
        &self,
        auth: &AuthContext,
        resource: &Resource,
        action: &Action,
    ) -> Result<AuthorizationResult>;

    async fn get_principal_roles(&self, principal_id: &str) -> Result<Vec<Role>>;
    
    async fn assign_role(
        &self,
        principal_id: &str,
        role_name: &str,
        scope: Option<PermissionScope>,
    ) -> Result<RoleAssignment>;
    
    async fn revoke_role(
        &self,
        principal_id: &str,
        role_name: &str,
    ) -> Result<()>;
}

#[derive(Debug, Clone)]
pub struct AuthorizationResult {
    pub granted: bool,
    pub reason: Option<String>,
    pub matched_role: Option<String>,
    pub matched_permission: Option<Permission>,
    pub evaluated_at: DateTime<Utc>,
}
```

### Role Definition Builder

```rust
// Fluent API for building roles
pub struct RoleBuilder {
    role: Role,
}

impl RoleBuilder {
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            role: Role {
                name: name.into(),
                permissions: Vec::new(),
                description: String::new(),
                hierarchy: Vec::new(),
                conditions: Vec::new(),
                tenant_scoped: false,
            }
        }
    }

    pub fn description(mut self, desc: impl Into<String>) -> Self {
        self.role.description = desc.into();
        self
    }

    pub fn inherit_from(mut self, parent: impl Into<String>) -> Self {
        self.role.hierarchy.push(parent.into());
        self
    }

    pub fn tenant_scoped(mut self, scoped: bool) -> Self {
        self.role.tenant_scoped = scoped;
        self
    }

    pub fn add_permission(mut self, permission: Permission) -> Self {
        self.role.permissions.push(permission);
        self
    }

    pub fn with_permission<F>(mut self, f: F) -> Self 
    where
        F: FnOnce(PermissionBuilder) -> Permission
    {
        let permission = f(PermissionBuilder::new());
        self.role.permissions.push(permission);
        self
    }

    pub fn require_mfa(mut self) -> Self {
        self.role.conditions.push(Condition {
            field: "mfa_verified".to_string(),
            operator: ConditionOperator::Equals,
            value: serde_json::json!(true),
            context: None,
        });
        self
    }

    pub fn build(self) -> Role {
        self.role
    }
}

pub struct PermissionBuilder {
    permission: Permission,
}

impl PermissionBuilder {
    pub fn new() -> Self {
        Self {
            permission: Permission {
                resource: Resource::All,
                actions: HashSet::new(),
                conditions: Vec::new(),
                scope: None,
            }
        }
    }

    pub fn resource(mut self, resource: Resource) -> Self {
        self.permission.resource = resource;
        self
    }

    pub fn actions(mut self, actions: impl IntoIterator<Item = Action>) -> Self {
        self.permission.actions = actions.into_iter().collect();
        self
    }

    pub fn all_actions(mut self) -> Self {
        use Action::*;
        self.permission.actions = vec![
            Read, Write, Create, Delete, Execute, 
            Spawn, Monitor, Terminate, Export
        ].into_iter().collect();
        self
    }

    pub fn condition(mut self, condition: Condition) -> Self {
        self.permission.conditions.push(condition);
        self
    }

    pub fn tenant_scoped(mut self, tenant_id: String) -> Self {
        self.permission.scope = Some(PermissionScope {
            tenant_id: Some(tenant_id),
            project_id: None,
            namespaces: None,
            time_window: None,
            ip_ranges: None,
        });
        self
    }

    pub fn build(self) -> Permission {
        self.permission
    }
}

// Predefined roles
pub fn create_default_roles() -> Vec<Role> {
    vec![
        RoleBuilder::new("system-admin")
            .description("Full system access across all tenants")
            .require_mfa()
            .with_permission(|p| p
                .resource(Resource::All)
                .all_actions()
                .build()
            )
            .build(),

        RoleBuilder::new("tenant-admin")
            .description("Full access within tenant boundary")
            .tenant_scoped(true)
            .with_permission(|p| p
                .resource(Resource::All)
                .all_actions()
                .condition(Condition {
                    field: "tenant_id".to_string(),
                    operator: ConditionOperator::Equals,
                    value: serde_json::json!("${auth.tenant_id}"),
                    context: Some("auth".to_string()),
                })
                .build()
            )
            .build(),

        RoleBuilder::new("developer")
            .description("Standard development access")
            .tenant_scoped(true)
            .inherit_from("user")
            .with_permission(|p| p
                .resource(Resource::Projects)
                .actions(vec![Action::Read, Action::Write])
                .build()
            )
            .with_permission(|p| p
                .resource(Resource::Agents)
                .actions(vec![Action::Spawn, Action::Monitor])
                .condition(Condition {
                    field: "time_window".to_string(),
                    operator: ConditionOperator::In,
                    value: serde_json::json!(["06:00-22:00"]),
                    context: None,
                })
                .build()
            )
            .build(),
    ]
}
```

## Core RBAC Architecture

### Role Definition Framework

Based on claude-code-flow's RBAC authorizer implementation:

```typescript
// Role and permission definitions from claude-code-flow RBAC
interface Role {
  name: string;
  permissions: Permission[];
  description: string;
  hierarchy?: string[];        // Parent roles for inheritance
  conditions?: Condition[];    // Dynamic role conditions
  tenantScoped?: boolean;      // Tenant-specific role
}

interface Permission {
  resource: string;           // Resource identifier (*, memory, agents, tasks)
  actions: string[];          // Allowed actions (read, write, delete, execute)
  conditions?: Condition[];   // Dynamic permission conditions
  scope?: PermissionScope;    // Scope restrictions
}

interface Condition {
  field: string;              // Field to evaluate (namespace, time, ip)
  operator: 'equals' | 'in' | 'regex' | 'range';
  value: any;                 // Expected value or pattern
  context?: string;           // Context for value resolution
}

interface PermissionScope {
  tenantId?: string;          // Tenant restriction
  projectId?: string;         // Project restriction
  namespaces?: string[];      // Namespace restrictions
  timeWindow?: string;        // Time-based restrictions
  ipRanges?: string[];        // IP-based restrictions
}
```

### Predefined Role Hierarchy

```typescript
// Predefined roles from claude-code-flow enterprise configuration
const enterpriseRoles: Role[] = [
  {
    name: 'system-admin',
    description: 'Full system access across all tenants',
    permissions: [
      {
        resource: '*',
        actions: ['*'],
        conditions: [
          {
            field: 'mfa_verified',
            operator: 'equals',
            value: true
          }
        ]
      }
    ]
  },
  {
    name: 'tenant-admin',
    description: 'Full access within tenant boundary',
    tenantScoped: true,
    permissions: [
      {
        resource: 'tenant:*',
        actions: ['*'],
        conditions: [
          {
            field: 'tenant_id',
            operator: 'equals',
            value: '${auth.tenantId}',
            context: 'auth'
          }
        ]
      }
    ]
  },
  {
    name: 'project-manager',
    description: 'Project management within tenant',
    tenantScoped: true,
    permissions: [
      {
        resource: 'projects',
        actions: ['read', 'write', 'create', 'delete'],
        scope: {
          tenantId: '${auth.tenantId}'
        }
      },
      {
        resource: 'agents',
        actions: ['spawn', 'monitor', 'terminate'],
        scope: {
          tenantId: '${auth.tenantId}'
        }
      },
      {
        resource: 'tasks',
        actions: ['create', 'monitor', 'cancel'],
        scope: {
          tenantId: '${auth.tenantId}'
        }
      }
    ]
  },
  {
    name: 'developer',
    description: 'Standard development access',
    tenantScoped: true,
    permissions: [
      {
        resource: 'projects',
        actions: ['read', 'write'],
        conditions: [
          {
            field: 'project_member',
            operator: 'equals',
            value: true
          }
        ]
      },
      {
        resource: 'agents',
        actions: ['spawn', 'monitor'],
        conditions: [
          {
            field: 'time_window',
            operator: 'in',
            value: ['06:00-22:00']
          }
        ]
      },
      {
        resource: 'memory',
        actions: ['read', 'write'],
        conditions: [
          {
            field: 'namespace',
            operator: 'equals',
            value: '${auth.tenantId}'
          }
        ]
      }
    ]
  },
  {
    name: 'analyst',
    description: 'Read-only analysis access',
    tenantScoped: true,
    permissions: [
      {
        resource: 'projects',
        actions: ['read'],
        scope: {
          tenantId: '${auth.tenantId}'
        }
      },
      {
        resource: 'memory',
        actions: ['read'],
        conditions: [
          {
            field: 'category',
            operator: 'in',
            value: ['research', 'analysis', 'findings', 'reports']
          }
        ]
      },
      {
        resource: 'analytics',
        actions: ['read', 'query'],
        scope: {
          tenantId: '${auth.tenantId}'
        }
      }
    ]
  },
  {
    name: 'auditor',
    description: 'Audit and compliance access',
    permissions: [
      {
        resource: 'audit-logs',
        actions: ['read', 'export'],
        conditions: [
          {
            field: 'clearance_level',
            operator: 'in',
            value: ['auditor', 'compliance']
          }
        ]
      },
      {
        resource: 'compliance-reports',
        actions: ['read', 'generate'],
        scope: {
          tenantId: '${auth.tenantId}'
        }
      }
    ]
  }
];
```

## Rust Permission Management Engine

### Permission Evaluator Implementation

```rust
use std::sync::Arc;
use tokio::sync::RwLock;
use regex::Regex;

pub struct PermissionEvaluator {
    role_store: Arc<dyn RoleStore>,
    assignment_store: Arc<dyn RoleAssignmentStore>,
    cache: Arc<RwLock<PermissionCache>>,
}

impl PermissionEvaluator {
    pub fn new(
        role_store: Arc<dyn RoleStore>,
        assignment_store: Arc<dyn RoleAssignmentStore>,
    ) -> Self {
        Self {
            role_store,
            assignment_store,
            cache: Arc::new(RwLock::new(PermissionCache::new())),
        }
    }

    pub async fn evaluate_permission(
        &self,
        auth: &AuthContext,
        resource: &Resource,
        action: &Action,
    ) -> Result<AuthorizationResult> {
        // Check cache first
        let cache_key = format!("{}:{}:{:?}:{:?}", 
            auth.principal.id, auth.tenant_id.as_deref().unwrap_or(""), 
            resource, action
        );
        
        if let Some(cached) = self.cache.read().await.get(&cache_key) {
            return Ok(cached.clone());
        }

        // Get all role assignments for principal
        let assignments = self.assignment_store
            .get_active_assignments(&auth.principal.id)
            .await?;

        // Evaluate each role
        for assignment in assignments {
            // Check assignment validity
            if !self.is_assignment_valid(&assignment, auth).await? {
                continue;
            }

            // Get role definition
            let role = self.role_store.get_role(&assignment.role_name).await?;
            if let Some(role) = role {
                // Check role conditions
                if !self.evaluate_conditions(&role.conditions, auth).await? {
                    continue;
                }

                // Evaluate permissions
                if let Some(result) = self.evaluate_role_permissions(
                    &role, resource, action, auth
                ).await? {
                    // Cache the result
                    self.cache.write().await.set(cache_key, result.clone());
                    return Ok(result);
                }
            }
        }

        // No permission granted
        let result = AuthorizationResult {
            granted: false,
            reason: Some("No matching permission found".to_string()),
            matched_role: None,
            matched_permission: None,
            evaluated_at: Utc::now(),
        };

        self.cache.write().await.set(cache_key, result.clone());
        Ok(result)
    }

    async fn evaluate_role_permissions(
        &self,
        role: &Role,
        resource: &Resource,
        action: &Action,
        auth: &AuthContext,
    ) -> Result<Option<AuthorizationResult>> {
        // Check inherited permissions first
        for parent_role_name in &role.hierarchy {
            if let Some(parent_role) = self.role_store.get_role(parent_role_name).await? {
                if let Some(result) = self.evaluate_role_permissions(
                    &parent_role, resource, action, auth
                ).await? {
                    return Ok(Some(result));
                }
            }
        }

        // Check direct permissions
        for permission in &role.permissions {
            if self.matches_resource(&permission.resource, resource) &&
               permission.actions.contains(action) {
                
                // Evaluate permission conditions
                if !self.evaluate_conditions(&permission.conditions, auth).await? {
                    continue;
                }

                // Check scope
                if let Some(scope) = &permission.scope {
                    if !self.evaluate_scope(scope, auth).await? {
                        continue;
                    }
                }

                return Ok(Some(AuthorizationResult {
                    granted: true,
                    reason: None,
                    matched_role: Some(role.name.clone()),
                    matched_permission: Some(permission.clone()),
                    evaluated_at: Utc::now(),
                }));
            }
        }

        Ok(None)
    }

    fn matches_resource(&self, perm_resource: &Resource, requested: &Resource) -> bool {
        match perm_resource {
            Resource::All => true,
            _ => perm_resource == requested,
        }
    }

    async fn evaluate_conditions(
        &self,
        conditions: &[Condition],
        auth: &AuthContext,
    ) -> Result<bool> {
        for condition in conditions {
            if !self.evaluate_condition(condition, auth).await? {
                return Ok(false);
            }
        }
        Ok(true)
    }

    async fn evaluate_condition(
        &self,
        condition: &Condition,
        auth: &AuthContext,
    ) -> Result<bool> {
        let actual_value = self.get_field_value(&condition.field, auth)?;
        let expected_value = self.resolve_value(&condition.value, auth)?;

        Ok(match &condition.operator {
            ConditionOperator::Equals => actual_value == expected_value,
            ConditionOperator::In => {
                if let serde_json::Value::Array(arr) = expected_value {
                    arr.contains(&actual_value)
                } else {
                    false
                }
            }
            ConditionOperator::Regex => {
                if let (serde_json::Value::String(pattern), serde_json::Value::String(value)) = 
                    (expected_value, actual_value) {
                    Regex::new(&pattern)?.is_match(&value)
                } else {
                    false
                }
            }
            ConditionOperator::GreaterThan => {
                self.compare_values(&actual_value, &expected_value, std::cmp::Ordering::Greater)
            }
            ConditionOperator::LessThan => {
                self.compare_values(&actual_value, &expected_value, std::cmp::Ordering::Less)
            }
            ConditionOperator::Range => {
                // Expect expected_value to be an array of [min, max]
                if let serde_json::Value::Array(range) = expected_value {
                    if range.len() == 2 {
                        self.compare_values(&actual_value, &range[0], std::cmp::Ordering::Greater) &&
                        self.compare_values(&actual_value, &range[1], std::cmp::Ordering::Less)
                    } else {
                        false
                    }
                } else {
                    false
                }
            }
        })
    }

    fn get_field_value(
        &self,
        field: &str,
        auth: &AuthContext,
    ) -> Result<serde_json::Value> {
        Ok(match field {
            "mfa_verified" => serde_json::json!(auth.mfa_verified),
            "tenant_id" => serde_json::json!(auth.tenant_id),
            "principal_type" => serde_json::json!(auth.principal.principal_type),
            "ip_address" => serde_json::json!(auth.ip_address),
            "timestamp" => serde_json::json!(auth.timestamp.to_rfc3339()),
            _ => {
                // Check principal attributes
                auth.principal.attributes.get(field)
                    .cloned()
                    .unwrap_or(serde_json::Value::Null)
            }
        })
    }

    fn resolve_value(
        &self,
        value: &serde_json::Value,
        auth: &AuthContext,
    ) -> Result<serde_json::Value> {
        if let serde_json::Value::String(s) = value {
            if s.contains("${") {
                // Simple template resolution
                let resolved = s.replace("${auth.tenant_id}", 
                    auth.tenant_id.as_deref().unwrap_or(""));
                return Ok(serde_json::json!(resolved));
            }
        }
        Ok(value.clone())
    }
}

// Role assignment management
pub struct RoleManager {
    role_store: Arc<dyn RoleStore>,
    assignment_store: Arc<dyn RoleAssignmentStore>,
    audit_logger: Arc<dyn AuditLogger>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleAssignment {
    pub id: String,
    pub principal_id: String,
    pub principal_type: PrincipalType,
    pub role_name: String,
    pub scope: Option<PermissionScope>,
    pub assigned_at: DateTime<Utc>,
    pub assigned_by: String,
    pub expires_at: Option<DateTime<Utc>>,
    pub revoked_at: Option<DateTime<Utc>>,
    pub revoked_by: Option<String>,
    pub revocation_reason: Option<String>,
}

impl RoleManager {
    pub async fn assign_role(
        &self,
        principal_id: &str,
        principal_type: PrincipalType,
        role_name: &str,
        scope: Option<PermissionScope>,
        assigned_by: &str,
    ) -> Result<RoleAssignment> {
        // Verify role exists
        let role = self.role_store.get_role(role_name).await?
            .ok_or_else(|| anyhow::anyhow!("Role {} not found", role_name))?;

        // Check if assignment already exists
        let existing = self.assignment_store
            .find_assignment(principal_id, role_name)
            .await?;
        
        if let Some(existing) = existing {
            if existing.revoked_at.is_none() {
                bail!("Role {} already assigned to principal {}", role_name, principal_id);
            }
        }

        let assignment = RoleAssignment {
            id: Uuid::new_v4().to_string(),
            principal_id: principal_id.to_string(),
            principal_type,
            role_name: role_name.to_string(),
            scope,
            assigned_at: Utc::now(),
            assigned_by: assigned_by.to_string(),
            expires_at: None,
            revoked_at: None,
            revoked_by: None,
            revocation_reason: None,
        };

        self.assignment_store.save_assignment(&assignment).await?;
        
        self.audit_logger.log_role_assignment(
            principal_id,
            role_name,
            assigned_by,
        ).await?;

        Ok(assignment)
    }

    pub async fn revoke_role(
        &self,
        principal_id: &str,
        role_name: &str,
        revoked_by: &str,
        reason: &str,
    ) -> Result<()> {
        let mut assignment = self.assignment_store
            .find_assignment(principal_id, role_name)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Role assignment not found"))?;

        if assignment.revoked_at.is_some() {
            bail!("Role assignment already revoked");
        }

        assignment.revoked_at = Some(Utc::now());
        assignment.revoked_by = Some(revoked_by.to_string());
        assignment.revocation_reason = Some(reason.to_string());

        self.assignment_store.save_assignment(&assignment).await?;
        
        self.audit_logger.log_role_revocation(
            principal_id,
            role_name,
            revoked_by,
            reason,
        ).await?;

        Ok(())
    }
}
```

## Permission Management Engine

### Role Assignment and Management

```typescript
// Role management system based on claude-code-flow agent permissions
class RoleManager {
  async assignRole(
    principalId: string,
    principalType: 'user' | 'agent' | 'service',
    roleId: string,
    scope?: RoleScope
  ): Promise<RoleAssignment> {
    
    // Validate role exists and is appropriate for principal type
    const role = await this.getRoleDefinition(roleId);
    if (!role) {
      throw new Error(`Role ${roleId} not found`);
    }

    // Check if assignment is allowed
    const canAssign = await this.validateRoleAssignment(
      principalId,
      principalType,
      role,
      scope
    );
    
    if (!canAssign.allowed) {
      throw new Error(`Role assignment denied: ${canAssign.reason}`);
    }

    const assignment: RoleAssignment = {
      id: this.generateAssignmentId(),
      principalId,
      principalType,
      roleId: role.name,
      scope: scope || {},
      assignedAt: new Date(),
      assignedBy: await this.getCurrentUser(),
      expiresAt: scope?.expiresAt,
      conditions: role.conditions || []
    };

    await this.roleAssignmentStore.store(assignment);
    await this.auditLogger.logRoleAssignment(assignment);
    
    // Invalidate permission cache for principal
    await this.permissionCache.invalidate(principalId);
    
    return assignment;
  }

  async revokeRole(
    principalId: string,
    roleId: string,
    reason: string
  ): Promise<void> {
    
    const assignment = await this.roleAssignmentStore.findByPrincipalAndRole(
      principalId,
      roleId
    );
    
    if (!assignment) {
      throw new Error('Role assignment not found');
    }

    assignment.revokedAt = new Date();
    assignment.revokedBy = await this.getCurrentUser();
    assignment.revocationReason = reason;

    await this.roleAssignmentStore.update(assignment);
    await this.auditLogger.logRoleRevocation(assignment);
    await this.permissionCache.invalidate(principalId);
  }

  async getRoleHierarchy(roleId: string): Promise<RoleHierarchy> {
    const role = await this.getRoleDefinition(roleId);
    const hierarchy = {
      role,
      parents: [] as Role[],
      children: [] as Role[],
      inheritedPermissions: [] as Permission[]
    };

    // Get parent roles
    if (role.hierarchy) {
      for (const parentRoleId of role.hierarchy) {
        const parentRole = await this.getRoleDefinition(parentRoleId);
        if (parentRole) {
          hierarchy.parents.push(parentRole);
          hierarchy.inheritedPermissions.push(...parentRole.permissions);
        }
      }
    }

    // Get child roles
    const allRoles = await this.getAllRoles();
    hierarchy.children = allRoles.filter(r => 
      r.hierarchy?.includes(roleId)
    );

    return hierarchy;
  }
}
```

### Dynamic Permission Evaluation

```typescript
// Permission evaluation engine from claude-code-flow RBAC authorizer
class PermissionEvaluator {
  async evaluatePermission(
    auth: AuthContext,
    resource: string,
    action: string,
    context?: EvaluationContext
  ): Promise<PermissionResult> {
    
    // Get all roles for the principal
    const roles = await this.getRoleAssignments(auth.principalId);
    
    // Evaluate each role's permissions
    for (const roleAssignment of roles) {
      const role = await this.getRoleDefinition(roleAssignment.roleId);
      
      if (!role) continue;
      
      // Check if role assignment is still valid
      if (!this.isAssignmentValid(roleAssignment)) {
        continue;
      }

      // Evaluate role permissions
      const permission = await this.evaluateRolePermission(
        role,
        resource,
        action,
        auth,
        context
      );
      
      if (permission.granted) {
        return {
          granted: true,
          role: role.name,
          permission: permission.matchedPermission,
          evaluatedAt: new Date(),
          context
        };
      }
    }

    return {
      granted: false,
      reason: 'no_matching_permission',
      evaluatedAt: new Date(),
      context
    };
  }

  private async evaluateRolePermission(
    role: Role,
    resource: string,
    action: string,
    auth: AuthContext,
    context?: EvaluationContext
  ): Promise<RolePermissionResult> {
    
    for (const permission of role.permissions) {
      // Check resource match
      if (!this.matchesResource(permission.resource, resource)) {
        continue;
      }

      // Check action match
      if (!this.matchesAction(permission.actions, action)) {
        continue;
      }

      // Evaluate conditions
      if (permission.conditions) {
        const conditionsMatch = await this.evaluateConditions(
          permission.conditions,
          auth,
          context
        );
        
        if (!conditionsMatch) {
          continue;
        }
      }

      // Check scope restrictions
      if (permission.scope) {
        const scopeMatch = await this.evaluateScope(
          permission.scope,
          auth,
          context
        );
        
        if (!scopeMatch) {
          continue;
        }
      }

      return {
        granted: true,
        matchedPermission: permission,
        role: role.name
      };
    }

    return {
      granted: false,
      role: role.name
    };
  }

  private async evaluateConditions(
    conditions: Condition[],
    auth: AuthContext,
    context?: EvaluationContext
  ): Promise<boolean> {
    
    for (const condition of conditions) {
      const result = await this.evaluateCondition(condition, auth, context);
      if (!result) {
        return false;
      }
    }
    
    return true;
  }

  private async evaluateCondition(
    condition: Condition,
    auth: AuthContext,
    context?: EvaluationContext
  ): Promise<boolean> {
    
    // Resolve condition value
    let expectedValue = condition.value;
    if (typeof expectedValue === 'string' && expectedValue.includes('${')) {
      expectedValue = this.resolveValue(expectedValue, auth, context);
    }

    // Get actual value from context
    const actualValue = this.getFieldValue(condition.field, auth, context);

    // Evaluate based on operator
    switch (condition.operator) {
      case 'equals':
        return actualValue === expectedValue;
      
      case 'in':
        return Array.isArray(expectedValue) && 
               expectedValue.includes(actualValue);
      
      case 'regex':
        return new RegExp(expectedValue).test(String(actualValue));
      
      case 'range':
        return this.isInRange(actualValue, expectedValue);
      
      default:
        return false;
    }
  }

  private resolveValue(
    template: string,
    auth: AuthContext,
    context?: EvaluationContext
  ): any {
    
    return template.replace(/\$\{([^}]+)\}/g, (match, path) => {
      const parts = path.split('.');
      let value: any;
      
      if (parts[0] === 'auth') {
        value = this.getNestedValue(auth, parts.slice(1).join('.'));
      } else if (parts[0] === 'context' && context) {
        value = this.getNestedValue(context, parts.slice(1).join('.'));
      } else {
        value = this.getNestedValue({ auth, context }, path);
      }
      
      return value;
    });
  }
}
```

## Advanced Permission Features

### Resource-Based Permissions

```typescript
// Resource-specific permission patterns
class ResourcePermissionManager {
  async checkResourcePermission(
    auth: AuthContext,
    resourceType: string,
    resourceId: string,
    action: string
  ): Promise<ResourcePermissionResult> {
    
    // Get resource metadata
    const resource = await this.getResourceMetadata(resourceType, resourceId);
    
    // Check ownership-based permissions
    const ownershipResult = await this.checkOwnershipPermission(
      auth,
      resource,
      action
    );
    
    if (ownershipResult.granted) {
      return ownershipResult;
    }

    // Check role-based permissions
    const roleResult = await this.evaluatePermission(
      auth,
      `${resourceType}:${resourceId}`,
      action,
      { resource }
    );
    
    if (roleResult.granted) {
      return {
        granted: true,
        basis: 'role-based',
        role: roleResult.role,
        permission: roleResult.permission
      };
    }

    // Check group-based permissions
    const groupResult = await this.checkGroupPermission(
      auth,
      resource,
      action
    );
    
    return groupResult;
  }

  private async checkOwnershipPermission(
    auth: AuthContext,
    resource: ResourceMetadata,
    action: string
  ): Promise<ResourcePermissionResult> {
    
    // Check if user owns the resource
    if (resource.ownerId === auth.principalId) {
      const ownerPermissions = await this.getOwnerPermissions(resource.type);
      
      if (ownerPermissions.includes(action)) {
        return {
          granted: true,
          basis: 'ownership',
          resourceOwner: true
        };
      }
    }

    return { granted: false, basis: 'ownership' };
  }
}
```

### Delegation and Impersonation

```typescript
// Permission delegation system
class PermissionDelegationManager {
  async delegatePermission(
    delegatorId: string,
    delegateeId: string,
    permission: DelegationPermission,
    constraints: DelegationConstraints
  ): Promise<DelegationGrant> {
    
    // Verify delegator has permission to delegate
    const canDelegate = await this.checkDelegationAuthority(
      delegatorId,
      permission
    );
    
    if (!canDelegate) {
      throw new Error('Insufficient authority to delegate permission');
    }

    const delegation: DelegationGrant = {
      id: this.generateDelegationId(),
      delegatorId,
      delegateeId,
      permission,
      constraints: {
        ...constraints,
        createdAt: new Date(),
        expiresAt: constraints.expiresAt || 
                   new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours default
      },
      status: 'active'
    };

    await this.delegationStore.store(delegation);
    await this.auditLogger.logPermissionDelegation(delegation);
    
    return delegation;
  }

  async checkDelegatedPermission(
    delegateeId: string,
    resource: string,
    action: string
  ): Promise<DelegationPermissionResult> {
    
    const activeDelegations = await this.getActiveDelegations(delegateeId);
    
    for (const delegation of activeDelegations) {
      if (this.matchesDelegatedPermission(delegation.permission, resource, action)) {
        
        // Check constraints
        const constraintsSatisfied = await this.checkDelegationConstraints(
          delegation.constraints
        );
        
        if (constraintsSatisfied) {
          return {
            granted: true,
            delegation,
            delegatorId: delegation.delegatorId
          };
        }
      }
    }

    return { granted: false };
  }
}
```

### Time-Based Permissions

```typescript
// Time-based and temporary permissions
class TemporalPermissionManager {
  async createTemporaryPermission(
    principalId: string,
    permission: Permission,
    duration: number,
    justification: string
  ): Promise<TemporaryPermission> {
    
    const tempPermission: TemporaryPermission = {
      id: this.generateTempPermissionId(),
      principalId,
      permission,
      grantedAt: new Date(),
      expiresAt: new Date(Date.now() + duration),
      justification,
      status: 'active',
      usageCount: 0,
      maxUsage: permission.maxUsage || undefined
    };

    await this.tempPermissionStore.store(tempPermission);
    await this.auditLogger.logTemporaryPermissionGrant(tempPermission);
    
    // Schedule automatic revocation
    await this.scheduler.scheduleRevocation(
      tempPermission.id,
      tempPermission.expiresAt
    );
    
    return tempPermission;
  }

  async checkTemporaryPermission(
    principalId: string,
    resource: string,
    action: string
  ): Promise<TemporaryPermissionResult> {
    
    const tempPermissions = await this.getActiveTemporaryPermissions(principalId);
    
    for (const tempPerm of tempPermissions) {
      if (this.matchesPermission(tempPerm.permission, resource, action)) {
        
        // Check usage limits
        if (tempPerm.maxUsage && tempPerm.usageCount >= tempPerm.maxUsage) {
          continue;
        }

        // Check expiration
        if (new Date() > tempPerm.expiresAt) {
          await this.revokeTemporaryPermission(tempPerm.id, 'expired');
          continue;
        }

        // Increment usage count
        await this.incrementUsageCount(tempPerm.id);
        
        return {
          granted: true,
          temporaryPermission: tempPerm,
          remainingUsage: tempPerm.maxUsage ? 
                         tempPerm.maxUsage - tempPerm.usageCount - 1 : 
                         undefined
        };
      }
    }

    return { granted: false };
  }
}
```

## Performance Optimization

### Permission Caching

```typescript
// High-performance permission caching
class PermissionCache {
  private cache = new Map<string, CachedPermission>();
  private static readonly TTL = 5 * 60 * 1000; // 5 minutes

  async getPermission(
    principalId: string,
    resource: string,
    action: string
  ): Promise<PermissionResult | null> {
    
    const cacheKey = this.generateCacheKey(principalId, resource, action);
    const cached = this.cache.get(cacheKey);
    
    if (cached && !this.isExpired(cached)) {
      return cached.result;
    }

    return null;
  }

  async setPermission(
    principalId: string,
    resource: string,
    action: string,
    result: PermissionResult
  ): Promise<void> {
    
    const cacheKey = this.generateCacheKey(principalId, resource, action);
    
    this.cache.set(cacheKey, {
      result,
      cachedAt: new Date(),
      expiresAt: new Date(Date.now() + PermissionCache.TTL)
    });
  }

  async invalidate(principalId: string): Promise<void> {
    // Remove all cached permissions for principal
    for (const [key, cached] of this.cache.entries()) {
      if (key.startsWith(`${principalId}:`)) {
        this.cache.delete(key);
      }
    }
  }

  async invalidateResource(resource: string): Promise<void> {
    // Remove all cached permissions for resource
    for (const [key, cached] of this.cache.entries()) {
      if (key.includes(`:${resource}:`)) {
        this.cache.delete(key);
      }
    }
  }
}
```

### Bulk Permission Evaluation

```typescript
// Bulk permission checking for performance
class BulkPermissionEvaluator {
  async evaluateBulkPermissions(
    auth: AuthContext,
    requests: PermissionRequest[]
  ): Promise<BulkPermissionResult> {
    
    // Group requests by resource type for optimization
    const groupedRequests = this.groupRequestsByResourceType(requests);
    const results = new Map<string, PermissionResult>();
    
    for (const [resourceType, resourceRequests] of groupedRequests) {
      // Get relevant roles for this resource type
      const relevantRoles = await this.getRelevantRoles(auth.principalId, resourceType);
      
      // Evaluate permissions in batch
      const batchResults = await this.evaluateBatchForResourceType(
        resourceRequests,
        relevantRoles,
        auth
      );
      
      // Merge results
      for (const [requestId, result] of batchResults) {
        results.set(requestId, result);
      }
    }

    return {
      principalId: auth.principalId,
      evaluatedAt: new Date(),
      results: Object.fromEntries(results),
      totalRequests: requests.length,
      grantedCount: Array.from(results.values()).filter(r => r.granted).length
    };
  }
}
```

## Best Practices

### 1. Role Design

- **Least Privilege**: Grant minimum necessary permissions
- **Role Hierarchy**: Use inheritance to reduce complexity
- **Business Alignment**: Align roles with business functions

### 2. Permission Granularity

- **Resource-Based**: Define permissions by resource type
- **Action-Specific**: Use specific actions rather than broad permissions
- **Context-Aware**: Include contextual conditions where appropriate

### 3. Performance

- **Caching Strategy**: Implement intelligent permission caching
- **Bulk Operations**: Support bulk permission evaluation
- **Lazy Loading**: Load permissions on-demand

### 4. Security

- **Regular Audits**: Periodically review role assignments
- **Principle of Least Privilege**: Regularly remove unused permissions
- **Separation of Duties**: Ensure critical operations require multiple roles

## Integration Points

- **Authentication System**: Identity verification and session management
- **Multi-Tenancy**: Tenant-scoped roles and permissions
- **Audit System**: Comprehensive access logging
- **Agent Management**: Agent-specific permission assignment

---

*Permission system patterns derived from claude-code-flow enterprise RBAC authorizer and security policy implementation*