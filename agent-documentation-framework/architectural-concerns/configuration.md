# RUST-SS Configuration Architecture

## Overview

This document serves as the single source of truth for all configuration management patterns, environment variable handling, feature flag systems, and deployment strategies across the RUST-SS framework. It adopts a hierarchical, dynamic configuration approach with zero secrets in configuration files and runtime adaptability.

## Core Configuration Principles

### Hierarchical and Dynamic Configuration

```rust
#[derive(Debug, <PERSON>lone)]
pub enum ConfigurationPrinciple {
    HierarchicalOverrides,      // Layered config with clear precedence
    ZeroSecretsInConfig,        // Secrets fetched at runtime only
    TypeSafeConfiguration,      // Strongly typed config structs
    DynamicReloading,           // Runtime config updates without restart
    EnvironmentAwareness,       // Different configs per environment
    ValidationFirst,            // Validate all config before use
    AuditableChanges,          // Track all config modifications
}
```

## Configuration Management Framework

### Hierarchical Configuration with Figment

```rust
use figment::{Figment, Provider, Metadata, Error as FigmentError};
use figment::providers::{Env, <PERSON>at, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>m<PERSON>};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::RwLock;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RustSSConfig {
    pub agent: AgentConfig,
    pub runtime: RuntimeConfig,
    pub network: NetworkConfig,
    pub storage: StorageConfig,
    pub security: SecurityConfig,
    pub monitoring: MonitoringConfig,
    pub features: FeatureConfig,
    
    #[serde(skip)]
    pub secrets: SecretsConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AgentConfig {
    pub id: String,
    pub name: String,
    pub role: AgentRole,
    pub capabilities: Vec<String>,
    pub resource_limits: ResourceLimits,
    pub heartbeat_interval: std::time::Duration,
}

pub struct ConfigurationManager {
    config: Arc<RwLock<RustSSConfig>>,
    figment: Arc<RwLock<Figment>>,
    config_sources: Vec<ConfigSource>,
    reload_watcher: Option<ConfigWatcher>,
    validators: Vec<Box<dyn ConfigValidator>>,
}

impl ConfigurationManager {
    pub async fn load() -> Result<Self, ConfigError> {
        let environment = detect_environment();
        
        // Build configuration hierarchy
        let figment = Figment::new()
            // 1. Default values compiled into the binary
            .merge(Self::default_config())
            // 2. Base configuration file
            .merge(Toml::file("config/default.toml"))
            // 3. Environment-specific overrides
            .merge(Toml::file(format!("config/{}.toml", environment)).nested())
            // 4. Local overrides (not in version control)
            .merge(Toml::file("config/local.toml").nested())
            // 5. Environment variables with prefix
            .merge(Env::prefixed("RUST_SS_").split("__"))
            // 6. Command-line arguments (highest priority)
            .merge(Self::parse_cli_config());
        
        // Extract and validate configuration
        let mut config: RustSSConfig = figment
            .extract()
            .map_err(|e| ConfigError::ParseError(e.to_string()))?;
        
        // Load secrets from external sources
        config.secrets = Self::load_secrets(&config).await?;
        
        // Validate complete configuration
        Self::validate_config(&config)?;
        
        let manager = Self {
            config: Arc::new(RwLock::new(config)),
            figment: Arc::new(RwLock::new(figment)),
            config_sources: Self::enumerate_sources(),
            reload_watcher: None,
            validators: Self::create_validators(),
        };
        
        // Start configuration file watching if enabled
        if manager.is_hot_reload_enabled().await {
            manager.start_config_watcher().await?;
        }
        
        Ok(manager)
    }
    
    fn default_config() -> impl Provider {
        // Provide sensible defaults for all configuration
        figment::providers::Serialized::defaults(RustSSConfig {
            agent: AgentConfig {
                id: uuid::Uuid::new_v4().to_string(),
                name: "unnamed-agent".to_string(),
                role: AgentRole::Worker,
                capabilities: vec!["basic".to_string()],
                resource_limits: ResourceLimits::default(),
                heartbeat_interval: std::time::Duration::from_secs(30),
            },
            runtime: RuntimeConfig {
                worker_threads: num_cpus::get(),
                blocking_threads: num_cpus::get() * 2,
                max_concurrent_tasks: 10000,
                task_queue_size: 1000,
            },
            network: NetworkConfig {
                bind_address: "0.0.0.0:8080".to_string(),
                advertise_address: None,
                enable_tls: true,
                connection_timeout: std::time::Duration::from_secs(30),
                max_connections: 1000,
            },
            storage: StorageConfig {
                data_dir: PathBuf::from("/var/lib/rust-ss"),
                cache_size: 1024 * 1024 * 1024, // 1GB
                wal_enabled: true,
                compression: CompressionType::Zstd,
            },
            security: SecurityConfig {
                auth_enabled: true,
                tls_required: true,
                audit_logging: true,
                rate_limiting: true,
            },
            monitoring: MonitoringConfig {
                metrics_enabled: true,
                metrics_port: 9090,
                tracing_enabled: true,
                log_level: "info".to_string(),
            },
            features: FeatureConfig {
                experimental: false,
                beta_features: vec![],
            },
            secrets: SecretsConfig::default(),
        })
    }
    
    pub async fn get_config(&self) -> RustSSConfig {
        self.config.read().await.clone()
    }
    
    pub async fn update_config<F>(&self, updater: F) -> Result<(), ConfigError>
    where
        F: FnOnce(&mut RustSSConfig) -> Result<(), ConfigError>,
    {
        let mut config = self.config.write().await;
        
        // Create a backup of current config
        let backup = config.clone();
        
        // Apply the update
        if let Err(e) = updater(&mut *config) {
            // Restore backup on error
            *config = backup;
            return Err(e);
        }
        
        // Validate the new configuration
        if let Err(e) = Self::validate_config(&*config) {
            // Restore backup on validation failure
            *config = backup;
            return Err(ConfigError::ValidationFailed(e.to_string()));
        }
        
        // Emit configuration change event
        self.emit_config_change_event(&backup, &*config).await;
        
        Ok(())
    }
    
    async fn start_config_watcher(&self) -> Result<(), ConfigError> {
        use notify::{Watcher, RecursiveMode, Event};
        
        let (tx, mut rx) = tokio::sync::mpsc::channel(100);
        
        let mut watcher = notify::recommended_watcher(move |res: Result<Event, _>| {
            if let Ok(event) = res {
                let _ = tx.blocking_send(event);
            }
        }).map_err(|e| ConfigError::WatcherError(e.to_string()))?;
        
        // Watch configuration directories
        watcher.watch(Path::new("config"), RecursiveMode::Recursive)
            .map_err(|e| ConfigError::WatcherError(e.to_string()))?;
        
        let config_manager = self.clone();
        
        tokio::spawn(async move {
            while let Some(event) = rx.recv().await {
                if event.kind.is_modify() {
                    tracing::info!("Configuration file changed, reloading...");
                    
                    if let Err(e) = config_manager.reload().await {
                        tracing::error!("Failed to reload configuration: {}", e);
                    }
                }
            }
        });
        
        Ok(())
    }
    
    async fn reload(&self) -> Result<(), ConfigError> {
        let new_figment = self.build_figment();
        let new_config: RustSSConfig = new_figment
            .extract()
            .map_err(|e| ConfigError::ParseError(e.to_string()))?;
        
        // Validate before applying
        Self::validate_config(&new_config)?;
        
        // Update configuration atomically
        *self.config.write().await = new_config;
        *self.figment.write().await = new_figment;
        
        tracing::info!("Configuration reloaded successfully");
        Ok(())
    }
}
```

### Environment Variable Management

```rust
use std::env;
use std::collections::HashMap;
use regex::Regex;

pub struct EnvironmentManager {
    prefix: String,
    delimiter: String,
    type_hints: HashMap<String, EnvVarType>,
    validators: HashMap<String, Box<dyn EnvVarValidator>>,
}

#[derive(Debug, Clone)]
pub enum EnvVarType {
    String,
    Integer,
    Float,
    Boolean,
    Duration,
    Size,
    List(String), // delimiter
    Json,
}

impl EnvironmentManager {
    pub fn new(prefix: &str) -> Self {
        Self {
            prefix: prefix.to_string(),
            delimiter: "__".to_string(),
            type_hints: Self::default_type_hints(),
            validators: HashMap::new(),
        }
    }
    
    pub fn parse_env_vars(&self) -> Result<HashMap<String, serde_json::Value>, ConfigError> {
        let mut parsed = HashMap::new();
        let prefix_with_delimiter = format!("{}{}", self.prefix, self.delimiter);
        
        for (key, value) in env::vars() {
            if !key.starts_with(&prefix_with_delimiter) {
                continue;
            }
            
            let config_path = key[prefix_with_delimiter.len()..]
                .replace(&self.delimiter, ".")
                .to_lowercase();
            
            let parsed_value = self.parse_value(&config_path, &value)?;
            
            // Validate if validator exists
            if let Some(validator) = self.validators.get(&config_path) {
                validator.validate(&parsed_value)?;
            }
            
            parsed.insert(config_path, parsed_value);
        }
        
        Ok(parsed)
    }
    
    fn parse_value(&self, path: &str, value: &str) -> Result<serde_json::Value, ConfigError> {
        let var_type = self.type_hints.get(path)
            .cloned()
            .unwrap_or(EnvVarType::String);
        
        match var_type {
            EnvVarType::String => Ok(serde_json::Value::String(value.to_string())),
            
            EnvVarType::Integer => {
                value.parse::<i64>()
                    .map(serde_json::Value::from)
                    .map_err(|_| ConfigError::InvalidEnvVar(format!("{} must be an integer", path)))
            }
            
            EnvVarType::Float => {
                value.parse::<f64>()
                    .map(serde_json::Value::from)
                    .map_err(|_| ConfigError::InvalidEnvVar(format!("{} must be a float", path)))
            }
            
            EnvVarType::Boolean => {
                match value.to_lowercase().as_str() {
                    "true" | "1" | "yes" | "on" => Ok(serde_json::Value::Bool(true)),
                    "false" | "0" | "no" | "off" => Ok(serde_json::Value::Bool(false)),
                    _ => Err(ConfigError::InvalidEnvVar(format!("{} must be a boolean", path)))
                }
            }
            
            EnvVarType::Duration => {
                self.parse_duration(value)
                    .map(|d| serde_json::Value::String(format!("{}s", d.as_secs())))
                    .map_err(|_| ConfigError::InvalidEnvVar(format!("{} must be a duration", path)))
            }
            
            EnvVarType::Size => {
                self.parse_size(value)
                    .map(serde_json::Value::from)
                    .map_err(|_| ConfigError::InvalidEnvVar(format!("{} must be a size", path)))
            }
            
            EnvVarType::List(delimiter) => {
                let items: Vec<serde_json::Value> = value
                    .split(&delimiter)
                    .map(|s| serde_json::Value::String(s.trim().to_string()))
                    .collect();
                Ok(serde_json::Value::Array(items))
            }
            
            EnvVarType::Json => {
                serde_json::from_str(value)
                    .map_err(|_| ConfigError::InvalidEnvVar(format!("{} must be valid JSON", path)))
            }
        }
    }
    
    fn parse_duration(&self, value: &str) -> Result<std::time::Duration, ConfigError> {
        let re = Regex::new(r"^(\d+)(ms|s|m|h|d)$").unwrap();
        
        if let Some(captures) = re.captures(value) {
            let amount: u64 = captures[1].parse().unwrap();
            let unit = &captures[2];
            
            let duration = match unit {
                "ms" => std::time::Duration::from_millis(amount),
                "s" => std::time::Duration::from_secs(amount),
                "m" => std::time::Duration::from_secs(amount * 60),
                "h" => std::time::Duration::from_secs(amount * 3600),
                "d" => std::time::Duration::from_secs(amount * 86400),
                _ => return Err(ConfigError::InvalidDuration(value.to_string())),
            };
            
            Ok(duration)
        } else {
            Err(ConfigError::InvalidDuration(value.to_string()))
        }
    }
    
    fn parse_size(&self, value: &str) -> Result<u64, ConfigError> {
        let re = Regex::new(r"^(\d+)(B|KB|MB|GB|TB)$").unwrap();
        
        if let Some(captures) = re.captures(value) {
            let amount: u64 = captures[1].parse().unwrap();
            let unit = &captures[2];
            
            let size = match unit {
                "B" => amount,
                "KB" => amount * 1024,
                "MB" => amount * 1024 * 1024,
                "GB" => amount * 1024 * 1024 * 1024,
                "TB" => amount * 1024 * 1024 * 1024 * 1024,
                _ => return Err(ConfigError::InvalidSize(value.to_string())),
            };
            
            Ok(size)
        } else {
            Err(ConfigError::InvalidSize(value.to_string()))
        }
    }
}

pub trait EnvVarValidator: Send + Sync {
    fn validate(&self, value: &serde_json::Value) -> Result<(), ConfigError>;
}

pub struct RangeValidator<T> {
    min: Option<T>,
    max: Option<T>,
}

impl<T: PartialOrd + Copy> EnvVarValidator for RangeValidator<T>
where
    T: serde::de::DeserializeOwned,
{
    fn validate(&self, value: &serde_json::Value) -> Result<(), ConfigError> {
        let v: T = serde_json::from_value(value.clone())
            .map_err(|_| ConfigError::ValidationFailed("Type mismatch".to_string()))?;
        
        if let Some(min) = self.min {
            if v < min {
                return Err(ConfigError::ValidationFailed(
                    format!("Value below minimum")
                ));
            }
        }
        
        if let Some(max) = self.max {
            if v > max {
                return Err(ConfigError::ValidationFailed(
                    format!("Value above maximum")
                ));
            }
        }
        
        Ok(())
    }
}
```

## Feature Flag System

### Dynamic Feature Flags

```rust
use async_trait::async_trait;
use std::collections::HashMap;
use tokio::sync::RwLock;
use std::sync::Arc;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureFlag {
    pub name: String,
    pub enabled: bool,
    pub rollout_percentage: Option<f64>,
    pub targeting_rules: Vec<TargetingRule>,
    pub variants: HashMap<String, serde_json::Value>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TargetingRule {
    pub attribute: String,
    pub operator: TargetingOperator,
    pub values: Vec<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TargetingOperator {
    Equals,
    NotEquals,
    Contains,
    NotContains,
    GreaterThan,
    LessThan,
    In,
    NotIn,
    Regex,
}

#[async_trait]
pub trait FeatureFlagProvider: Send + Sync {
    async fn get_flags(&self) -> Result<Vec<FeatureFlag>, ConfigError>;
    async fn watch_changes(&self) -> Result<tokio::sync::mpsc::Receiver<FeatureFlagEvent>, ConfigError>;
}

pub struct FeatureFlagManager {
    provider: Arc<dyn FeatureFlagProvider>,
    flags: Arc<RwLock<HashMap<String, FeatureFlag>>>,
    evaluation_cache: Arc<DashMap<String, bool>>,
    metrics: Arc<FeatureFlagMetrics>,
}

impl FeatureFlagManager {
    pub async fn new(provider: Arc<dyn FeatureFlagProvider>) -> Result<Self, ConfigError> {
        let flags = provider.get_flags().await?;
        let flag_map = flags.into_iter()
            .map(|f| (f.name.clone(), f))
            .collect();
        
        let manager = Self {
            provider,
            flags: Arc::new(RwLock::new(flag_map)),
            evaluation_cache: Arc::new(DashMap::new()),
            metrics: Arc::new(FeatureFlagMetrics::new()),
        };
        
        manager.start_watching().await?;
        Ok(manager)
    }
    
    pub async fn is_enabled(&self, flag_name: &str, context: &EvaluationContext) -> bool {
        // Check cache first
        let cache_key = format!("{}:{}", flag_name, context.hash());
        if let Some(cached) = self.evaluation_cache.get(&cache_key) {
            self.metrics.record_cache_hit();
            return *cached;
        }
        
        let flags = self.flags.read().await;
        let result = if let Some(flag) = flags.get(flag_name) {
            self.evaluate_flag(flag, context).await
        } else {
            false
        };
        
        // Cache the result
        self.evaluation_cache.insert(cache_key, result);
        self.metrics.record_evaluation(flag_name, result);
        
        result
    }
    
    pub async fn get_variant(&self, flag_name: &str, context: &EvaluationContext) -> Option<serde_json::Value> {
        let flags = self.flags.read().await;
        let flag = flags.get(flag_name)?;
        
        if !self.evaluate_flag(flag, context).await {
            return None;
        }
        
        // Simple variant selection based on user ID hash
        let variant_key = self.select_variant(flag, context);
        flag.variants.get(&variant_key).cloned()
    }
    
    async fn evaluate_flag(&self, flag: &FeatureFlag, context: &EvaluationContext) -> bool {
        // Check if globally disabled
        if !flag.enabled {
            return false;
        }
        
        // Check targeting rules
        for rule in &flag.targeting_rules {
            if !self.evaluate_rule(rule, context) {
                return false;
            }
        }
        
        // Check rollout percentage
        if let Some(percentage) = flag.rollout_percentage {
            let hash = self.hash_context(flag, context);
            let bucket = (hash % 100) as f64;
            return bucket < percentage;
        }
        
        true
    }
    
    fn evaluate_rule(&self, rule: &TargetingRule, context: &EvaluationContext) -> bool {
        let attribute_value = match context.attributes.get(&rule.attribute) {
            Some(v) => v,
            None => return false,
        };
        
        match &rule.operator {
            TargetingOperator::Equals => {
                rule.values.contains(attribute_value)
            }
            TargetingOperator::NotEquals => {
                !rule.values.contains(attribute_value)
            }
            TargetingOperator::In => {
                rule.values.contains(attribute_value)
            }
            TargetingOperator::NotIn => {
                !rule.values.contains(attribute_value)
            }
            TargetingOperator::Regex => {
                if let (Some(pattern_str), Some(value_str)) = 
                    (rule.values.first().and_then(|v| v.as_str()), 
                     attribute_value.as_str()) {
                    if let Ok(re) = regex::Regex::new(pattern_str) {
                        return re.is_match(value_str);
                    }
                }
                false
            }
            _ => false, // Other operators implementation omitted for brevity
        }
    }
    
    async fn start_watching(&self) -> Result<(), ConfigError> {
        let mut receiver = self.provider.watch_changes().await?;
        let flags = self.flags.clone();
        let cache = self.evaluation_cache.clone();
        
        tokio::spawn(async move {
            while let Some(event) = receiver.recv().await {
                match event {
                    FeatureFlagEvent::FlagUpdated(flag) => {
                        flags.write().await.insert(flag.name.clone(), flag);
                        cache.clear(); // Invalidate cache
                    }
                    FeatureFlagEvent::FlagRemoved(name) => {
                        flags.write().await.remove(&name);
                        cache.clear();
                    }
                    FeatureFlagEvent::AllFlagsUpdated(new_flags) => {
                        let new_map = new_flags.into_iter()
                            .map(|f| (f.name.clone(), f))
                            .collect();
                        *flags.write().await = new_map;
                        cache.clear();
                    }
                }
            }
        });
        
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct EvaluationContext {
    pub agent_id: String,
    pub agent_role: String,
    pub environment: String,
    pub attributes: HashMap<String, serde_json::Value>,
}

impl EvaluationContext {
    pub fn hash(&self) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(&self.agent_id);
        hasher.update(&self.agent_role);
        hasher.update(&self.environment);
        hex::encode(hasher.finalize())
    }
}
```

## Secrets Management

### Runtime Secret Loading

```rust
use async_trait::async_trait;
use vaultrs::client::{VaultClient, VaultClientSettingsBuilder};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

#[derive(Debug, Clone, Default)]
pub struct SecretsConfig {
    pub database_credentials: Option<DatabaseCredentials>,
    pub api_keys: HashMap<String, String>,
    pub certificates: HashMap<String, Certificate>,
    pub encryption_keys: HashMap<String, Vec<u8>>,
}

#[derive(Debug, Clone)]
pub struct DatabaseCredentials {
    pub username: String,
    pub password: String,
    pub connection_string: String,
}

#[derive(Debug, Clone)]
pub struct Certificate {
    pub cert_pem: String,
    pub key_pem: String,
    pub ca_pem: Option<String>,
}

#[async_trait]
pub trait SecretProvider: Send + Sync {
    async fn get_secret(&self, path: &str) -> Result<serde_json::Value, ConfigError>;
    async fn list_secrets(&self, prefix: &str) -> Result<Vec<String>, ConfigError>;
}

pub struct SecretsManager {
    providers: HashMap<String, Arc<dyn SecretProvider>>,
    cache: Arc<RwLock<HashMap<String, CachedSecret>>>,
    config: SecretsManagerConfig,
}

#[derive(Debug, Clone)]
struct CachedSecret {
    value: serde_json::Value,
    expires_at: chrono::DateTime<chrono::Utc>,
    version: String,
}

impl SecretsManager {
    pub async fn new(config: SecretsManagerConfig) -> Result<Self, ConfigError> {
        let mut providers = HashMap::new();
        
        // Initialize Vault provider if configured
        if let Some(vault_config) = &config.vault {
            let vault_provider = VaultSecretProvider::new(vault_config).await?;
            providers.insert("vault".to_string(), Arc::new(vault_provider) as Arc<dyn SecretProvider>);
        }
        
        // Initialize other providers (AWS Secrets Manager, etc.)
        
        Ok(Self {
            providers,
            cache: Arc::new(RwLock::new(HashMap::new())),
            config,
        })
    }
    
    pub async fn get_secret(&self, secret_ref: &SecretReference) -> Result<serde_json::Value, ConfigError> {
        // Check cache first
        if let Some(cached) = self.get_cached_secret(&secret_ref.path).await {
            return Ok(cached);
        }
        
        // Get from provider
        let provider = self.providers.get(&secret_ref.provider)
            .ok_or_else(|| ConfigError::UnknownSecretProvider(secret_ref.provider.clone()))?;
        
        let secret = provider.get_secret(&secret_ref.path).await?;
        
        // Cache the secret
        self.cache_secret(&secret_ref.path, secret.clone()).await;
        
        Ok(secret)
    }
    
    pub async fn load_all_secrets(&self, config: &RustSSConfig) -> Result<SecretsConfig, ConfigError> {
        let mut secrets = SecretsConfig::default();
        
        // Load database credentials
        if let Some(db_ref) = &config.storage.database_credentials_ref {
            let creds_json = self.get_secret(db_ref).await?;
            secrets.database_credentials = Some(serde_json::from_value(creds_json)?);
        }
        
        // Load API keys
        for (name, key_ref) in &config.security.api_key_refs {
            let key_value = self.get_secret(key_ref).await?;
            if let Some(key_str) = key_value.as_str() {
                secrets.api_keys.insert(name.clone(), key_str.to_string());
            }
        }
        
        // Load certificates
        for (name, cert_ref) in &config.network.certificate_refs {
            let cert_json = self.get_secret(cert_ref).await?;
            let cert: Certificate = serde_json::from_value(cert_json)?;
            secrets.certificates.insert(name.clone(), cert);
        }
        
        Ok(secrets)
    }
    
    async fn get_cached_secret(&self, path: &str) -> Option<serde_json::Value> {
        let cache = self.cache.read().await;
        
        if let Some(cached) = cache.get(path) {
            if cached.expires_at > chrono::Utc::now() {
                return Some(cached.value.clone());
            }
        }
        
        None
    }
    
    async fn cache_secret(&self, path: &str, value: serde_json::Value) {
        let expires_at = chrono::Utc::now() + self.config.cache_ttl;
        
        let cached = CachedSecret {
            value,
            expires_at,
            version: uuid::Uuid::new_v4().to_string(),
        };
        
        self.cache.write().await.insert(path.to_string(), cached);
    }
}

#[derive(Debug, Clone, Deserialize)]
pub struct SecretReference {
    pub provider: String,
    pub path: String,
    pub version: Option<String>,
}

pub struct VaultSecretProvider {
    client: VaultClient,
    mount_path: String,
}

impl VaultSecretProvider {
    pub async fn new(config: &VaultConfig) -> Result<Self, ConfigError> {
        let client = VaultClient::new(
            VaultClientSettingsBuilder::default()
                .address(&config.address)
                .token(&config.token)
                .build()
                .map_err(|e| ConfigError::VaultError(e.to_string()))?
        ).map_err(|e| ConfigError::VaultError(e.to_string()))?;
        
        Ok(Self {
            client,
            mount_path: config.mount_path.clone(),
        })
    }
}

#[async_trait]
impl SecretProvider for VaultSecretProvider {
    async fn get_secret(&self, path: &str) -> Result<serde_json::Value, ConfigError> {
        let full_path = format!("{}/{}", self.mount_path, path);
        
        let secret = self.client
            .kv2()
            .read(&full_path)
            .await
            .map_err(|e| ConfigError::SecretFetchError(e.to_string()))?;
        
        Ok(serde_json::to_value(secret.data)?)
    }
    
    async fn list_secrets(&self, prefix: &str) -> Result<Vec<String>, ConfigError> {
        let full_path = format!("{}/{}", self.mount_path, prefix);
        
        let list = self.client
            .kv2()
            .list(&full_path)
            .await
            .map_err(|e| ConfigError::SecretFetchError(e.to_string()))?;
        
        Ok(list.keys)
    }
}
```

## Deployment Configuration

### Multi-Environment Support

```rust
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentConfig {
    pub environment: Environment,
    pub region: String,
    pub cluster: ClusterConfig,
    pub scaling: ScalingConfig,
    pub health_checks: HealthCheckConfig,
    pub rollout: RolloutConfig,
}

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum Environment {
    Development,
    Staging,
    Production,
    Testing,
    Custom(String),
}

impl Environment {
    pub fn from_str(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "dev" | "development" => Environment::Development,
            "staging" | "stage" => Environment::Staging,
            "prod" | "production" => Environment::Production,
            "test" | "testing" => Environment::Testing,
            custom => Environment::Custom(custom.to_string()),
        }
    }
    
    pub fn is_production(&self) -> bool {
        matches!(self, Environment::Production)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClusterConfig {
    pub name: String,
    pub namespace: String,
    pub service_mesh_enabled: bool,
    pub istio_config: Option<IstioConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingConfig {
    pub min_replicas: u32,
    pub max_replicas: u32,
    pub target_cpu_percent: f64,
    pub target_memory_percent: f64,
    pub scale_down_stabilization: std::time::Duration,
    pub scale_up_stabilization: std::time::Duration,
}

pub struct DeploymentManager {
    configs: HashMap<Environment, DeploymentConfig>,
    current_environment: Environment,
    validators: Vec<Box<dyn DeploymentValidator>>,
}

impl DeploymentManager {
    pub fn load(environment: Environment) -> Result<Self, ConfigError> {
        let mut configs = HashMap::new();
        
        // Load all environment configs
        for env in &[Environment::Development, Environment::Staging, Environment::Production] {
            let config_path = format!("deploy/{}.yaml", env.to_string().to_lowercase());
            if let Ok(config) = Self::load_deployment_config(&config_path) {
                configs.insert(*env, config);
            }
        }
        
        Ok(Self {
            configs,
            current_environment: environment,
            validators: Self::create_validators(),
        })
    }
    
    pub fn get_config(&self) -> Option<&DeploymentConfig> {
        self.configs.get(&self.current_environment)
    }
    
    pub fn validate_deployment(&self) -> Result<(), ConfigError> {
        let config = self.get_config()
            .ok_or(ConfigError::MissingDeploymentConfig)?;
        
        for validator in &self.validators {
            validator.validate(config)?;
        }
        
        Ok(())
    }
    
    pub fn generate_kubernetes_manifests(&self) -> Result<Vec<K8sManifest>, ConfigError> {
        let config = self.get_config()
            .ok_or(ConfigError::MissingDeploymentConfig)?;
        
        let mut manifests = Vec::new();
        
        // Generate Deployment
        manifests.push(self.generate_deployment(config)?);
        
        // Generate Service
        manifests.push(self.generate_service(config)?);
        
        // Generate ConfigMap
        manifests.push(self.generate_configmap(config)?);
        
        // Generate HPA if scaling is configured
        if config.scaling.max_replicas > config.scaling.min_replicas {
            manifests.push(self.generate_hpa(config)?);
        }
        
        // Generate Istio resources if service mesh is enabled
        if config.cluster.service_mesh_enabled {
            manifests.extend(self.generate_istio_resources(config)?);
        }
        
        Ok(manifests)
    }
}

pub trait DeploymentValidator: Send + Sync {
    fn validate(&self, config: &DeploymentConfig) -> Result<(), ConfigError>;
}

pub struct ProductionValidator;

impl DeploymentValidator for ProductionValidator {
    fn validate(&self, config: &DeploymentConfig) -> Result<(), ConfigError> {
        if config.environment.is_production() {
            // Enforce production requirements
            if config.scaling.min_replicas < 3 {
                return Err(ConfigError::ValidationFailed(
                    "Production requires at least 3 replicas".to_string()
                ));
            }
            
            if !config.cluster.service_mesh_enabled {
                return Err(ConfigError::ValidationFailed(
                    "Production requires service mesh".to_string()
                ));
            }
            
            if config.rollout.max_surge_percent > 50.0 {
                return Err(ConfigError::ValidationFailed(
                    "Production max surge cannot exceed 50%".to_string()
                ));
            }
        }
        
        Ok(())
    }
}
```

## Configuration Templates

### Complete Configuration Example

```json
{
  "configuration": {
    "sources": {
      "hierarchy": [
        "defaults",
        "config/default.toml",
        "config/${ENVIRONMENT}.toml",
        "config/local.toml",
        "environment_variables",
        "command_line_args"
      ],
      "environment_prefix": "RUST_SS",
      "environment_delimiter": "__",
      "hot_reload": true,
      "watch_interval_seconds": 10
    },
    "validation": {
      "strict_mode": true,
      "required_fields": [
        "agent.id",
        "agent.role",
        "network.bind_address"
      ],
      "type_checking": true,
      "range_validation": true
    },
    "secrets": {
      "providers": {
        "vault": {
          "address": "https://vault.rust-ss.local:8200",
          "mount_path": "secret/data/rust-ss",
          "auth_method": "kubernetes",
          "role": "rust-ss-agent"
        },
        "aws_secrets_manager": {
          "region": "us-west-2",
          "prefix": "rust-ss/"
        }
      },
      "cache_ttl_seconds": 300,
      "retry_attempts": 3,
      "retry_delay_ms": 1000
    },
    "feature_flags": {
      "provider": "unleash",
      "api_url": "https://unleash.rust-ss.local/api",
      "refresh_interval_seconds": 30,
      "cache_enabled": true,
      "default_enabled": false,
      "metrics_enabled": true
    },
    "deployment": {
      "environments": {
        "development": {
          "scaling": {
            "min_replicas": 1,
            "max_replicas": 3
          },
          "resource_limits": {
            "memory": "512Mi",
            "cpu": "500m"
          }
        },
        "staging": {
          "scaling": {
            "min_replicas": 2,
            "max_replicas": 5
          },
          "resource_limits": {
            "memory": "1Gi",
            "cpu": "1000m"
          }
        },
        "production": {
          "scaling": {
            "min_replicas": 3,
            "max_replicas": 20
          },
          "resource_limits": {
            "memory": "2Gi",
            "cpu": "2000m"
          },
          "pod_disruption_budget": {
            "min_available": 2
          }
        }
      }
    },
    "environment_variables": {
      "type_hints": {
        "runtime.worker_threads": "integer",
        "runtime.task_timeout": "duration",
        "storage.cache_size": "size",
        "features.enabled_features": "list",
        "monitoring.labels": "json"
      },
      "validators": {
        "runtime.worker_threads": {
          "type": "range",
          "min": 1,
          "max": 1024
        },
        "network.port": {
          "type": "range",
          "min": 1024,
          "max": 65535
        }
      }
    }
  }
}
```

### Environment-Specific Configurations

```toml
# config/production.toml
[agent]
heartbeat_interval = "30s"
max_retry_attempts = 5

[network]
enable_tls = true
tls_cert_ref = { provider = "vault", path = "certificates/production/agent" }
connection_timeout = "30s"
max_connections = 5000

[storage]
wal_enabled = true
compression = "zstd"
backup_enabled = true
backup_schedule = "0 2 * * *"

[security]
auth_enabled = true
audit_logging = true
rate_limiting = true
max_requests_per_minute = 1000

[monitoring]
metrics_enabled = true
tracing_enabled = true
log_level = "info"
alert_endpoints = ["pagerduty", "slack"]

[features]
experimental = false
beta_features = []
canary_percentage = 5.0
```

## Implementation Guidelines

### For Agents

1. **Load Configuration Hierarchically**:
   ```rust
   let config_manager = ConfigurationManager::load().await?;
   let config = config_manager.get_config().await;
   ```

2. **Use Environment Variables Correctly**:
   ```rust
   // RUST_SS__NETWORK__PORT=8080
   // RUST_SS__RUNTIME__WORKER_THREADS=16
   // RUST_SS__FEATURES__ENABLED_FEATURES=auth,metrics,tracing
   ```

3. **Never Store Secrets in Config**:
   ```rust
   let secrets_manager = SecretsManager::new(config.secrets).await?;
   let db_creds = secrets_manager.get_secret(&config.database_ref).await?;
   ```

4. **Check Feature Flags Dynamically**:
   ```rust
   let feature_manager = FeatureFlagManager::new(provider).await?;
   
   if feature_manager.is_enabled("new_algorithm", &context).await {
       use_new_algorithm();
   } else {
       use_legacy_algorithm();
   }
   ```

5. **Support Hot Reloading**:
   ```rust
   config_manager.watch_for_changes(|old, new| {
       tracing::info!("Configuration updated");
       reconfigure_services(new);
   }).await?;
   ```

6. **Validate All Configuration**:
   ```rust
   config_manager.update_config(|cfg| {
       cfg.network.port = 9000;
       Ok(())
   }).await?; // Automatically validated
   ```

7. **Use Deployment-Aware Config**:
   ```rust
   let deployment = DeploymentManager::load(environment)?;
   let k8s_manifests = deployment.generate_kubernetes_manifests()?;
   ```

### Configuration Best Practices

1. **Hierarchical Loading**: Layer configs from defaults to environment-specific
2. **Zero Secrets**: Never put secrets in config files or environment variables
3. **Type Safety**: Use strongly typed configuration structs
4. **Validation First**: Validate all config before use
5. **Dynamic Updates**: Support runtime configuration changes
6. **Feature Flags**: Use for gradual rollouts and A/B testing
7. **Environment Awareness**: Different configs for dev/staging/prod
8. **Audit Trail**: Log all configuration changes
9. **Fail Fast**: Exit early on invalid configuration
10. **Documentation**: Document all configuration options

## References

This document consolidates configuration patterns from:
- [12-Factor App](https://12factor.net/)
- [Figment Documentation](https://docs.rs/figment/)
- [HashiCorp Vault Best Practices](https://www.vaultproject.io/docs/concepts/policies)
- [Feature Flag Best Practices](https://docs.getunleash.io/topics/feature-flags/best-practices)

For implementation details, refer to:
- `figment` for hierarchical configuration
- `vaultrs` for secrets management
- `notify` for file watching
- `serde` for serialization/deserialization