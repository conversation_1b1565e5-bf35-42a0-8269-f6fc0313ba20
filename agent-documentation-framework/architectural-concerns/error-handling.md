# RUST-SS Error Handling Patterns

## Overview

This document serves as the single source of truth for all error handling patterns, strategies, and implementations across the RUST-SS framework. It consolidates scattered error handling documentation from MCP protocols, workflow operations, and external integrations into a unified reference.

## Core Error Classification

### Error Type Hierarchy

```rust
#[derive(Debug, Error)]
pub enum RustSSError {
    // JSON-RPC 2.0 Standard Errors
    #[error("Parse error: {0}")]
    ParseError(String),
    
    #[error("Invalid request: {0}")]
    InvalidRequest(String),
    
    #[error("Method not found: {0}")]
    MethodNotFound(String),
    
    #[error("Invalid params: {0}")]
    InvalidParams(String),
    
    #[error("Internal error: {0}")]
    InternalError(String),
    
    // MCP-Specific Errors
    #[error("Protocol error: {0}")]
    ProtocolError(#[from] ProtocolError),
    
    #[error("Authentication error: {0}")]
    AuthenticationError(#[from] AuthenticationError),
    
    #[error("Authorization error: {0}")]
    AuthorizationError(#[from] AuthorizationError),
    
    #[error("Tool execution error: {0}")]
    ToolExecutionError(#[from] ToolExecutionError),
    
    #[error("Resource error: {0}")]
    ResourceError(#[from] ResourceError),
    
    #[error("Transport error: {0}")]
    TransportError(#[from] TransportError),
    
    #[error("Session error: {0}")]
    SessionError(#[from] SessionError),
    
    #[error("Capability error: {0}")]
    CapabilityError(#[from] CapabilityError),
    
    // Workflow Errors
    #[error("Workflow execution error: {0}")]
    WorkflowError(#[from] WorkflowError),
    
    #[error("Task failure: {0}")]
    TaskError(#[from] TaskError),
    
    #[error("Coordination error: {0}")]
    CoordinationError(#[from] CoordinationError),
    
    #[error("State management error: {0}")]
    StateError(#[from] StateError),
    
    // Integration Errors
    #[error("External service error: {0}")]
    ExternalServiceError(#[from] ExternalServiceError),
    
    #[error("Integration error: {0}")]
    IntegrationError(#[from] IntegrationError),
    
    #[error("Data synchronization error: {0}")]
    SyncError(#[from] SyncError),
    
    // System Errors
    #[error("Configuration error: {0}")]
    ConfigurationError(String),
    
    #[error("Rate limit exceeded")]
    RateLimitExceeded,
    
    #[error("Timeout: operation took longer than {timeout:?}")]
    Timeout { timeout: std::time::Duration },
    
    #[error("Circuit breaker open")]
    CircuitBreakerOpen,
    
    #[error("Service unavailable: {reason}")]
    ServiceUnavailable { reason: String },
    
    #[error("Out of memory")]
    OutOfMemory,
    
    #[error("Disk full")]
    DiskFull,
    
    #[error("Database connection lost")]
    DatabaseConnectionLost,
    
    #[error("Service dependency failure: {service}")]
    ServiceDependencyFailure { service: String },
}
```

### Error Severity Classification

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum ErrorSeverity {
    Critical,   // System integrity at risk, immediate action required
    High,       // Significant functionality impacted, urgent attention needed
    Medium,     // Moderate impact, should be addressed soon
    Low,        // Minor impact, can be addressed in normal flow
}

impl ErrorSeverity {
    pub fn from_error(error: &RustSSError) -> Self {
        match error {
            RustSSError::InternalError(_) => ErrorSeverity::Critical,
            RustSSError::OutOfMemory => ErrorSeverity::Critical,
            RustSSError::DiskFull => ErrorSeverity::Critical,
            RustSSError::DatabaseConnectionLost => ErrorSeverity::Critical,
            RustSSError::ServiceUnavailable { .. } => ErrorSeverity::High,
            RustSSError::AuthenticationError(_) => ErrorSeverity::High,
            RustSSError::AuthorizationError(_) => ErrorSeverity::High,
            RustSSError::ToolExecutionError(ToolExecutionError::SecurityViolation(_)) => ErrorSeverity::High,
            RustSSError::ServiceDependencyFailure { .. } => ErrorSeverity::High,
            RustSSError::WorkflowError(_) => ErrorSeverity::Medium,
            RustSSError::TaskError(_) => ErrorSeverity::Medium,
            RustSSError::RateLimitExceeded => ErrorSeverity::Medium,
            RustSSError::ToolExecutionError(_) => ErrorSeverity::Medium,
            RustSSError::ExternalServiceError(_) => ErrorSeverity::Medium,
            RustSSError::InvalidParams(_) => ErrorSeverity::Low,
            RustSSError::MethodNotFound(_) => ErrorSeverity::Low,
            _ => ErrorSeverity::Medium,
        }
    }
}
```

### Error Recoverability Classification

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum ErrorRecoverability {
    Transient,      // Temporary failures that may resolve with retry
    Permanent,      // Errors that won't resolve with retries
    SystemLevel,    // Infrastructure-level errors requiring special handling
}

impl ErrorRecoverability {
    pub fn from_error(error: &RustSSError) -> Self {
        match error {
            // Transient errors - retry may succeed
            RustSSError::TransportError(_) => ErrorRecoverability::Transient,
            RustSSError::Timeout { .. } => ErrorRecoverability::Transient,
            RustSSError::ServiceUnavailable { .. } => ErrorRecoverability::Transient,
            RustSSError::RateLimitExceeded => ErrorRecoverability::Transient,
            RustSSError::CircuitBreakerOpen => ErrorRecoverability::Transient,
            
            // Permanent errors - retry will fail
            RustSSError::AuthenticationError(_) => ErrorRecoverability::Permanent,
            RustSSError::AuthorizationError(_) => ErrorRecoverability::Permanent,
            RustSSError::InvalidParams(_) => ErrorRecoverability::Permanent,
            RustSSError::MethodNotFound(_) => ErrorRecoverability::Permanent,
            RustSSError::ParseError(_) => ErrorRecoverability::Permanent,
            RustSSError::InvalidRequest(_) => ErrorRecoverability::Permanent,
            
            // System-level errors - require special handling
            RustSSError::OutOfMemory => ErrorRecoverability::SystemLevel,
            RustSSError::DiskFull => ErrorRecoverability::SystemLevel,
            RustSSError::DatabaseConnectionLost => ErrorRecoverability::SystemLevel,
            RustSSError::ConfigurationError(_) => ErrorRecoverability::SystemLevel,
            
            // Default to transient for unknown errors
            _ => ErrorRecoverability::Transient,
        }
    }
}
```

## Circuit Breaker Pattern

### Unified Circuit Breaker Implementation

```rust
#[derive(Debug, Clone)]
pub enum CircuitState {
    Closed,
    Open { opened_at: std::time::Instant },
    HalfOpen { test_request_count: usize },
}

#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    pub failure_threshold: f64,           // Failure rate to open circuit (0.0-1.0)
    pub success_threshold: usize,         // Consecutive successes to close from half-open
    pub timeout: std::time::Duration,     // How long to stay open before half-open
    pub min_request_count: usize,         // Minimum requests before considering failure rate
    pub half_open_max_requests: usize,    // Max requests to allow in half-open state
    pub volume_threshold: usize,          // Minimum volume for statistical significance
}

pub struct UnifiedCircuitBreaker {
    state: Arc<RwLock<CircuitState>>,
    config: CircuitBreakerConfig,
    metrics: Arc<RwLock<CircuitBreakerMetrics>>,
    event_bus: Arc<EventBus>,
}

impl UnifiedCircuitBreaker {
    pub async fn call<F, T, E>(&self, operation: F) -> Result<T, CircuitBreakerError<E>>
    where
        F: Future<Output = Result<T, E>>,
        E: std::error::Error + Send + Sync + 'static,
    {
        // Check if we can execute the operation
        if !self.can_execute().await {
            return Err(CircuitBreakerError::CircuitOpen);
        }
        
        let start_time = std::time::Instant::now();
        
        match operation.await {
            Ok(result) => {
                self.record_success(start_time.elapsed()).await;
                Ok(result)
            }
            Err(error) => {
                self.record_failure(start_time.elapsed()).await;
                Err(CircuitBreakerError::OperationFailed(error))
            }
        }
    }
    
    async fn can_execute(&self) -> bool {
        let state = self.state.read().await;
        
        match *state {
            CircuitState::Closed => true,
            CircuitState::Open { opened_at } => {
                if opened_at.elapsed() > self.config.timeout {
                    // Transition to half-open
                    drop(state);
                    *self.state.write().await = CircuitState::HalfOpen { test_request_count: 0 };
                    self.emit_state_change(CircuitState::HalfOpen { test_request_count: 0 }).await;
                    true
                } else {
                    false
                }
            }
            CircuitState::HalfOpen { test_request_count } => {
                test_request_count < self.config.half_open_max_requests
            }
        }
    }
    
    async fn record_success(&self, duration: std::time::Duration) {
        let mut metrics = self.metrics.write().await;
        metrics.total_requests += 1;
        metrics.consecutive_successes += 1;
        metrics.consecutive_failures = 0;
        
        let mut state = self.state.write().await;
        match *state {
            CircuitState::HalfOpen { test_request_count } => {
                if metrics.consecutive_successes >= self.config.success_threshold {
                    *state = CircuitState::Closed;
                    self.emit_state_change(CircuitState::Closed).await;
                } else {
                    *state = CircuitState::HalfOpen { 
                        test_request_count: test_request_count + 1 
                    };
                }
            }
            _ => {} // Stay closed
        }
    }
    
    async fn record_failure(&self, duration: std::time::Duration) {
        let mut metrics = self.metrics.write().await;
        metrics.total_requests += 1;
        metrics.failed_requests += 1;
        metrics.consecutive_failures += 1;
        metrics.consecutive_successes = 0;
        metrics.last_failure_time = Some(std::time::Instant::now());
        
        let mut state = self.state.write().await;
        
        // Check if we should open the circuit
        let failure_rate = metrics.failed_requests as f64 / metrics.total_requests as f64;
        let should_open = metrics.total_requests >= self.config.min_request_count
            && failure_rate >= self.config.failure_threshold;
        
        if should_open && !matches!(*state, CircuitState::Open { .. }) {
            *state = CircuitState::Open { opened_at: std::time::Instant::now() };
            self.emit_state_change(CircuitState::Open { 
                opened_at: std::time::Instant::now() 
            }).await;
        }
    }
}
```

## Retry Strategies

### Exponential Backoff with Jitter

```rust
#[derive(Debug, Clone)]
pub struct RetryConfig {
    pub max_attempts: usize,
    pub base_delay: std::time::Duration,
    pub max_delay: std::time::Duration,
    pub backoff_multiplier: f64,
    pub jitter: bool,
    pub retryable_errors: Vec<ErrorRecoverability>,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay: std::time::Duration::from_millis(100),
            max_delay: std::time::Duration::from_secs(30),
            backoff_multiplier: 2.0,
            jitter: true,
            retryable_errors: vec![ErrorRecoverability::Transient],
        }
    }
}

pub struct RetryManager {
    config: RetryConfig,
}

impl RetryManager {
    pub async fn execute_with_retry<F, T, E>(&self, mut operation: F) -> Result<T, E>
    where
        F: FnMut() -> Pin<Box<dyn Future<Output = Result<T, E>> + Send>>,
        E: Into<RustSSError>,
    {
        let mut last_error: Option<E> = None;
        
        for attempt in 1..=self.config.max_attempts {
            match operation().await {
                Ok(result) => {
                    if attempt > 1 {
                        tracing::info!(
                            attempt = attempt,
                            "Operation succeeded after retry"
                        );
                    }
                    return Ok(result);
                }
                Err(error) => {
                    let rust_ss_error = error.into();
                    let recoverability = ErrorRecoverability::from_error(&rust_ss_error);
                    
                    // Check if error is retryable
                    if !self.config.retryable_errors.contains(&recoverability) {
                        return Err(rust_ss_error.into());
                    }
                    
                    last_error = Some(rust_ss_error.into());
                    
                    // Don't delay after the last attempt
                    if attempt < self.config.max_attempts {
                        let delay = self.calculate_delay(attempt);
                        tracing::warn!(
                            attempt = attempt,
                            delay_ms = delay.as_millis(),
                            error = %rust_ss_error,
                            "Operation failed, retrying after delay"
                        );
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap())
    }
    
    fn calculate_delay(&self, attempt: usize) -> std::time::Duration {
        let delay_ms = self.config.base_delay.as_millis() as f64 
            * self.config.backoff_multiplier.powi((attempt - 1) as i32);
        
        let delay_ms = delay_ms.min(self.config.max_delay.as_millis() as f64);
        
        let final_delay_ms = if self.config.jitter {
            let jitter_range = delay_ms * 0.1; // 10% jitter
            let jitter = (rand::random::<f64>() - 0.5) * 2.0 * jitter_range;
            delay_ms + jitter
        } else {
            delay_ms
        };
        
        std::time::Duration::from_millis(final_delay_ms.max(0.0) as u64)
    }
}
```

### Adaptive Retry Policy

```rust
pub struct AdaptiveRetryManager {
    base_config: RetryConfig,
    success_rate: Arc<AtomicU64>, // Fixed-point representation (0-100000)
    avg_response_time: Arc<AtomicU64>, // Milliseconds
    recent_outcomes: Arc<Mutex<VecDeque<bool>>>,
    window_size: usize,
}

impl AdaptiveRetryManager {
    pub fn new(base_config: RetryConfig) -> Self {
        Self {
            base_config,
            success_rate: Arc::new(AtomicU64::new(100000)), // 100% initially
            avg_response_time: Arc::new(AtomicU64::new(0)),
            recent_outcomes: Arc::new(Mutex::new(VecDeque::new())),
            window_size: 100,
        }
    }
    
    pub async fn execute_with_adaptive_retry<F, T, E>(&self, operation: F) -> Result<T, E>
    where
        F: Fn() -> Pin<Box<dyn Future<Output = Result<T, E>> + Send>>,
        E: Into<RustSSError>,
    {
        let adapted_config = self.adapt_configuration().await;
        let retry_manager = RetryManager { config: adapted_config };
        
        let start_time = std::time::Instant::now();
        
        match retry_manager.execute_with_retry(|| operation()).await {
            Ok(result) => {
                self.record_success(start_time.elapsed()).await;
                Ok(result)
            }
            Err(error) => {
                self.record_failure().await;
                Err(error)
            }
        }
    }
    
    async fn adapt_configuration(&self) -> RetryConfig {
        let mut config = self.base_config.clone();
        
        let success_rate = self.success_rate.load(Ordering::Relaxed) as f64 / 100000.0;
        let avg_response_time = self.avg_response_time.load(Ordering::Relaxed);
        
        // Reduce retries if success rate is low
        if success_rate < 0.5 {
            config.max_attempts = std::cmp::max(1, config.max_attempts / 2);
        }
        
        // Increase delays if system is slow
        if avg_response_time > 5000 { // 5 seconds
            config.base_delay = config.base_delay * 2;
            config.max_delay = config.max_delay * 2;
        }
        
        config
    }
    
    async fn record_success(&self, response_time: std::time::Duration) {
        let mut outcomes = self.recent_outcomes.lock().await;
        outcomes.push_back(true);
        
        if outcomes.len() > self.window_size {
            outcomes.pop_front();
        }
        
        let successes = outcomes.iter().filter(|&&success| success).count();
        let success_rate = (successes as f64 / outcomes.len() as f64 * 100000.0) as u64;
        self.success_rate.store(success_rate, Ordering::Relaxed);
        
        let current_avg = self.avg_response_time.load(Ordering::Relaxed);
        let new_avg = (current_avg + response_time.as_millis() as u64) / 2;
        self.avg_response_time.store(new_avg, Ordering::Relaxed);
    }
    
    async fn record_failure(&self) {
        let mut outcomes = self.recent_outcomes.lock().await;
        outcomes.push_back(false);
        
        if outcomes.len() > self.window_size {
            outcomes.pop_front();
        }
        
        let successes = outcomes.iter().filter(|&&success| success).count();
        let success_rate = (successes as f64 / outcomes.len() as f64 * 100000.0) as u64;
        self.success_rate.store(success_rate, Ordering::Relaxed);
    }
}
```

## Recovery Strategies

### Fallback Mechanisms

```rust
#[async_trait]
pub trait FallbackStrategy<T>: Send + Sync {
    async fn can_handle(&self, error: &RustSSError) -> bool;
    async fn execute(&self, context: &OperationContext) -> Result<T, RustSSError>;
    fn get_priority(&self) -> u8; // Lower number = higher priority
}

pub struct CachedResponseFallback<T> {
    cache: Arc<dyn Cache<T>>,
    ttl: std::time::Duration,
}

#[async_trait]
impl<T> FallbackStrategy<T> for CachedResponseFallback<T>
where
    T: Clone + Send + Sync + 'static,
{
    async fn can_handle(&self, error: &RustSSError) -> bool {
        matches!(
            error,
            RustSSError::ServiceUnavailable { .. } |
            RustSSError::Timeout { .. } |
            RustSSError::ExternalServiceError(_)
        )
    }
    
    async fn execute(&self, context: &OperationContext) -> Result<T, RustSSError> {
        let cache_key = context.generate_cache_key();
        
        if let Some(cached_value) = self.cache.get(&cache_key).await {
            tracing::info!(
                cache_key = %cache_key,
                "Using cached response as fallback"
            );
            return Ok(cached_value);
        }
        
        Err(RustSSError::ServiceUnavailable {
            reason: "No cached value available for fallback".to_string()
        })
    }
    
    fn get_priority(&self) -> u8 {
        1 // High priority
    }
}

pub struct DefaultValueFallback<T> {
    default_value: T,
}

#[async_trait]
impl<T> FallbackStrategy<T> for DefaultValueFallback<T>
where
    T: Clone + Send + Sync + 'static,
{
    async fn can_handle(&self, _error: &RustSSError) -> bool {
        true // Can handle any error as last resort
    }
    
    async fn execute(&self, _context: &OperationContext) -> Result<T, RustSSError> {
        tracing::warn!("Using default value as fallback");
        Ok(self.default_value.clone())
    }
    
    fn get_priority(&self) -> u8 {
        255 // Lowest priority - last resort
    }
}

pub struct FallbackManager<T> {
    strategies: Vec<Box<dyn FallbackStrategy<T>>>,
}

impl<T> FallbackManager<T>
where
    T: Clone + Send + Sync + 'static,
{
    pub fn new() -> Self {
        Self {
            strategies: Vec::new(),
        }
    }
    
    pub fn register_strategy(&mut self, strategy: Box<dyn FallbackStrategy<T>>) {
        self.strategies.push(strategy);
        self.strategies.sort_by_key(|s| s.get_priority());
    }
    
    pub async fn execute_fallback(
        &self,
        error: &RustSSError,
        context: &OperationContext,
    ) -> Result<T, RustSSError> {
        for strategy in &self.strategies {
            if strategy.can_handle(error).await {
                match strategy.execute(context).await {
                    Ok(result) => {
                        tracing::info!(
                            strategy = strategy.get_priority(),
                            "Fallback strategy succeeded"
                        );
                        return Ok(result);
                    }
                    Err(fallback_error) => {
                        tracing::warn!(
                            strategy = strategy.get_priority(),
                            error = %fallback_error,
                            "Fallback strategy failed, trying next"
                        );
                    }
                }
            }
        }
        
        Err(RustSSError::InternalError(
            "All fallback strategies failed".to_string()
        ))
    }
}
```

## Rollback and Recovery

### Checkpoint-Based Rollback

```rust
#[derive(Debug, Clone)]
pub struct Checkpoint {
    pub id: String,
    pub workflow_id: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub state: serde_json::Value,
    pub metadata: std::collections::HashMap<String, serde_json::Value>,
    pub version: String,
}

pub struct RollbackManager {
    checkpoints: Arc<RwLock<std::collections::HashMap<String, Checkpoint>>>,
    state_manager: Arc<dyn StateManager>,
    rollback_strategies: std::collections::HashMap<String, Box<dyn RollbackStrategy>>,
}

#[async_trait]
pub trait RollbackStrategy: Send + Sync {
    async fn execute(
        &self,
        checkpoint: &Checkpoint,
        options: &RollbackOptions,
    ) -> Result<RollbackResult, RustSSError>;
    
    fn can_handle(&self, error: &RustSSError) -> bool;
}

#[derive(Debug, Clone)]
pub struct RollbackOptions {
    pub strategy: Option<String>,
    pub preserve_data: bool,
    pub notify_stakeholders: bool,
    pub reason: Option<String>,
}

#[derive(Debug)]
pub enum RollbackResult {
    Success {
        checkpoint_id: String,
        restored_state: serde_json::Value,
        actions_taken: Vec<String>,
    },
    PartialSuccess {
        checkpoint_id: String,
        warnings: Vec<String>,
    },
    Failed {
        reason: String,
        partial_state: Option<serde_json::Value>,
    },
}

impl RollbackManager {
    pub async fn create_checkpoint(
        &self,
        workflow_id: String,
        checkpoint_id: String,
        metadata: Option<std::collections::HashMap<String, serde_json::Value>>,
    ) -> Result<(), RustSSError> {
        let state = self.state_manager.capture_state(&workflow_id).await?;
        
        let checkpoint = Checkpoint {
            id: checkpoint_id.clone(),
            workflow_id: workflow_id.clone(),
            created_at: chrono::Utc::now(),
            state,
            metadata: metadata.unwrap_or_default(),
            version: self.generate_version(),
        };
        
        self.checkpoints.write().await.insert(
            format!("{}:{}", workflow_id, checkpoint_id),
            checkpoint.clone(),
        );
        
        self.state_manager.persist_checkpoint(&checkpoint).await?;
        
        tracing::info!(
            workflow_id = %workflow_id,
            checkpoint_id = %checkpoint_id,
            "Checkpoint created successfully"
        );
        
        Ok(())
    }
    
    pub async fn rollback_to_checkpoint(
        &self,
        workflow_id: String,
        checkpoint_id: String,
        options: RollbackOptions,
    ) -> Result<RollbackResult, RustSSError> {
        let checkpoint_key = format!("{}:{}", workflow_id, checkpoint_id);
        let checkpoint = self.checkpoints.read().await
            .get(&checkpoint_key)
            .cloned()
            .ok_or_else(|| RustSSError::InvalidRequest(
                format!("Checkpoint '{}' not found", checkpoint_id)
            ))?;
        
        let strategy_name = options.strategy.as_deref().unwrap_or("default");
        let strategy = self.rollback_strategies.get(strategy_name)
            .ok_or_else(|| RustSSError::ConfigurationError(
                format!("Rollback strategy '{}' not found", strategy_name)
            ))?;
        
        tracing::info!(
            workflow_id = %workflow_id,
            checkpoint_id = %checkpoint_id,
            strategy = %strategy_name,
            reason = ?options.reason,
            "Starting rollback operation"
        );
        
        match strategy.execute(&checkpoint, &options).await {
            Ok(result) => {
                self.state_manager.restore_state(&workflow_id, &checkpoint.state).await?;
                
                if options.notify_stakeholders {
                    self.notify_stakeholders(&workflow_id, &checkpoint_id, &result).await;
                }
                
                tracing::info!(
                    workflow_id = %workflow_id,
                    checkpoint_id = %checkpoint_id,
                    "Rollback completed successfully"
                );
                
                Ok(result)
            }
            Err(error) => {
                tracing::error!(
                    workflow_id = %workflow_id,
                    checkpoint_id = %checkpoint_id,
                    error = %error,
                    "Rollback operation failed"
                );
                Err(error)
            }
        }
    }
    
    pub async fn auto_rollback_on_failure(
        &self,
        workflow_id: String,
        error: &RustSSError,
    ) -> Result<Option<RollbackResult>, RustSSError> {
        let triggers = self.get_rollback_triggers(&workflow_id).await?;
        
        for trigger in triggers {
            if self.should_trigger_rollback(error, &trigger) {
                if let Some(checkpoint) = self.find_suitable_checkpoint(&workflow_id, &trigger).await? {
                    let options = RollbackOptions {
                        strategy: Some(trigger.strategy),
                        preserve_data: trigger.preserve_data,
                        notify_stakeholders: true,
                        reason: Some(format!("Auto-rollback triggered by: {}", error)),
                    };
                    
                    match self.rollback_to_checkpoint(workflow_id, checkpoint.id, options).await {
                        Ok(result) => return Ok(Some(result)),
                        Err(rollback_error) => {
                            tracing::error!(
                                workflow_id = %workflow_id,
                                original_error = %error,
                                rollback_error = %rollback_error,
                                "Auto-rollback failed"
                            );
                        }
                    }
                }
            }
        }
        
        Ok(None)
    }
}
```

## Error Context and Observability

### Error Context Management

```rust
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub error_id: String,
    pub session_id: Option<String>,
    pub request_id: Option<String>,
    pub user_id: Option<String>,
    pub operation: Option<String>,
    pub component: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub metadata: std::collections::HashMap<String, serde_json::Value>,
    pub stack_trace: Option<String>,
    pub correlation_id: Option<String>,
}

impl ErrorContext {
    pub fn new(component: &str) -> Self {
        Self {
            error_id: uuid::Uuid::new_v4().to_string(),
            session_id: None,
            request_id: None,
            user_id: None,
            operation: None,
            component: component.to_string(),
            timestamp: chrono::Utc::now(),
            metadata: std::collections::HashMap::new(),
            stack_trace: None,
            correlation_id: None,
        }
    }
    
    pub fn with_session_id(mut self, session_id: String) -> Self {
        self.session_id = Some(session_id);
        self
    }
    
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
    
    pub fn with_operation(mut self, operation: String) -> Self {
        self.operation = Some(operation);
        self
    }
    
    pub fn with_metadata(mut self, key: String, value: serde_json::Value) -> Self {
        self.metadata.insert(key, value);
        self
    }
}
```

### Comprehensive Error Manager

```rust
pub struct ErrorManager {
    error_store: Arc<dyn ErrorStore>,
    recovery_framework: ErrorRecoveryFramework,
    circuit_breakers: std::collections::HashMap<String, UnifiedCircuitBreaker>,
    fallback_managers: std::collections::HashMap<String, Box<dyn std::any::Any + Send + Sync>>,
    rollback_manager: RollbackManager,
    notification_service: Arc<dyn NotificationService>,
    metrics: Arc<ErrorMetrics>,
}

impl ErrorManager {
    pub async fn handle_error(
        &self,
        error: RustSSError,
        context: ErrorContext,
    ) -> Result<ErrorHandlingResult, RustSSError> {
        // Store error for analysis and debugging
        self.error_store.store_error(&error, &context).await?;
        
        // Update metrics
        self.metrics.record_error(&error, &context).await;
        
        // Log error with appropriate level
        self.log_error(&error, &context).await;
        
        // Classify error and determine handling strategy
        let severity = ErrorSeverity::from_error(&error);
        let recoverability = ErrorRecoverability::from_error(&error);
        
        // Handle critical errors immediately
        if severity == ErrorSeverity::Critical {
            return self.handle_critical_error(error, context).await;
        }
        
        // Attempt recovery based on error type
        match recoverability {
            ErrorRecoverability::Transient => {
                self.handle_transient_error(error, context).await
            }
            ErrorRecoverability::Permanent => {
                self.handle_permanent_error(error, context).await
            }
            ErrorRecoverability::SystemLevel => {
                self.handle_system_error(error, context).await
            }
        }
    }
    
    async fn handle_critical_error(
        &self,
        error: RustSSError,
        context: ErrorContext,
    ) -> Result<ErrorHandlingResult, RustSSError> {
        // Immediate alerting
        self.notification_service.send_critical_alert(&error, &context).await?;
        
        // Attempt automatic rollback if configured
        if let Some(workflow_id) = context.metadata.get("workflow_id")
            .and_then(|v| v.as_str()) {
            if let Ok(Some(rollback_result)) = self.rollback_manager
                .auto_rollback_on_failure(workflow_id.to_string(), &error).await {
                return Ok(ErrorHandlingResult {
                    error_id: context.error_id,
                    handled: true,
                    recovery_action: Some(RecoveryAction::Rollback(rollback_result)),
                    should_retry: false,
                    retry_delay: None,
                });
            }
        }
        
        Ok(ErrorHandlingResult {
            error_id: context.error_id,
            handled: false,
            recovery_action: None,
            should_retry: false,
            retry_delay: None,
        })
    }
    
    async fn log_error(&self, error: &RustSSError, context: &ErrorContext) {
        let severity = ErrorSeverity::from_error(error);
        
        match severity {
            ErrorSeverity::Critical => {
                tracing::error!(
                    error_id = %context.error_id,
                    component = %context.component,
                    session_id = ?context.session_id,
                    operation = ?context.operation,
                    error = %error,
                    "Critical error occurred"
                );
            }
            ErrorSeverity::High => {
                tracing::error!(
                    error_id = %context.error_id,
                    component = %context.component,
                    error = %error,
                    "High severity error"
                );
            }
            ErrorSeverity::Medium => {
                tracing::warn!(
                    error_id = %context.error_id,
                    component = %context.component,
                    error = %error,
                    "Medium severity error"
                );
            }
            ErrorSeverity::Low => {
                tracing::info!(
                    error_id = %context.error_id,
                    component = %context.component,
                    error = %error,
                    "Low severity error"
                );
            }
        }
    }
}
```

## Configuration Templates

### Error Handling Configuration

```json
{
  "error_handling": {
    "global_strategy": "continue_on_non_critical",
    "circuit_breaker": {
      "enabled": true,
      "default_config": {
        "failure_threshold": 0.5,
        "success_threshold": 3,
        "timeout_ms": 60000,
        "min_request_count": 10,
        "half_open_max_requests": 3,
        "volume_threshold": 20
      },
      "service_specific": {
        "mcp_protocol": {
          "failure_threshold": 0.3,
          "timeout_ms": 30000
        },
        "external_integrations": {
          "failure_threshold": 0.6,
          "timeout_ms": 120000
        }
      }
    },
    "retry_policy": {
      "default": {
        "max_attempts": 3,
        "base_delay_ms": 100,
        "max_delay_ms": 30000,
        "backoff_multiplier": 2.0,
        "jitter": true
      },
      "aggressive": {
        "max_attempts": 5,
        "base_delay_ms": 50,
        "backoff_multiplier": 1.5
      },
      "conservative": {
        "max_attempts": 2,
        "base_delay_ms": 1000,
        "backoff_multiplier": 3.0
      }
    },
    "rollback": {
      "enabled": true,
      "auto_triggers": [
        {
          "error_types": ["OutOfMemory", "DiskFull", "DatabaseConnectionLost"],
          "strategy": "immediate_rollback",
          "preserve_data": false
        },
        {
          "error_types": ["ServiceDependencyFailure"],
          "strategy": "graceful_rollback",
          "preserve_data": true
        }
      ],
      "checkpoint_frequency": "per_stage",
      "retention_period": "7d"
    },
    "fallback": {
      "enabled": true,
      "strategies": [
        {
          "type": "cached_response",
          "priority": 1,
          "ttl_hours": 24
        },
        {
          "type": "default_value",
          "priority": 255
        }
      ]
    },
    "notifications": {
      "critical_errors": {
        "channels": ["slack", "email", "pagerduty"],
        "escalation_delay_minutes": 5
      },
      "high_errors": {
        "channels": ["slack", "email"],
        "escalation_delay_minutes": 15
      }
    },
    "monitoring": {
      "error_rate_threshold": 0.05,
      "response_time_threshold_ms": 5000,
      "failure_count_threshold": 10,
      "window_size_minutes": 5
    }
  }
}
```

## Implementation Guidelines

### For Agents

1. **Error Classification**: Always classify errors using `ErrorSeverity::from_error()` and `ErrorRecoverability::from_error()`

2. **Context Enrichment**: Create comprehensive `ErrorContext` with all available metadata

3. **Circuit Breaker Usage**: Wrap external service calls with circuit breakers:
   ```rust
   let result = circuit_breaker.call(async {
       external_service.call().await
   }).await?;
   ```

4. **Retry Implementation**: Use `RetryManager` for transient errors:
   ```rust
   let retry_manager = RetryManager::new(RetryConfig::default());
   let result = retry_manager.execute_with_retry(|| {
       Box::pin(async { risky_operation().await })
   }).await?;
   ```

5. **Fallback Strategy**: Implement fallbacks for critical operations:
   ```rust
   let mut fallback_manager = FallbackManager::new();
   fallback_manager.register_strategy(Box::new(CachedResponseFallback::new()));
   ```

6. **Checkpoint Creation**: Create checkpoints before risky operations:
   ```rust
   rollback_manager.create_checkpoint(
       workflow_id,
       "pre_deployment".to_string(),
       Some(metadata)
   ).await?;
   ```

### Error Handling Best Practices

1. **Fail Fast**: Don't retry permanent errors
2. **Graceful Degradation**: Provide fallbacks when possible
3. **Observable Errors**: Include rich context and correlation IDs
4. **Stateful Recovery**: Use checkpoints for complex workflows
5. **Circuit Protection**: Prevent cascade failures with circuit breakers
6. **Adaptive Behavior**: Adjust retry policies based on system health
7. **Comprehensive Logging**: Log at appropriate levels with structured data
8. **Automated Recovery**: Implement auto-rollback for critical failures

## References

This document consolidates patterns from:
- [MCP Error Handling](../protocols/mcp/error-handling.md) → **Refer to this document**
- [Workflow Error Handling](../operations/workflows/error-handling.md) → **Refer to this document**  
- [Integration Error Handling](../integration/external-systems/error-handling.md) → **Refer to this document**
- [Circuit Breaker Patterns](../optimization-patterns/circuit-breakers/CLAUDE.md)
- [State Management](../services/state-management/patterns.md)
- [Service Architecture](../services/service-architecture.md)

For implementation details and advanced patterns, refer to the specific domain documentation while using this document as the authoritative reference for error handling approach.