# Data Conflict Management

## Conflict Resolution Strategies

### Conflict Detection Framework

```typescript
interface ConflictDetector<T> {
  detectConflicts(values: VersionedValue<T>[]): ConflictSet<T>;
  getConflictType(conflict: ConflictSet<T>): ConflictType;
  analyzeConflictComplexity(conflict: ConflictSet<T>): ConflictComplexity;
}

enum ConflictType {
  SIMPLE_CONCURRENT = 'simple_concurrent',     // Two concurrent updates
  MULTI_WAY_CONFLICT = 'multi_way_conflict',   // Multiple concurrent updates
  SEMANTIC_CONFLICT = 'semantic_conflict',     // Logically incompatible changes
  STRUCTURAL_CONFLICT = 'structural_conflict', // Schema or type conflicts
  TEMPORAL_CONFLICT = 'temporal_conflict'      // Time-based ordering conflicts
}

enum ConflictComplexity {
  TRIVIAL = 'trivial',         // Automatically resolvable
  SIMPLE = 'simple',           // Resolvable with basic strategies
  COMPLEX = 'complex',         // Requires sophisticated resolution
  MANUAL = 'manual'            // Requires human intervention
}

class VectorClockConflictDetector<T> implements ConflictDetector<T> {
  detectConflicts(values: VersionedValue<T>[]): ConflictSet<T> {
    if (values.length < 2) {
      return { conflicts: [], isConflicted: false };
    }
    
    const conflicts: ConflictPair<T>[] = [];
    
    for (let i = 0; i < values.length; i++) {
      for (let j = i + 1; j < values.length; j++) {
        const relation = values[i].version.compare(values[j].version);
        
        if (relation === ClockRelation.CONCURRENT) {
          conflicts.push({
            value1: values[i],
            value2: values[j],
            type: ConflictType.SIMPLE_CONCURRENT,
            detectionTime: Date.now()
          });
        }
      }
    }
    
    return {
      conflicts,
      isConflicted: conflicts.length > 0,
      complexConflicts: this.analyzeComplexConflicts(conflicts)
    };
  }
  
  private analyzeComplexConflicts(conflicts: ConflictPair<T>[]): ComplexConflict<T>[] {
    const complexConflicts: ComplexConflict<T>[] = [];
    
    // Group conflicts by key to detect multi-way conflicts
    const conflictsByKey = new Map<string, ConflictPair<T>[]>();
    
    for (const conflict of conflicts) {
      const key = this.extractKey(conflict);
      const existing = conflictsByKey.get(key) || [];
      existing.push(conflict);
      conflictsByKey.set(key, existing);
    }
    
    // Analyze each group
    for (const [key, keyConflicts] of conflictsByKey) {
      if (keyConflicts.length > 1) {
        complexConflicts.push({
          key,
          type: ConflictType.MULTI_WAY_CONFLICT,
          conflicts: keyConflicts,
          complexity: ConflictComplexity.COMPLEX
        });
      }
    }
    
    return complexConflicts;
  }
}
```

### Resolution Strategy Patterns

#### 1. Last-Writer-Wins (LWW)
Simple timestamp-based resolution with deterministic tiebreaking.

```typescript
class LastWriterWinsResolver<T> implements ConflictResolver<T> {
  private tieBreaker: TieBreaker;
  
  constructor(config: LWWConfig) {
    this.tieBreaker = new TieBreaker(config.tieBreakingStrategy);
  }
  
  async resolve(conflict: ConflictSet<T>): Promise<ResolutionResult<T>> {
    if (!conflict.isConflicted) {
      return { resolved: conflict.conflicts[0]?.value1, strategy: 'no_conflict' };
    }
    
    let winner = conflict.conflicts[0].value1;
    
    for (const conflictPair of conflict.conflicts) {
      const candidates = [conflictPair.value1, conflictPair.value2];
      winner = this.selectWinner(candidates);
    }
    
    return {
      resolved: winner,
      strategy: 'last_writer_wins',
      metadata: {
        winnerTimestamp: winner.timestamp,
        conflictsResolved: conflict.conflicts.length
      }
    };
  }
  
  private selectWinner(candidates: VersionedValue<T>[]): VersionedValue<T> {
    // Sort by timestamp (most recent first)
    const sorted = candidates.sort((a, b) => b.timestamp - a.timestamp);
    
    // If timestamps are equal, use tie breaker
    if (sorted.length > 1 && sorted[0].timestamp === sorted[1].timestamp) {
      return this.tieBreaker.resolve(sorted);
    }
    
    return sorted[0];
  }
}

interface TieBreaker {
  resolve<T>(candidates: VersionedValue<T>[]): VersionedValue<T>;
}

class NodeIdTieBreaker implements TieBreaker {
  resolve<T>(candidates: VersionedValue<T>[]): VersionedValue<T> {
    // Deterministic resolution based on node ID (lexicographic order)
    return candidates.sort((a, b) => 
      (a.nodeId || '').localeCompare(b.nodeId || '')
    )[0];
  }
}

class ValueHashTieBreaker implements TieBreaker {
  resolve<T>(candidates: VersionedValue<T>[]): VersionedValue<T> {
    // Deterministic resolution based on value hash
    return candidates.sort((a, b) => {
      const hashA = this.hashValue(a.value);
      const hashB = this.hashValue(b.value);
      return hashA.localeCompare(hashB);
    })[0];
  }
  
  private hashValue(value: any): string {
    return require('crypto')
      .createHash('sha256')
      .update(JSON.stringify(value))
      .digest('hex');
  }
}
```

#### 2. Operational Transform (OT)
Transform operations to maintain consistency in collaborative editing scenarios.

```typescript
interface Operation {
  type: 'insert' | 'delete' | 'retain';
  position?: number;
  content?: string;
  length?: number;
}

interface OperationTransformer {
  transform(op1: Operation, op2: Operation): [Operation, Operation];
  compose(ops: Operation[]): Operation[];
  invert(op: Operation): Operation;
}

class TextOperationTransformer implements OperationTransformer {
  transform(op1: Operation, op2: Operation): [Operation, Operation] {
    // Transform op1 against op2 and op2 against op1
    if (op1.type === 'insert' && op2.type === 'insert') {
      return this.transformInsertInsert(op1, op2);
    } else if (op1.type === 'insert' && op2.type === 'delete') {
      return this.transformInsertDelete(op1, op2);
    } else if (op1.type === 'delete' && op2.type === 'insert') {
      const [op2Prime, op1Prime] = this.transformInsertDelete(op2, op1);
      return [op1Prime, op2Prime];
    } else if (op1.type === 'delete' && op2.type === 'delete') {
      return this.transformDeleteDelete(op1, op2);
    }
    
    throw new Error(`Unsupported operation combination: ${op1.type}, ${op2.type}`);
  }
  
  private transformInsertInsert(
    op1: Operation, 
    op2: Operation
  ): [Operation, Operation] {
    const pos1 = op1.position!;
    const pos2 = op2.position!;
    const len1 = op1.content!.length;
    const len2 = op2.content!.length;
    
    if (pos1 <= pos2) {
      // op1 comes before op2
      return [
        op1, // op1 unchanged
        { ...op2, position: pos2 + len1 } // shift op2 right
      ];
    } else {
      // op2 comes before op1
      return [
        { ...op1, position: pos1 + len2 }, // shift op1 right
        op2 // op2 unchanged
      ];
    }
  }
  
  private transformInsertDelete(
    insert: Operation, 
    delete_: Operation
  ): [Operation, Operation] {
    const insertPos = insert.position!;
    const deletePos = delete_.position!;
    const deleteLen = delete_.length!;
    const insertLen = insert.content!.length;
    
    if (insertPos <= deletePos) {
      // Insert comes before delete
      return [
        insert, // insert unchanged
        { ...delete_, position: deletePos + insertLen } // shift delete right
      ];
    } else if (insertPos >= deletePos + deleteLen) {
      // Insert comes after delete
      return [
        { ...insert, position: insertPos - deleteLen }, // shift insert left
        delete_ // delete unchanged
      ];
    } else {
      // Insert is within delete range
      return [
        { ...insert, position: deletePos }, // move insert to delete start
        { ...delete_, length: deleteLen + insertLen } // extend delete
      ];
    }
  }
  
  private transformDeleteDelete(
    op1: Operation, 
    op2: Operation
  ): [Operation, Operation] {
    const pos1 = op1.position!;
    const len1 = op1.length!;
    const pos2 = op2.position!;
    const len2 = op2.length!;
    
    const end1 = pos1 + len1;
    const end2 = pos2 + len2;
    
    if (end1 <= pos2) {
      // op1 comes completely before op2
      return [
        op1, // op1 unchanged
        { ...op2, position: pos2 - len1 } // shift op2 left
      ];
    } else if (end2 <= pos1) {
      // op2 comes completely before op1
      return [
        { ...op1, position: pos1 - len2 }, // shift op1 left
        op2 // op2 unchanged
      ];
    } else {
      // Overlapping deletes - merge them
      const mergedStart = Math.min(pos1, pos2);
      const mergedEnd = Math.max(end1, end2);
      const mergedLength = mergedEnd - mergedStart;
      
      return [
        { type: 'delete', position: mergedStart, length: mergedLength },
        { type: 'retain', position: 0, length: 0 } // No-op
      ];
    }
  }
  
  compose(ops: Operation[]): Operation[] {
    // Compose a sequence of operations into a minimal set
    const composed: Operation[] = [];
    let currentPosition = 0;
    
    for (const op of ops) {
      if (op.type === 'retain') {
        currentPosition += op.length || 0;
      } else if (op.type === 'insert') {
        composed.push({
          type: 'insert',
          position: currentPosition,
          content: op.content
        });
        currentPosition += op.content!.length;
      } else if (op.type === 'delete') {
        composed.push({
          type: 'delete',
          position: currentPosition,
          length: op.length
        });
        // Don't advance position for deletes
      }
    }
    
    return this.optimizeOperations(composed);
  }
  
  private optimizeOperations(ops: Operation[]): Operation[] {
    // Merge adjacent operations of the same type
    const optimized: Operation[] = [];
    let current: Operation | null = null;
    
    for (const op of ops) {
      if (!current) {
        current = { ...op };
        continue;
      }
      
      if (current.type === op.type && current.type === 'insert') {
        // Merge adjacent inserts
        if (current.position! + current.content!.length === op.position) {
          current.content += op.content;
          continue;
        }
      } else if (current.type === op.type && current.type === 'delete') {
        // Merge adjacent deletes
        if (current.position! + current.length! === op.position) {
          current.length! += op.length!;
          continue;
        }
      }
      
      optimized.push(current);
      current = { ...op };
    }
    
    if (current) {
      optimized.push(current);
    }
    
    return optimized;
  }
}

class OperationalTransformResolver<T> implements ConflictResolver<T> {
  private transformer: OperationTransformer;
  
  constructor(transformer: OperationTransformer) {
    this.transformer = transformer;
  }
  
  async resolve(conflict: ConflictSet<T>): Promise<ResolutionResult<T>> {
    if (!conflict.isConflicted) {
      return { resolved: conflict.conflicts[0]?.value1, strategy: 'no_conflict' };
    }
    
    // Extract operations from conflicted values
    const operations = conflict.conflicts.map(c => 
      this.extractOperations([c.value1, c.value2])
    ).flat();
    
    // Transform operations to make them compatible
    const transformedOps = await this.transformOperations(operations);
    
    // Apply transformed operations to base state
    const baseState = this.findBaseState(conflict);
    const resolved = this.applyOperations(baseState, transformedOps);
    
    return {
      resolved,
      strategy: 'operational_transform',
      metadata: {
        operationsApplied: transformedOps.length,
        baseState: baseState.value
      }
    };
  }
  
  private async transformOperations(operations: Operation[]): Promise<Operation[]> {
    if (operations.length <= 1) {
      return operations;
    }
    
    // Transform operations pairwise to ensure convergence
    const transformed: Operation[] = [];
    let queue = [...operations];
    
    while (queue.length > 1) {
      const op1 = queue.shift()!;
      const op2 = queue.shift()!;
      
      const [t1, t2] = this.transformer.transform(op1, op2);
      transformed.push(t1);
      queue.unshift(t2); // Put t2 back in queue for next iteration
    }
    
    if (queue.length === 1) {
      transformed.push(queue[0]);
    }
    
    return transformed;
  }
}
```

#### 3. Conflict-Free Replicated Data Types (CRDTs)
Mathematical structures that automatically resolve conflicts.

```typescript
interface CRDT<T> {
  merge(other: CRDT<T>): CRDT<T>;
  value(): T;
  clone(): CRDT<T>;
}

class ORSetCRDT<T> implements CRDT<Set<T>> {
  private added: Map<T, Set<string>> = new Map(); // Element -> Set of unique tags
  private removed: Set<string> = new Set(); // Set of removed tags
  private nodeId: string;
  
  constructor(nodeId: string) {
    this.nodeId = nodeId;
  }
  
  add(element: T): void {
    const tag = this.generateUniqueTag();
    const tags = this.added.get(element) || new Set();
    tags.add(tag);
    this.added.set(element, tags);
  }
  
  remove(element: T): void {
    const tags = this.added.get(element);
    if (tags) {
      // Add all tags for this element to removed set
      tags.forEach(tag => this.removed.add(tag));
    }
  }
  
  merge(other: ORSetCRDT<T>): ORSetCRDT<T> {
    const merged = new ORSetCRDT<T>(this.nodeId);
    
    // Merge added elements
    for (const [element, tags] of this.added) {
      const mergedTags = new Set(tags);
      merged.added.set(element, mergedTags);
    }
    
    for (const [element, tags] of other.added) {
      const existingTags = merged.added.get(element) || new Set();
      tags.forEach(tag => existingTags.add(tag));
      merged.added.set(element, existingTags);
    }
    
    // Merge removed tags
    merged.removed = new Set([...this.removed, ...other.removed]);
    
    return merged;
  }
  
  value(): Set<T> {
    const result = new Set<T>();
    
    for (const [element, tags] of this.added) {
      // Element is in set if it has at least one tag that hasn't been removed
      const hasActiveTag = Array.from(tags).some(tag => !this.removed.has(tag));
      if (hasActiveTag) {
        result.add(element);
      }
    }
    
    return result;
  }
  
  clone(): ORSetCRDT<T> {
    const cloned = new ORSetCRDT<T>(this.nodeId);
    
    // Deep clone added map
    for (const [element, tags] of this.added) {
      cloned.added.set(element, new Set(tags));
    }
    
    // Clone removed set
    cloned.removed = new Set(this.removed);
    
    return cloned;
  }
  
  private generateUniqueTag(): string {
    return `${this.nodeId}-${Date.now()}-${Math.random()}`;
  }
}

class LWWMapCRDT<K, V> implements CRDT<Map<K, V>> {
  private values: Map<K, TimestampedValue<V>> = new Map();
  private nodeId: string;
  
  constructor(nodeId: string) {
    this.nodeId = nodeId;
  }
  
  set(key: K, value: V): void {
    const timestamped: TimestampedValue<V> = {
      value,
      timestamp: Date.now(),
      nodeId: this.nodeId
    };
    
    const existing = this.values.get(key);
    if (!existing || this.isNewer(timestamped, existing)) {
      this.values.set(key, timestamped);
    }
  }
  
  delete(key: K): void {
    // Deletion is represented as a special tombstone value
    const tombstone: TimestampedValue<V> = {
      value: undefined as any,
      timestamp: Date.now(),
      nodeId: this.nodeId,
      deleted: true
    };
    
    this.values.set(key, tombstone);
  }
  
  merge(other: LWWMapCRDT<K, V>): LWWMapCRDT<K, V> {
    const merged = new LWWMapCRDT<K, V>(this.nodeId);
    
    // Start with all our values
    for (const [key, value] of this.values) {
      merged.values.set(key, { ...value });
    }
    
    // Merge other's values (keeping newer ones)
    for (const [key, otherValue] of other.values) {
      const ourValue = merged.values.get(key);
      
      if (!ourValue || this.isNewer(otherValue, ourValue)) {
        merged.values.set(key, { ...otherValue });
      }
    }
    
    return merged;
  }
  
  value(): Map<K, V> {
    const result = new Map<K, V>();
    
    for (const [key, timestampedValue] of this.values) {
      if (!timestampedValue.deleted) {
        result.set(key, timestampedValue.value);
      }
    }
    
    return result;
  }
  
  clone(): LWWMapCRDT<K, V> {
    const cloned = new LWWMapCRDT<K, V>(this.nodeId);
    
    for (const [key, value] of this.values) {
      cloned.values.set(key, { ...value });
    }
    
    return cloned;
  }
  
  private isNewer(value1: TimestampedValue<V>, value2: TimestampedValue<V>): boolean {
    if (value1.timestamp > value2.timestamp) {
      return true;
    }
    
    if (value1.timestamp === value2.timestamp) {
      // Use node ID as tiebreaker for deterministic resolution
      return value1.nodeId > value2.nodeId;
    }
    
    return false;
  }
}
```

#### 4. Semantic Conflict Resolution
Domain-specific conflict resolution based on business logic.

```typescript
interface SemanticResolver<T> {
  canResolve(conflict: ConflictSet<T>): boolean;
  resolve(conflict: ConflictSet<T>): Promise<ResolutionResult<T>>;
  getResolutionRules(): ResolutionRule<T>[];
}

interface ResolutionRule<T> {
  name: string;
  condition: (conflict: ConflictSet<T>) => boolean;
  resolver: (conflict: ConflictSet<T>) => Promise<T>;
  priority: number;
}

class AccountBalanceResolver implements SemanticResolver<AccountBalance> {
  private rules: ResolutionRule<AccountBalance>[] = [];
  
  constructor() {
    this.setupRules();
  }
  
  private setupRules(): void {
    // Rule 1: Always preserve the higher balance for safety
    this.rules.push({
      name: 'preserve_higher_balance',
      condition: (conflict) => this.isBalanceConflict(conflict),
      resolver: async (conflict) => {
        const balances = conflict.conflicts.flatMap(c => [c.value1.value, c.value2.value]);
        const maxBalance = Math.max(...balances.map(b => b.amount));
        return balances.find(b => b.amount === maxBalance)!;
      },
      priority: 1
    });
    
    // Rule 2: Merge transaction lists to avoid lost transactions
    this.rules.push({
      name: 'merge_transactions',
      condition: (conflict) => this.hasTransactionConflict(conflict),
      resolver: async (conflict) => {
        const allTransactions = new Set<string>();
        const values = conflict.conflicts.flatMap(c => [c.value1.value, c.value2.value]);
        
        values.forEach(balance => {
          balance.transactions.forEach(txId => allTransactions.add(txId));
        });
        
        // Calculate final balance from all transactions
        const finalAmount = await this.calculateBalanceFromTransactions(
          Array.from(allTransactions)
        );
        
        return {
          amount: finalAmount,
          transactions: Array.from(allTransactions),
          lastUpdated: Date.now()
        };
      },
      priority: 2
    });
    
    // Sort rules by priority
    this.rules.sort((a, b) => a.priority - b.priority);
  }
  
  canResolve(conflict: ConflictSet<AccountBalance>): boolean {
    return this.rules.some(rule => rule.condition(conflict));
  }
  
  async resolve(conflict: ConflictSet<AccountBalance>): Promise<ResolutionResult<AccountBalance>> {
    // Find the first applicable rule
    const applicableRule = this.rules.find(rule => rule.condition(conflict));
    
    if (!applicableRule) {
      throw new Error('No applicable resolution rule found');
    }
    
    const resolved = await applicableRule.resolver(conflict);
    
    return {
      resolved: {
        value: resolved,
        version: this.generateVersion(),
        timestamp: Date.now()
      },
      strategy: 'semantic_resolution',
      metadata: {
        rule: applicableRule.name,
        conflictsProcessed: conflict.conflicts.length
      }
    };
  }
  
  private isBalanceConflict(conflict: ConflictSet<AccountBalance>): boolean {
    const values = conflict.conflicts.flatMap(c => [c.value1.value, c.value2.value]);
    const amounts = values.map(v => v.amount);
    return new Set(amounts).size > 1; // Different amounts = balance conflict
  }
  
  private hasTransactionConflict(conflict: ConflictSet<AccountBalance>): boolean {
    const values = conflict.conflicts.flatMap(c => [c.value1.value, c.value2.value]);
    const allTransactions = new Set<string>();
    
    values.forEach(balance => {
      balance.transactions.forEach(txId => allTransactions.add(txId));
    });
    
    // Check if any value is missing transactions that others have
    return values.some(balance => 
      balance.transactions.length !== allTransactions.size
    );
  }
  
  private async calculateBalanceFromTransactions(transactionIds: string[]): Promise<number> {
    // Fetch all transactions and calculate final balance
    // This would typically involve querying a transaction log
    let balance = 0;
    
    for (const txId of transactionIds) {
      const transaction = await this.getTransaction(txId);
      balance += transaction.amount;
    }
    
    return balance;
  }
}
```

## Conflict Resolution Pipeline

### Multi-Strategy Resolution
```typescript
class ConflictResolutionPipeline<T> {
  private strategies: ConflictResolutionStrategy<T>[] = [];
  private metrics: ConflictMetrics = new ConflictMetrics();
  
  addStrategy(strategy: ConflictResolutionStrategy<T>): void {
    this.strategies.push(strategy);
    this.strategies.sort((a, b) => a.getPriority() - b.getPriority());
  }
  
  async resolve(conflict: ConflictSet<T>): Promise<ResolutionResult<T>> {
    const startTime = Date.now();
    
    try {
      // Try each strategy in priority order
      for (const strategy of this.strategies) {
        if (await strategy.canHandle(conflict)) {
          const result = await strategy.resolve(conflict);
          
          this.metrics.recordResolution(
            strategy.getName(),
            'success',
            Date.now() - startTime
          );
          
          return result;
        }
      }
      
      // No strategy could handle the conflict
      throw new Error('No resolution strategy available for conflict');
      
    } catch (error) {
      this.metrics.recordResolution(
        'unknown',
        'failure',
        Date.now() - startTime
      );
      throw error;
    }
  }
  
  getMetrics(): ConflictResolutionMetrics {
    return this.metrics.getStats();
  }
}
```