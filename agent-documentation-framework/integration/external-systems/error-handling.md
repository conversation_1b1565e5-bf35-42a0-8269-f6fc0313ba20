# Integration Fault Tolerance

> **📖 Primary Reference**: See [RUST-SS Error Handling Pattern<PERSON>](../../architectural-concerns/error-handling.md) for the complete unified error handling framework.

## Integration-Specific Error Patterns

This document provides integration-specific error handling patterns for external systems. For comprehensive error handling strategies, circuit breakers, retry policies, and recovery mechanisms, refer to the [centralized error handling documentation](../../architectural-concerns/error-handling.md).

### Error Types and Handling Strategies

#### 1. Transient Errors
Temporary failures that may resolve themselves with time or retry attempts.

```typescript
enum TransientErrorType {
  NETWORK_TIMEOUT = 'network_timeout',
  CONNECTION_RESET = 'connection_reset',
  SERVICE_UNAVAILABLE = 'service_unavailable',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  TEMPORARY_OVERLOAD = 'temporary_overload'
}

class TransientErrorHandler {
  private retryPolicy: RetryPolicy;
  private circuitBreaker: CircuitBreaker;
  
  constructor(config: ErrorHandlingConfig) {
    this.retryPolicy = new RetryPolicy({
      maxAttempts: config.transient.maxRetries ?? 3,
      baseDelay: config.transient.baseDelay ?? 1000,
      maxDelay: config.transient.maxDelay ?? 30000,
      backoffMultiplier: config.transient.backoffMultiplier ?? 2,
      jitter: config.transient.jitter ?? true
    });
    
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: config.circuitBreaker.failureThreshold ?? 5,
      timeout: config.circuitBreaker.timeout ?? 60000,
      monitoringPeriod: config.circuitBreaker.monitoringPeriod ?? 10000
    });
  }
  
  async handle<T>(
    operation: () => Promise<T>,
    errorClassifier: (error: Error) => boolean
  ): Promise<T> {
    return this.circuitBreaker.execute(() => {
      return this.retryPolicy.execute(async (attempt: number) => {
        try {
          return await operation();
        } catch (error) {
          if (!errorClassifier(error)) {
            throw error; // Non-transient error, don't retry
          }
          
          if (this.shouldBackoff(error)) {
            await this.applyBackoff(attempt, error);
          }
          
          throw error; // Will trigger retry if attempts remaining
        }
      });
    });
  }
  
  private shouldBackoff(error: Error): boolean {
    // Apply backoff for rate limiting and overload scenarios
    return error instanceof RateLimitError || 
           error instanceof ServiceOverloadError;
  }
  
  private async applyBackoff(attempt: number, error: Error): Promise<void> {
    let delay = this.retryPolicy.calculateDelay(attempt);
    
    // Respect Retry-After header for rate limiting
    if (error instanceof RateLimitError && error.retryAfter) {
      delay = Math.max(delay, error.retryAfter * 1000);
    }
    
    await this.sleep(delay);
  }
}
```

#### 2. Permanent Errors
Errors that won't resolve with retries and require different handling strategies.

```typescript
enum PermanentErrorType {
  AUTHENTICATION_FAILED = 'authentication_failed',
  AUTHORIZATION_DENIED = 'authorization_denied',
  RESOURCE_NOT_FOUND = 'resource_not_found',
  INVALID_REQUEST = 'invalid_request',
  SCHEMA_VALIDATION_FAILED = 'schema_validation_failed'
}

class PermanentErrorHandler {
  private errorMappings: Map<string, ErrorMappingStrategy>;
  private fallbackHandlers: Map<string, FallbackHandler>;
  
  constructor(config: ErrorHandlingConfig) {
    this.setupErrorMappings(config.permanent.mappings);
    this.setupFallbackHandlers(config.permanent.fallbacks);
  }
  
  async handle(error: Error, context: OperationContext): Promise<ErrorHandlingResult> {
    const errorType = this.classifyError(error);
    const mappingStrategy = this.errorMappings.get(errorType);
    
    if (mappingStrategy) {
      const mappedError = mappingStrategy.map(error, context);
      await this.logError(mappedError, context);
      
      // Check for fallback handler
      const fallbackHandler = this.fallbackHandlers.get(errorType);
      if (fallbackHandler) {
        const fallbackResult = await fallbackHandler.handle(mappedError, context);
        return {
          error: mappedError,
          handled: true,
          fallbackResult
        };
      }
      
      return {
        error: mappedError,
        handled: false
      };
    }
    
    // Unknown error type
    await this.logUnknownError(error, context);
    return {
      error,
      handled: false
    };
  }
  
  private classifyError(error: Error): string {
    // HTTP status code mapping
    if (error instanceof HTTPError) {
      switch (error.status) {
        case 401: return PermanentErrorType.AUTHENTICATION_FAILED;
        case 403: return PermanentErrorType.AUTHORIZATION_DENIED;
        case 404: return PermanentErrorType.RESOURCE_NOT_FOUND;
        case 400: return PermanentErrorType.INVALID_REQUEST;
        case 422: return PermanentErrorType.SCHEMA_VALIDATION_FAILED;
      }
    }
    
    // gRPC status code mapping
    if (error instanceof GRPCError) {
      switch (error.code) {
        case grpc.status.UNAUTHENTICATED: 
          return PermanentErrorType.AUTHENTICATION_FAILED;
        case grpc.status.PERMISSION_DENIED: 
          return PermanentErrorType.AUTHORIZATION_DENIED;
        case grpc.status.NOT_FOUND: 
          return PermanentErrorType.RESOURCE_NOT_FOUND;
        case grpc.status.INVALID_ARGUMENT: 
          return PermanentErrorType.INVALID_REQUEST;
      }
    }
    
    return 'unknown';
  }
}
```

#### 3. System Errors
Infrastructure-level errors that require immediate attention and may affect system stability.

```typescript
enum SystemErrorType {
  OUT_OF_MEMORY = 'out_of_memory',
  DISK_FULL = 'disk_full',
  DATABASE_CONNECTION_LOST = 'database_connection_lost',
  SERVICE_DEPENDENCY_FAILURE = 'service_dependency_failure',
  CONFIGURATION_ERROR = 'configuration_error'
}

class SystemErrorHandler {
  private alertManager: AlertManager;
  private healthMonitor: HealthMonitor;
  private emergencyShutdown: EmergencyShutdown;
  
  constructor(config: SystemErrorConfig) {
    this.alertManager = new AlertManager(config.alerts);
    this.healthMonitor = new HealthMonitor(config.health);
    this.emergencyShutdown = new EmergencyShutdown(config.shutdown);
  }
  
  async handle(error: SystemError, context: OperationContext): Promise<void> {
    const severity = this.assessSeverity(error);
    
    // Immediate alerting for critical errors
    if (severity === 'critical') {
      await this.alertManager.sendCriticalAlert(error, context);
    }
    
    // Update health status
    await this.healthMonitor.reportSystemError(error);
    
    // Determine response strategy
    switch (error.type) {
      case SystemErrorType.OUT_OF_MEMORY:
        await this.handleMemoryExhaustion(error, context);
        break;
        
      case SystemErrorType.DISK_FULL:
        await this.handleDiskSpaceExhaustion(error, context);
        break;
        
      case SystemErrorType.DATABASE_CONNECTION_LOST:
        await this.handleDatabaseFailure(error, context);
        break;
        
      case SystemErrorType.SERVICE_DEPENDENCY_FAILURE:
        await this.handleDependencyFailure(error, context);
        break;
        
      default:
        await this.handleGenericSystemError(error, context);
    }
  }
  
  private async handleMemoryExhaustion(error: SystemError, context: OperationContext): Promise<void> {
    // Immediate actions to free memory
    await this.freeMemoryResources();
    
    // Reduce processing load
    await this.throttleOperations();
    
    // Alert operations team
    await this.alertManager.sendAlert({
      type: 'memory_exhaustion',
      severity: 'critical',
      message: 'System memory exhausted - immediate action required',
      context,
      recommendedActions: [
        'Check for memory leaks in recent deployments',
        'Scale up memory resources',
        'Review memory usage patterns'
      ]
    });
    
    // Consider emergency shutdown if memory cannot be freed
    const memoryFreed = await this.checkMemoryRecovery();
    if (!memoryFreed) {
      await this.emergencyShutdown.initiate('memory_exhaustion');
    }
  }
}
```

## Circuit Breaker Implementation

### Advanced Circuit Breaker Pattern

```typescript
enum CircuitState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open'
}

interface CircuitBreakerConfig {
  failureThreshold: number;
  successThreshold: number;
  timeout: number;
  monitoringPeriod: number;
  volumeThreshold: number;
}

class AdvancedCircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private successCount: number = 0;
  private lastFailureTime: number = 0;
  private requestCount: number = 0;
  private requestWindow: number[] = [];
  
  constructor(private config: CircuitBreakerConfig) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.transitionToHalfOpen();
      } else {
        throw new CircuitBreakerOpenError('Circuit breaker is open');
      }
    }
    
    try {
      const result = await this.executeWithTimeout(operation);
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private async executeWithTimeout<T>(operation: () => Promise<T>): Promise<T> {
    return Promise.race([
      operation(),
      this.createTimeoutPromise()
    ]);
  }
  
  private onSuccess(): void {
    this.requestCount++;
    
    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= this.config.successThreshold) {
        this.transitionToClosed();
      }
    } else {
      this.failureCount = 0; // Reset failure count on success
    }
    
    this.updateRequestWindow();
  }
  
  private onFailure(): void {
    this.requestCount++;
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.state === CircuitState.HALF_OPEN) {
      this.transitionToOpen();
    } else if (this.shouldOpenCircuit()) {
      this.transitionToOpen();
    }
    
    this.updateRequestWindow();
  }
  
  private shouldOpenCircuit(): boolean {
    // Check if we have enough volume to make a decision
    if (this.requestCount < this.config.volumeThreshold) {
      return false;
    }
    
    // Check failure rate within monitoring period
    const recentRequests = this.getRecentRequests();
    const failureRate = this.calculateFailureRate(recentRequests);
    
    return failureRate >= (this.config.failureThreshold / 100);
  }
  
  private shouldAttemptReset(): boolean {
    const timeSinceLastFailure = Date.now() - this.lastFailureTime;
    return timeSinceLastFailure >= this.config.timeout;
  }
  
  private transitionToOpen(): void {
    this.state = CircuitState.OPEN;
    this.successCount = 0;
    this.emitStateChange('open');
  }
  
  private transitionToHalfOpen(): void {
    this.state = CircuitState.HALF_OPEN;
    this.successCount = 0;
    this.emitStateChange('half_open');
  }
  
  private transitionToClosed(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.emitStateChange('closed');
  }
  
  private calculateFailureRate(requests: RequestRecord[]): number {
    if (requests.length === 0) return 0;
    
    const failures = requests.filter(r => !r.success).length;
    return (failures / requests.length) * 100;
  }
  
  getMetrics(): CircuitBreakerMetrics {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      requestCount: this.requestCount,
      lastFailureTime: this.lastFailureTime,
      failureRate: this.calculateCurrentFailureRate()
    };
  }
}
```

## Retry Strategies

### Exponential Backoff with Jitter

```typescript
interface RetryPolicyConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
  retryCondition?: (error: Error, attempt: number) => boolean;
}

class RetryPolicy {
  constructor(private config: RetryPolicyConfig) {}
  
  async execute<T>(operation: (attempt: number) => Promise<T>): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.config.maxAttempts; attempt++) {
      try {
        return await operation(attempt);
      } catch (error) {
        lastError = error;
        
        // Check if we should retry this error
        if (this.config.retryCondition && !this.config.retryCondition(error, attempt)) {
          throw error;
        }
        
        // Don't delay after the last attempt
        if (attempt < this.config.maxAttempts) {
          const delay = this.calculateDelay(attempt);
          await this.sleep(delay);
        }
      }
    }
    
    throw lastError!;
  }
  
  calculateDelay(attempt: number): number {
    // Exponential backoff: baseDelay * (multiplier ^ (attempt - 1))
    let delay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt - 1);
    
    // Cap at maximum delay
    delay = Math.min(delay, this.config.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (this.config.jitter) {
      const jitterRange = delay * 0.1; // 10% jitter
      const jitter = (Math.random() - 0.5) * 2 * jitterRange;
      delay += jitter;
    }
    
    return Math.max(0, delay);
  }
  
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### Adaptive Retry Strategy

```typescript
class AdaptiveRetryPolicy {
  private successRate: number = 1.0;
  private avgResponseTime: number = 0;
  private recentOutcomes: boolean[] = [];
  private windowSize: number = 100;
  
  constructor(private baseConfig: RetryPolicyConfig) {}
  
  async execute<T>(operation: (attempt: number) => Promise<T>): Promise<T> {
    const adaptedConfig = this.adaptConfiguration();
    const retryPolicy = new RetryPolicy(adaptedConfig);
    
    const startTime = Date.now();
    try {
      const result = await retryPolicy.execute(operation);
      this.recordSuccess(Date.now() - startTime);
      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }
  
  private adaptConfiguration(): RetryPolicyConfig {
    const adaptedConfig = { ...this.baseConfig };
    
    // Reduce retries if success rate is low
    if (this.successRate < 0.5) {
      adaptedConfig.maxAttempts = Math.max(1, Math.floor(adaptedConfig.maxAttempts / 2));
    }
    
    // Increase delays if system is slow
    if (this.avgResponseTime > 5000) { // 5 seconds
      adaptedConfig.baseDelay *= 2;
      adaptedConfig.maxDelay *= 2;
    }
    
    return adaptedConfig;
  }
  
  private recordSuccess(responseTime: number): void {
    this.recentOutcomes.push(true);
    this.updateMetrics(responseTime);
  }
  
  private recordFailure(): void {
    this.recentOutcomes.push(false);
    this.updateMetrics();
  }
  
  private updateMetrics(responseTime?: number): void {
    // Maintain sliding window
    if (this.recentOutcomes.length > this.windowSize) {
      this.recentOutcomes.shift();
    }
    
    // Update success rate
    const successes = this.recentOutcomes.filter(success => success).length;
    this.successRate = successes / this.recentOutcomes.length;
    
    // Update average response time
    if (responseTime !== undefined) {
      this.avgResponseTime = (this.avgResponseTime + responseTime) / 2;
    }
  }
}
```

## Fallback Mechanisms

### Graceful Degradation Strategies

```typescript
interface FallbackStrategy<T> {
  canHandle(error: Error): boolean;
  execute(context: OperationContext): Promise<T>;
  getPriority(): number;
}

class CachedResponseFallback<T> implements FallbackStrategy<T> {
  constructor(private cache: Cache, private ttl: number = 3600000) {} // 1 hour default
  
  canHandle(error: Error): boolean {
    return error instanceof ServiceUnavailableError || 
           error instanceof TimeoutError;
  }
  
  async execute(context: OperationContext): Promise<T> {
    const cacheKey = this.generateCacheKey(context);
    const cachedValue = await this.cache.get(cacheKey);
    
    if (cachedValue) {
      return cachedValue;
    }
    
    throw new Error('No cached value available for fallback');
  }
  
  getPriority(): number {
    return 1; // High priority
  }
}

class DefaultValueFallback<T> implements FallbackStrategy<T> {
  constructor(private defaultValue: T) {}
  
  canHandle(error: Error): boolean {
    return true; // Can handle any error
  }
  
  async execute(context: OperationContext): Promise<T> {
    return this.defaultValue;
  }
  
  getPriority(): number {
    return 10; // Low priority - last resort
  }
}

class AlternativeServiceFallback<T> implements FallbackStrategy<T> {
  constructor(private alternativeService: ExternalServiceConnector) {}
  
  canHandle(error: Error): boolean {
    return error instanceof ServiceUnavailableError;
  }
  
  async execute(context: OperationContext): Promise<T> {
    return this.alternativeService.execute(context.operation);
  }
  
  getPriority(): number {
    return 2; // Medium priority
  }
}

class FallbackManager<T> {
  private strategies: FallbackStrategy<T>[] = [];
  
  registerStrategy(strategy: FallbackStrategy<T>): void {
    this.strategies.push(strategy);
    this.strategies.sort((a, b) => a.getPriority() - b.getPriority());
  }
  
  async executeFallback(error: Error, context: OperationContext): Promise<T> {
    for (const strategy of this.strategies) {
      if (strategy.canHandle(error)) {
        try {
          const result = await strategy.execute(context);
          this.recordFallbackSuccess(strategy.constructor.name);
          return result;
        } catch (fallbackError) {
          this.recordFallbackFailure(strategy.constructor.name, fallbackError);
          // Continue to next strategy
        }
      }
    }
    
    throw new Error('All fallback strategies failed');
  }
}
```

## Integration Error Handling Summary

This integration-specific error handling complements the [unified RUST-SS error handling framework](../../architectural-concerns/error-handling.md) with patterns optimized for external system interactions, including transient error recovery, permanent error handling, and system-level fault tolerance.