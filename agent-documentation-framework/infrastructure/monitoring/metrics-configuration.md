# Metrics Configuration

## Prometheus Metrics Setup

### Basic Metrics Registry
```rust
use prometheus::{
    register_counter_vec, register_gauge_vec, register_histogram_vec,
    Counter, CounterVec, Gauge, GaugeVec, Histogram, HistogramVec,
    Registry, TextEncoder, Encoder
};
use lazy_static::lazy_static;

lazy_static! {
    pub static ref REGISTRY: Registry = Registry::new();
    
    // Agent metrics
    pub static ref AGENT_TASKS_TOTAL: CounterVec = register_counter_vec!(
        "agent_tasks_total",
        "Total number of tasks processed by agents",
        &["agent_type", "agent_id", "status", "priority"]
    ).unwrap();
    
    pub static ref AGENT_ACTIVE_TASKS: GaugeVec = register_gauge_vec!(
        "agent_active_tasks",
        "Number of currently active tasks per agent",
        &["agent_type", "agent_id"]
    ).unwrap();
    
    pub static ref AGENT_TASK_DURATION: HistogramVec = register_histogram_vec!(
        "agent_task_duration_seconds",
        "Task processing duration in seconds",
        &["agent_type", "task_type"],
        vec![0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0]
    ).unwrap();
}
```

### Metrics Middleware
```rust
use axum::{
    middleware::{self, Next},
    response::Response,
    extract::MatchedPath,
    http::{Request, StatusCode},
};
use std::time::Instant;

pub async fn metrics_middleware<B>(
    req: Request<B>,
    next: Next<B>,
) -> Result<Response, StatusCode> {
    let start = Instant::now();
    let path = req
        .extensions()
        .get::<MatchedPath>()
        .map(|p| p.as_str().to_string())
        .unwrap_or_else(|| "unknown".to_string());
    let method = req.method().to_string();
    
    let response = next.run(req).await;
    
    let duration = start.elapsed().as_secs_f64();
    let status = response.status().as_u16().to_string();
    
    HTTP_REQUEST_DURATION
        .with_label_values(&[&method, &path, &status])
        .observe(duration);
        
    HTTP_REQUESTS_TOTAL
        .with_label_values(&[&method, &path, &status])
        .inc();
    
    Ok(response)
}
```

### Custom Business Metrics
```rust
pub struct BusinessMetrics {
    task_queue_depth: GaugeVec,
    agent_utilization: GaugeVec,
    task_success_rate: GaugeVec,
    revenue_processed: CounterVec,
}

impl BusinessMetrics {
    pub fn new() -> Self {
        Self {
            task_queue_depth: register_gauge_vec!(
                "business_task_queue_depth",
                "Current depth of task queues",
                &["queue_name", "priority"]
            ).unwrap(),
            
            agent_utilization: register_gauge_vec!(
                "business_agent_utilization_ratio",
                "Agent utilization as ratio of busy time",
                &["agent_type", "agent_pool"]
            ).unwrap(),
            
            task_success_rate: register_gauge_vec!(
                "business_task_success_rate",
                "Rolling success rate for tasks",
                &["task_type", "window"]
            ).unwrap(),
            
            revenue_processed: register_counter_vec!(
                "business_revenue_processed_total",
                "Total revenue processed in cents",
                &["currency", "product", "status"]
            ).unwrap(),
        }
    }
    
    pub fn record_task_completion(&self, task_type: &str, success: bool, value_cents: u64) {
        let status = if success { "success" } else { "failure" };
        
        self.revenue_processed
            .with_label_values(&["USD", task_type, status])
            .inc_by(value_cents as f64);
    }
}
```

## Application Performance Metrics

### Resource Pool Metrics
```rust
pub struct PoolMetrics {
    connections_total: IntGaugeVec,
    connections_active: IntGaugeVec,
    connections_idle: IntGaugeVec,
    wait_time: HistogramVec,
    timeout_total: IntCounterVec,
}

impl PoolMetrics {
    pub fn new(pool_name: &str) -> Self {
        let labels = &["pool_name"];
        
        Self {
            connections_total: register_int_gauge_vec!(
                format!("{}_connections_total", pool_name),
                "Total number of connections in pool",
                labels
            ).unwrap(),
            
            connections_active: register_int_gauge_vec!(
                format!("{}_connections_active", pool_name),
                "Number of active connections",
                labels
            ).unwrap(),
            
            connections_idle: register_int_gauge_vec!(
                format!("{}_connections_idle", pool_name),
                "Number of idle connections",
                labels
            ).unwrap(),
            
            wait_time: register_histogram_vec!(
                format!("{}_wait_time_seconds", pool_name),
                "Time spent waiting for connection",
                labels,
                exponential_buckets(0.001, 2.0, 10).unwrap()
            ).unwrap(),
            
            timeout_total: register_int_counter_vec!(
                format!("{}_timeout_total", pool_name),
                "Number of connection timeouts",
                labels
            ).unwrap(),
        }
    }
    
    pub fn record_acquisition(&self, pool_name: &str, wait_time: Duration, success: bool) {
        self.wait_time
            .with_label_values(&[pool_name])
            .observe(wait_time.as_secs_f64());
            
        if !success {
            self.timeout_total
                .with_label_values(&[pool_name])
                .inc();
        }
    }
}
```

### Cache Performance Metrics
```rust
pub struct CacheMetrics {
    hit_total: IntCounterVec,
    miss_total: IntCounterVec,
    eviction_total: IntCounterVec,
    size_bytes: IntGaugeVec,
    operation_duration: HistogramVec,
}

impl CacheMetrics {
    pub fn new() -> Self {
        Self {
            hit_total: register_int_counter_vec!(
                "cache_hit_total",
                "Total number of cache hits",
                &["cache_name", "cache_type"]
            ).unwrap(),
            
            miss_total: register_int_counter_vec!(
                "cache_miss_total",
                "Total number of cache misses",
                &["cache_name", "cache_type"]
            ).unwrap(),
            
            eviction_total: register_int_counter_vec!(
                "cache_eviction_total",
                "Total number of cache evictions",
                &["cache_name", "cache_type", "reason"]
            ).unwrap(),
            
            size_bytes: register_int_gauge_vec!(
                "cache_size_bytes",
                "Current cache size in bytes",
                &["cache_name", "cache_type"]
            ).unwrap(),
            
            operation_duration: register_histogram_vec!(
                "cache_operation_duration_seconds",
                "Cache operation duration",
                &["cache_name", "operation"],
                exponential_buckets(0.000001, 10.0, 8).unwrap() // 1μs to 10ms
            ).unwrap(),
        }
    }
    
    pub fn record_hit_rate(&self, cache_name: &str, hits: u64, total: u64) {
        let hit_rate = if total > 0 {
            hits as f64 / total as f64
        } else {
            0.0
        };
        
        CACHE_HIT_RATE
            .with_label_values(&[cache_name])
            .set(hit_rate);
    }
}
```

## System Resource Metrics

### CPU and Memory Monitoring
```rust
use sysinfo::{System, SystemExt, ProcessExt, CpuExt};

pub struct SystemMetrics {
    cpu_usage: GaugeVec,
    memory_usage: GaugeVec,
    disk_usage: GaugeVec,
    network_io: CounterVec,
}

impl SystemMetrics {
    pub async fn collect_metrics(&self) {
        let mut system = System::new_all();
        
        loop {
            system.refresh_all();
            
            // CPU metrics
            for (i, cpu) in system.cpus().iter().enumerate() {
                self.cpu_usage
                    .with_label_values(&[&format!("cpu{}", i)])
                    .set(cpu.cpu_usage() as f64);
            }
            
            // Memory metrics
            self.memory_usage
                .with_label_values(&["used"])
                .set(system.used_memory() as f64);
                
            self.memory_usage
                .with_label_values(&["available"])
                .set(system.available_memory() as f64);
                
            self.memory_usage
                .with_label_values(&["total"])
                .set(system.total_memory() as f64);
            
            // Process-specific metrics
            if let Some(process) = system.process(sysinfo::get_current_pid().unwrap()) {
                PROCESS_MEMORY_BYTES.set(process.memory() as f64);
                PROCESS_CPU_USAGE.set(process.cpu_usage() as f64);
                PROCESS_THREADS.set(process.threads() as f64);
            }
            
            tokio::time::sleep(Duration::from_secs(10)).await;
        }
    }
}
```

## RED Method Implementation

### Request Rate, Errors, Duration
```rust
pub struct REDMetrics {
    request_rate: CounterVec,
    error_rate: CounterVec,
    duration_histogram: HistogramVec,
    duration_summary: SummaryVec,
}

impl REDMetrics {
    pub fn new(service_name: &str) -> Self {
        Self {
            request_rate: register_counter_vec!(
                format!("{}_requests_total", service_name),
                "Total number of requests",
                &["method", "endpoint"]
            ).unwrap(),
            
            error_rate: register_counter_vec!(
                format!("{}_errors_total", service_name),
                "Total number of errors",
                &["method", "endpoint", "error_type"]
            ).unwrap(),
            
            duration_histogram: register_histogram_vec!(
                format!("{}_request_duration_seconds", service_name),
                "Request duration histogram",
                &["method", "endpoint", "status_code"],
                vec![0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
            ).unwrap(),
            
            duration_summary: register_summary_vec!(
                format!("{}_request_duration_quantiles", service_name),
                "Request duration quantiles",
                &["method", "endpoint"],
                vec![0.5, 0.9, 0.95, 0.99]
            ).unwrap(),
        }
    }
    
    pub fn record_request(
        &self,
        method: &str,
        endpoint: &str,
        status: u16,
        duration: Duration,
        error: Option<&str>,
    ) {
        self.request_rate
            .with_label_values(&[method, endpoint])
            .inc();
            
        if let Some(error_type) = error {
            self.error_rate
                .with_label_values(&[method, endpoint, error_type])
                .inc();
        }
        
        let duration_secs = duration.as_secs_f64();
        
        self.duration_histogram
            .with_label_values(&[method, endpoint, &status.to_string()])
            .observe(duration_secs);
            
        self.duration_summary
            .with_label_values(&[method, endpoint])
            .observe(duration_secs);
    }
}
```

## Custom Collectors

### Dynamic Metric Collection
```rust
use prometheus::core::{Collector, Desc, Metric, Opts};
use prometheus::proto::MetricFamily;

pub struct DynamicCollector {
    descs: Vec<Desc>,
    collect_fn: Box<dyn Fn() -> Vec<MetricFamily> + Send + Sync>,
}

impl DynamicCollector {
    pub fn new<F>(name: &str, help: &str, collect_fn: F) -> Self
    where
        F: Fn() -> Vec<MetricFamily> + Send + Sync + 'static,
    {
        let desc = Desc::new(
            name.to_string(),
            help.to_string(),
            vec![],
            HashMap::new(),
        ).unwrap();
        
        Self {
            descs: vec![desc],
            collect_fn: Box::new(collect_fn),
        }
    }
}

impl Collector for DynamicCollector {
    fn desc(&self) -> Vec<&Desc> {
        self.descs.iter().collect()
    }
    
    fn collect(&self) -> Vec<MetricFamily> {
        (self.collect_fn)()
    }
}

// Example: Collect agent metrics dynamically
pub fn create_agent_collector(agent_manager: Arc<AgentManager>) -> DynamicCollector {
    DynamicCollector::new(
        "agent_dynamic_info",
        "Dynamic agent information",
        move || {
            let agents = agent_manager.get_all_agents();
            let mut metrics = Vec::new();
            
            for agent in agents {
                // Create gauge for each agent
                let mut gauge = Gauge::new();
                gauge.set(agent.current_load as f64);
                
                let mut metric = Metric::new();
                metric.set_gauge(gauge);
                metric.set_label(vec![
                    create_label("agent_id", &agent.id),
                    create_label("agent_type", &agent.agent_type),
                    create_label("status", &agent.status.to_string()),
                ]);
                
                let mut family = MetricFamily::new();
                family.set_name("agent_current_load".to_string());
                family.set_help("Current load on agent".to_string());
                family.set_metric_type(MetricType::GAUGE);
                family.set_metric(vec![metric]);
                
                metrics.push(family);
            }
            
            metrics
        },
    )
}
```

## Exemplar Support

### Recording Exemplars with Traces
```rust
pub struct ExemplarRecorder {
    trace_id_header: String,
}

impl ExemplarRecorder {
    pub fn record_with_exemplar(
        &self,
        histogram: &HistogramVec,
        labels: &[&str],
        value: f64,
        trace_id: Option<String>,
    ) {
        let metric = histogram.with_label_values(labels);
        
        if let Some(trace_id) = trace_id {
            // In a real implementation, this would attach the exemplar
            // For now, we'll log it
            log::debug!(
                "Recording exemplar: metric={:?}, value={}, trace_id={}",
                labels,
                value,
                trace_id
            );
        }
        
        metric.observe(value);
    }
}
```

## Metric Export Configuration

### Prometheus Scrape Endpoint
```rust
use axum::{routing::get, Router};

pub fn metrics_router() -> Router {
    Router::new()
        .route("/metrics", get(metrics_handler))
        .route("/health", get(health_handler))
}

async fn metrics_handler() -> String {
    let encoder = TextEncoder::new();
    let metric_families = REGISTRY.gather();
    
    let mut buffer = Vec::new();
    encoder.encode(&metric_families, &mut buffer).unwrap();
    
    String::from_utf8(buffer).unwrap()
}

async fn health_handler() -> impl IntoResponse {
    let health = json!({
        "status": "healthy",
        "timestamp": Utc::now().to_rfc3339(),
        "version": env!("CARGO_PKG_VERSION"),
    });
    
    Json(health)
}
```

### Push Gateway Configuration
```rust
pub struct PushGatewayConfig {
    endpoint: String,
    job_name: String,
    instance: String,
    push_interval: Duration,
}

impl PushGatewayConfig {
    pub async fn start_pusher(self, registry: Registry) {
        let client = reqwest::Client::new();
        let mut interval = tokio::time::interval(self.push_interval);
        
        loop {
            interval.tick().await;
            
            let encoder = TextEncoder::new();
            let metric_families = registry.gather();
            
            let mut buffer = Vec::new();
            encoder.encode(&metric_families, &mut buffer).unwrap();
            
            let url = format!(
                "{}/metrics/job/{}/instance/{}",
                self.endpoint, self.job_name, self.instance
            );
            
            match client
                .post(&url)
                .body(buffer)
                .header("Content-Type", "text/plain; version=0.0.4")
                .send()
                .await
            {
                Ok(_) => log::debug!("Metrics pushed successfully"),
                Err(e) => log::error!("Failed to push metrics: {}", e),
            }
        }
    }
}
```

## Cardinality Management

### Label Cardinality Limiter
```rust
pub struct CardinalityLimiter {
    max_cardinality: usize,
    seen_labels: Arc<DashMap<String, HashSet<String>>>,
}

impl CardinalityLimiter {
    pub fn check_and_add(&self, metric_name: &str, labels: &[(&str, &str)]) -> bool {
        let label_key = labels.iter()
            .map(|(k, v)| format!("{}={}", k, v))
            .collect::<Vec<_>>()
            .join(",");
            
        let mut seen = self.seen_labels
            .entry(metric_name.to_string())
            .or_insert_with(HashSet::new);
            
        if seen.len() >= self.max_cardinality && !seen.contains(&label_key) {
            log::warn!(
                "Cardinality limit reached for metric {}: {} unique label combinations",
                metric_name,
                self.max_cardinality
            );
            return false;
        }
        
        seen.insert(label_key);
        true
    }
}
```

## Metric Aggregation Rules

### Pre-aggregation Configuration
```rust
pub struct AggregationRule {
    source_metric: String,
    target_metric: String,
    aggregation_type: AggregationType,
    interval: Duration,
    labels_to_keep: Vec<String>,
}

pub enum AggregationType {
    Sum,
    Average,
    Max,
    Min,
    Percentile(f64),
}

pub struct MetricAggregator {
    rules: Vec<AggregationRule>,
    registry: Registry,
}

impl MetricAggregator {
    pub async fn run(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(60));
        
        loop {
            interval.tick().await;
            
            for rule in &self.rules {
                self.apply_rule(rule).await;
            }
        }
    }
    
    async fn apply_rule(&self, rule: &AggregationRule) {
        // Collect source metrics
        let families = self.registry.gather();
        let source_family = families.iter()
            .find(|f| f.get_name() == rule.source_metric);
            
        if let Some(family) = source_family {
            // Aggregate based on rule
            let aggregated = self.aggregate_metrics(family, rule);
            
            // Store in target metric
            self.store_aggregated(rule.target_metric.clone(), aggregated);
        }
    }
}
```