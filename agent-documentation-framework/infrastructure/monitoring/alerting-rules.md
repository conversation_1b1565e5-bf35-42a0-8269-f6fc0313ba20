# Alerting Rules

## Alert Manager Configuration

### Basic Alert Rules Setup
```rust
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Debug, Serialize, Deserialize)]
pub struct AlertRule {
    pub name: String,
    pub condition: AlertCondition,
    pub threshold: f64,
    pub duration: Duration,
    pub severity: AlertSeverity,
    pub labels: HashMap<String, String>,
    pub annotations: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum AlertCondition {
    GreaterThan,
    LessThan,
    Equals,
    NotEquals,
    ChangePercent(f64), // Percentage change threshold
    Absent,             // Metric is missing
}

#[derive(Debug, Serialize, Deserialize, Clone, Copy)]
pub enum AlertSeverity {
    Critical,
    Warning,
    Info,
}

pub struct AlertManager {
    rules: Vec<AlertRule>,
    active_alerts: Arc<RwLock<HashMap<String, ActiveAlert>>>,
    notification_channels: Vec<Box<dyn NotificationChannel>>,
}

#[derive(Debug, <PERSON>lone)]
struct ActiveAlert {
    rule_name: String,
    value: f64,
    fired_at: SystemTime,
    last_sent: Option<SystemTime>,
    send_count: u32,
}
```

### Rule Engine Implementation
```rust
impl AlertManager {
    pub async fn evaluate_rules(&self, metrics: &MetricSnapshot) {
        for rule in &self.rules {
            let current_value = match self.extract_metric_value(metrics, &rule.name) {
                Some(value) => value,
                None => {
                    if matches!(rule.condition, AlertCondition::Absent) {
                        self.fire_alert(rule, 0.0).await;
                    }
                    continue;
                }
            };
            
            let should_alert = match rule.condition {
                AlertCondition::GreaterThan => current_value > rule.threshold,
                AlertCondition::LessThan => current_value < rule.threshold,
                AlertCondition::Equals => (current_value - rule.threshold).abs() < f64::EPSILON,
                AlertCondition::NotEquals => (current_value - rule.threshold).abs() > f64::EPSILON,
                AlertCondition::ChangePercent(threshold) => {
                    self.check_percentage_change(&rule.name, current_value, threshold)
                }
                AlertCondition::Absent => false, // Already handled above
            };
            
            if should_alert {
                self.fire_alert(rule, current_value).await;
            } else {
                self.resolve_alert(&rule.name).await;
            }
        }
    }
    
    async fn fire_alert(&self, rule: &AlertRule, value: f64) {
        let alert_key = rule.name.clone();
        let now = SystemTime::now();
        
        let mut active_alerts = self.active_alerts.write().await;
        
        match active_alerts.get_mut(&alert_key) {
            Some(alert) => {
                // Update existing alert
                alert.value = value;
                
                // Check if we should send notification again (rate limiting)
                if self.should_send_notification(alert, rule.severity) {
                    self.send_notification(rule, value).await;
                    alert.last_sent = Some(now);
                    alert.send_count += 1;
                }
            }
            None => {
                // New alert
                let alert = ActiveAlert {
                    rule_name: rule.name.clone(),
                    value,
                    fired_at: now,
                    last_sent: Some(now),
                    send_count: 1,
                };
                
                active_alerts.insert(alert_key, alert);
                self.send_notification(rule, value).await;
            }
        }
    }
    
    fn should_send_notification(&self, alert: &ActiveAlert, severity: AlertSeverity) -> bool {
        let interval = match severity {
            AlertSeverity::Critical => Duration::from_secs(60),    // 1 minute
            AlertSeverity::Warning => Duration::from_secs(300),   // 5 minutes
            AlertSeverity::Info => Duration::from_secs(1800),     // 30 minutes
        };
        
        alert.last_sent
            .map(|last| last.elapsed().unwrap_or_default() >= interval)
            .unwrap_or(true)
    }
}
```

## Notification Channels

### Multi-Channel Notification
```rust
#[async_trait]
pub trait NotificationChannel: Send + Sync {
    async fn send(&self, alert: &AlertNotification) -> Result<()>;
    fn name(&self) -> &str;
    fn supports_severity(&self, severity: AlertSeverity) -> bool;
}

#[derive(Debug, Serialize)]
pub struct AlertNotification {
    pub alert_name: String,
    pub severity: AlertSeverity,
    pub message: String,
    pub value: f64,
    pub threshold: f64,
    pub fired_at: SystemTime,
    pub labels: HashMap<String, String>,
    pub runbook_url: Option<String>,
}

pub struct SlackNotifier {
    webhook_url: String,
    channel: String,
    username: String,
}

#[async_trait]
impl NotificationChannel for SlackNotifier {
    async fn send(&self, alert: &AlertNotification) -> Result<()> {
        let color = match alert.severity {
            AlertSeverity::Critical => "danger",
            AlertSeverity::Warning => "warning",
            AlertSeverity::Info => "good",
        };
        
        let payload = json!({
            "channel": self.channel,
            "username": self.username,
            "attachments": [{
                "color": color,
                "title": format!("{} Alert: {}", alert.severity, alert.alert_name),
                "text": alert.message,
                "fields": [
                    {
                        "title": "Current Value",
                        "value": alert.value,
                        "short": true
                    },
                    {
                        "title": "Threshold",
                        "value": alert.threshold,
                        "short": true
                    },
                    {
                        "title": "Fired At",
                        "value": format_time(alert.fired_at),
                        "short": true
                    }
                ],
                "actions": [{
                    "type": "button",
                    "text": "View Runbook",
                    "url": alert.runbook_url.as_ref().unwrap_or(&"#".to_string())
                }]
            }]
        });
        
        let client = reqwest::Client::new();
        client
            .post(&self.webhook_url)
            .json(&payload)
            .send()
            .await?;
            
        Ok(())
    }
    
    fn name(&self) -> &str {
        "slack"
    }
    
    fn supports_severity(&self, _severity: AlertSeverity) -> bool {
        true
    }
}
```

### Email Notification
```rust
use lettre::{SmtpTransport, Transport, Message};

pub struct EmailNotifier {
    transport: SmtpTransport,
    from: String,
    to: Vec<String>,
}

#[async_trait]
impl NotificationChannel for EmailNotifier {
    async fn send(&self, alert: &AlertNotification) -> Result<()> {
        for recipient in &self.to {
            let email = Message::builder()
                .from(self.from.parse()?)
                .to(recipient.parse()?)
                .subject(format!("[{}] Alert: {}", alert.severity, alert.alert_name))
                .body(self.format_email_body(alert))?;
                
            self.transport.send(&email)?;
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "email"
    }
    
    fn supports_severity(&self, severity: AlertSeverity) -> bool {
        matches!(severity, AlertSeverity::Critical | AlertSeverity::Warning)
    }
}

impl EmailNotifier {
    fn format_email_body(&self, alert: &AlertNotification) -> String {
        format!(
            r#"
Alert Details:
=============

Alert Name: {}
Severity: {}
Current Value: {}
Threshold: {}
Fired At: {}

Description:
{}

Labels:
{}

If you need help resolving this alert, please refer to the runbook:
{}

This is an automated message from the monitoring system.
            "#,
            alert.alert_name,
            alert.severity,
            alert.value,
            alert.threshold,
            format_time(alert.fired_at),
            alert.message,
            format_labels(&alert.labels),
            alert.runbook_url.as_ref().unwrap_or(&"Not available".to_string())
        )
    }
}
```

## System Health Rules

### Agent Health Monitoring
```rust
pub fn create_agent_health_rules() -> Vec<AlertRule> {
    vec![
        AlertRule {
            name: "agent_high_failure_rate".to_string(),
            condition: AlertCondition::GreaterThan,
            threshold: 0.1, // 10% failure rate
            duration: Duration::from_secs(300), // 5 minutes
            severity: AlertSeverity::Warning,
            labels: hashmap! {
                "component".to_string() => "agent".to_string(),
                "type".to_string() => "failure_rate".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "Agent failure rate is high".to_string(),
                "description".to_string() => "Agent {{$labels.agent_id}} has a failure rate of {{$value}}% over the last 5 minutes".to_string(),
                "runbook_url".to_string() => "https://docs.example.com/runbooks/agent-failure".to_string(),
            },
        },
        
        AlertRule {
            name: "agent_task_queue_full".to_string(),
            condition: AlertCondition::GreaterThan,
            threshold: 1000.0, // 1000 pending tasks
            duration: Duration::from_secs(60),
            severity: AlertSeverity::Critical,
            labels: hashmap! {
                "component".to_string() => "agent".to_string(),
                "type".to_string() => "queue_depth".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "Agent task queue is full".to_string(),
                "description".to_string() => "Agent {{$labels.agent_id}} has {{$value}} pending tasks".to_string(),
                "runbook_url".to_string() => "https://docs.example.com/runbooks/queue-full".to_string(),
            },
        },
        
        AlertRule {
            name: "agent_memory_usage_high".to_string(),
            condition: AlertCondition::GreaterThan,
            threshold: 0.9, // 90% memory usage
            duration: Duration::from_secs(600), // 10 minutes
            severity: AlertSeverity::Warning,
            labels: hashmap! {
                "component".to_string() => "agent".to_string(),
                "type".to_string() => "memory".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "Agent memory usage is high".to_string(),
                "description".to_string() => "Agent {{$labels.agent_id}} is using {{$value}}% of available memory".to_string(),
            },
        },
    ]
}
```

### Performance Rules
```rust
pub fn create_performance_rules() -> Vec<AlertRule> {
    vec![
        AlertRule {
            name: "response_time_high".to_string(),
            condition: AlertCondition::GreaterThan,
            threshold: 5.0, // 5 seconds
            duration: Duration::from_secs(180), // 3 minutes
            severity: AlertSeverity::Warning,
            labels: hashmap! {
                "component".to_string() => "api".to_string(),
                "type".to_string() => "latency".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "API response time is high".to_string(),
                "description".to_string() => "95th percentile response time is {{$value}}s for endpoint {{$labels.endpoint}}".to_string(),
            },
        },
        
        AlertRule {
            name: "database_connections_exhausted".to_string(),
            condition: AlertCondition::GreaterThan,
            threshold: 0.95, // 95% of pool used
            duration: Duration::from_secs(60),
            severity: AlertSeverity::Critical,
            labels: hashmap! {
                "component".to_string() => "database".to_string(),
                "type".to_string() => "connections".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "Database connection pool nearly exhausted".to_string(),
                "description".to_string() => "{{$value}}% of database connections are in use".to_string(),
            },
        },
        
        AlertRule {
            name: "cache_hit_rate_low".to_string(),
            condition: AlertCondition::LessThan,
            threshold: 0.8, // 80% hit rate
            duration: Duration::from_secs(300),
            severity: AlertSeverity::Warning,
            labels: hashmap! {
                "component".to_string() => "cache".to_string(),
                "type".to_string() => "hit_rate".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "Cache hit rate is low".to_string(),
                "description".to_string() => "Cache {{$labels.cache_name}} hit rate is {{$value}}%".to_string(),
            },
        },
    ]
}
```

## Business Logic Rules

### SLA Monitoring
```rust
pub fn create_sla_rules() -> Vec<AlertRule> {
    vec![
        AlertRule {
            name: "sla_availability_breach".to_string(),
            condition: AlertCondition::LessThan,
            threshold: 0.999, // 99.9% availability
            duration: Duration::from_secs(60),
            severity: AlertSeverity::Critical,
            labels: hashmap! {
                "component".to_string() => "sla".to_string(),
                "type".to_string() => "availability".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "SLA availability breach".to_string(),
                "description".to_string() => "Service availability is {{$value}}%, below SLA threshold".to_string(),
                "runbook_url".to_string() => "https://docs.example.com/runbooks/sla-breach".to_string(),
            },
        },
        
        AlertRule {
            name: "task_completion_rate_low".to_string(),
            condition: AlertCondition::LessThan,
            threshold: 0.95, // 95% completion rate
            duration: Duration::from_secs(900), // 15 minutes
            severity: AlertSeverity::Warning,
            labels: hashmap! {
                "component".to_string() => "business".to_string(),
                "type".to_string() => "completion_rate".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "Task completion rate is low".to_string(),
                "description".to_string() => "Only {{$value}}% of tasks are completing successfully".to_string(),
            },
        },
    ]
}
```

### Revenue Impact Rules
```rust
pub fn create_revenue_impact_rules() -> Vec<AlertRule> {
    vec![
        AlertRule {
            name: "revenue_processing_stopped".to_string(),
            condition: AlertCondition::Equals,
            threshold: 0.0, // No revenue processed
            duration: Duration::from_secs(300), // 5 minutes
            severity: AlertSeverity::Critical,
            labels: hashmap! {
                "component".to_string() => "revenue".to_string(),
                "type".to_string() => "processing".to_string(),
                "impact".to_string() => "high".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "Revenue processing has stopped".to_string(),
                "description".to_string() => "No revenue has been processed in the last 5 minutes".to_string(),
                "runbook_url".to_string() => "https://docs.example.com/runbooks/revenue-stopped".to_string(),
            },
        },
        
        AlertRule {
            name: "payment_failure_spike".to_string(),
            condition: AlertCondition::GreaterThan,
            threshold: 0.05, // 5% payment failure rate
            duration: Duration::from_secs(60),
            severity: AlertSeverity::Critical,
            labels: hashmap! {
                "component".to_string() => "payments".to_string(),
                "type".to_string() => "failure_rate".to_string(),
                "impact".to_string() => "high".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => "Payment failure rate is high".to_string(),
                "description".to_string() => "{{$value}}% of payments are failing".to_string(),
            },
        },
    ]
}
```

## Anomaly Detection

### Statistical Anomaly Detection
```rust
pub struct AnomalyDetector {
    window_size: usize,
    sensitivity: f64,
    history: Arc<RwLock<HashMap<String, VecDeque<f64>>>>,
}

impl AnomalyDetector {
    pub fn is_anomaly(&self, metric_name: &str, value: f64) -> bool {
        let mut history = self.history.write().unwrap();
        let metric_history = history
            .entry(metric_name.to_string())
            .or_insert_with(VecDeque::new);
            
        // Add new value
        metric_history.push_back(value);
        
        // Keep only window_size values
        while metric_history.len() > self.window_size {
            metric_history.pop_front();
        }
        
        // Need at least 10 data points for anomaly detection
        if metric_history.len() < 10 {
            return false;
        }
        
        // Calculate statistics
        let mean = metric_history.iter().sum::<f64>() / metric_history.len() as f64;
        let variance = metric_history
            .iter()
            .map(|&x| (x - mean).powi(2))
            .sum::<f64>() / (metric_history.len() - 1) as f64;
        let std_dev = variance.sqrt();
        
        // Check if current value is outside acceptable range
        let z_score = (value - mean) / std_dev;
        z_score.abs() > self.sensitivity
    }
    
    pub fn create_anomaly_rule(&self, metric_name: &str) -> AlertRule {
        AlertRule {
            name: format!("{}_anomaly", metric_name),
            condition: AlertCondition::ChangePercent(self.sensitivity),
            threshold: 0.0, // Will be calculated dynamically
            duration: Duration::from_secs(60),
            severity: AlertSeverity::Warning,
            labels: hashmap! {
                "component".to_string() => "anomaly_detection".to_string(),
                "metric".to_string() => metric_name.to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => format!("Anomaly detected in {}", metric_name),
                "description".to_string() => "Statistical anomaly detected in metric {{$labels.metric}}".to_string(),
            },
        }
    }
}
```

### Machine Learning Based Detection
```rust
use linfa::prelude::*;
use linfa_clustering::Dbscan;

pub struct MLAnomalyDetector {
    model: Option<Dbscan<f64>>,
    training_data: Vec<Vec<f64>>,
    feature_extractors: Vec<Box<dyn FeatureExtractor>>,
}

trait FeatureExtractor: Send + Sync {
    fn extract(&self, metrics: &MetricSnapshot) -> Vec<f64>;
    fn name(&self) -> &str;
}

impl MLAnomalyDetector {
    pub async fn train(&mut self) {
        if self.training_data.len() < 100 {
            return; // Need more training data
        }
        
        let dataset = Array2::from_shape_vec(
            (self.training_data.len(), self.training_data[0].len()),
            self.training_data.iter().flatten().copied().collect(),
        ).unwrap();
        
        let model = Dbscan::params(2)
            .min_points(5)
            .tolerance(0.5)
            .transform(dataset)
            .unwrap();
            
        self.model = Some(model);
    }
    
    pub fn detect_anomaly(&self, metrics: &MetricSnapshot) -> Option<AnomalyReport> {
        let model = self.model.as_ref()?;
        
        let features: Vec<f64> = self.feature_extractors
            .iter()
            .flat_map(|extractor| extractor.extract(metrics))
            .collect();
            
        // Check if point is an outlier
        let is_anomaly = self.is_outlier(&features);
        
        if is_anomaly {
            Some(AnomalyReport {
                timestamp: SystemTime::now(),
                features: features.clone(),
                confidence: self.calculate_confidence(&features),
                affected_metrics: self.identify_affected_metrics(&features),
            })
        } else {
            None
        }
    }
}
```

## Alert Escalation

### Escalation Policy
```rust
pub struct EscalationPolicy {
    levels: Vec<EscalationLevel>,
}

struct EscalationLevel {
    delay: Duration,
    channels: Vec<String>,
    required_ack: bool,
}

impl EscalationPolicy {
    pub async fn escalate(&self, alert: &ActiveAlert) {
        let elapsed = alert.fired_at.elapsed().unwrap_or_default();
        
        for (i, level) in self.levels.iter().enumerate() {
            if elapsed >= level.delay {
                if !level.required_ack || !self.is_acknowledged(&alert.rule_name) {
                    self.notify_level(alert, i).await;
                }
            }
        }
    }
    
    async fn notify_level(&self, alert: &ActiveAlert, level: usize) {
        let level_config = &self.levels[level];
        
        for channel_name in &level_config.channels {
            if let Some(channel) = self.get_channel(channel_name) {
                let notification = AlertNotification {
                    alert_name: alert.rule_name.clone(),
                    severity: AlertSeverity::Critical, // Escalated alerts are critical
                    message: format!(
                        "ESCALATED (Level {}): {}",
                        level + 1,
                        alert.rule_name
                    ),
                    value: alert.value,
                    threshold: 0.0, // Will be filled from rule
                    fired_at: alert.fired_at,
                    labels: HashMap::new(),
                    runbook_url: None,
                };
                
                if let Err(e) = channel.send(&notification).await {
                    log::error!("Failed to send escalation notification: {}", e);
                }
            }
        }
    }
}
```

## Alert Grouping and Deduplication

### Alert Grouper
```rust
pub struct AlertGrouper {
    groups: Arc<RwLock<HashMap<String, AlertGroup>>>,
    grouping_rules: Vec<GroupingRule>,
}

struct AlertGroup {
    key: String,
    alerts: Vec<String>,
    first_alert: SystemTime,
    last_update: SystemTime,
    notification_sent: bool,
}

struct GroupingRule {
    name: String,
    matcher: Box<dyn Fn(&AlertRule) -> Option<String> + Send + Sync>,
    max_age: Duration,
    max_size: usize,
}

impl AlertGrouper {
    pub async fn add_alert(&self, rule: &AlertRule) -> Option<String> {
        for grouping_rule in &self.grouping_rules {
            if let Some(group_key) = (grouping_rule.matcher)(rule) {
                return self.add_to_group(group_key, rule).await;
            }
        }
        
        None // No grouping applied
    }
    
    async fn add_to_group(&self, group_key: String, rule: &AlertRule) -> Option<String> {
        let mut groups = self.groups.write().await;
        let now = SystemTime::now();
        
        let group = groups
            .entry(group_key.clone())
            .or_insert_with(|| AlertGroup {
                key: group_key.clone(),
                alerts: Vec::new(),
                first_alert: now,
                last_update: now,
                notification_sent: false,
            });
            
        group.alerts.push(rule.name.clone());
        group.last_update = now;
        
        // Check if group should be notified
        if !group.notification_sent && 
           (group.alerts.len() >= 3 || 
            group.first_alert.elapsed().unwrap_or_default() >= Duration::from_secs(60)) {
            group.notification_sent = true;
            return Some(group_key);
        }
        
        None
    }
}

// Example grouping rules
pub fn service_grouping_rule() -> GroupingRule {
    GroupingRule {
        name: "service".to_string(),
        matcher: Box::new(|rule| {
            rule.labels.get("service").cloned()
        }),
        max_age: Duration::from_secs(300),
        max_size: 10,
    }
}

pub fn severity_grouping_rule() -> GroupingRule {
    GroupingRule {
        name: "severity".to_string(),
        matcher: Box::new(|rule| {
            Some(format!("severity_{:?}", rule.severity))
        }),
        max_age: Duration::from_secs(600),
        max_size: 20,
    }
}
```

## Health Check Integration

### Service Health Alerting
```rust
pub struct HealthCheckAlerter {
    checks: HashMap<String, Box<dyn HealthCheck>>,
    alert_manager: Arc<AlertManager>,
}

impl HealthCheckAlerter {
    pub async fn monitor_health(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(30));
        
        loop {
            interval.tick().await;
            
            for (service_name, health_check) in &self.checks {
                match health_check.check().await {
                    HealthStatus::Healthy => {
                        self.resolve_health_alert(service_name).await;
                    }
                    HealthStatus::Degraded => {
                        self.fire_health_alert(service_name, AlertSeverity::Warning).await;
                    }
                    HealthStatus::Unhealthy => {
                        self.fire_health_alert(service_name, AlertSeverity::Critical).await;
                    }
                }
            }
        }
    }
    
    async fn fire_health_alert(&self, service: &str, severity: AlertSeverity) {
        let rule = AlertRule {
            name: format!("{}_health_check", service),
            condition: AlertCondition::Equals,
            threshold: 0.0,
            duration: Duration::from_secs(0),
            severity,
            labels: hashmap! {
                "service".to_string() => service.to_string(),
                "check_type".to_string() => "health".to_string(),
            },
            annotations: hashmap! {
                "summary".to_string() => format!("Service {} health check failed", service),
                "description".to_string() => format!("Health check for service {} is reporting {}", service, severity),
            },
        };
        
        self.alert_manager.fire_alert(&rule, 1.0).await;
    }
}
```