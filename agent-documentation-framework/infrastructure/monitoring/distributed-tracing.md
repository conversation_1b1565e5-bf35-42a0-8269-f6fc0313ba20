# Distributed Tracing

## OpenTelemetry Setup

### Basic Tracer Configuration
```rust
use opentelemetry::{
    global,
    sdk::{
        export::trace::stdout,
        propagation::TraceContextPropagator,
        trace::{self, RandomIdGenerator, Sam<PERSON>},
        Resource,
    },
    trace::{<PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, Span, StatusC<PERSON>},
    KeyValue,
};
use opentelemetry_otlp::{Protocol, WithExportConfig};
use opentelemetry_semantic_conventions as semcov;

pub fn init_tracing(
    service_name: &str,
    environment: &str,
    otlp_endpoint: &str,
) -> Result<impl Tracer> {
    global::set_text_map_propagator(TraceContextPropagator::new());
    
    let resource = Resource::new(vec![
        KeyValue::new(semcov::resource::SERVICE_NAME, service_name.to_string()),
        KeyValue::new(semcov::resource::SERVICE_VERSION, env!("CARGO_PKG_VERSION")),
        KeyValue::new("service.environment", environment.to_string()),
        KeyValue::new(semcov::resource::DEPLOYMENT_ENVIRONMENT, environment.to_string()),
    ]);
    
    let tracer = opentelemetry_otlp::new_pipeline()
        .tracing()
        .with_exporter(
            opentelemetry_otlp::new_exporter()
                .tonic()
                .with_endpoint(otlp_endpoint)
                .with_protocol(Protocol::Grpc)
                .with_timeout(Duration::from_secs(3)),
        )
        .with_trace_config(
            trace::config()
                .with_sampler(Sampler::TraceIdRatioBased(0.1))
                .with_id_generator(RandomIdGenerator::default())
                .with_max_events_per_span(64)
                .with_max_attributes_per_span(16)
                .with_max_links_per_span(16)
                .with_resource(resource),
        )
        .install_batch(opentelemetry::runtime::Tokio)?;
    
    Ok(tracer)
}
```

### Span Management
```rust
use opentelemetry::trace::{SpanKind, Link, SpanContext};
use std::collections::HashMap;

pub struct SpanBuilder {
    tracer: Box<dyn Tracer>,
    name: String,
    kind: SpanKind,
    attributes: Vec<KeyValue>,
    links: Vec<Link>,
}

impl SpanBuilder {
    pub fn new(tracer: Box<dyn Tracer>, name: impl Into<String>) -> Self {
        Self {
            tracer,
            name: name.into(),
            kind: SpanKind::Internal,
            attributes: Vec::new(),
            links: Vec::new(),
        }
    }
    
    pub fn with_kind(mut self, kind: SpanKind) -> Self {
        self.kind = kind;
        self
    }
    
    pub fn with_attributes(mut self, attributes: Vec<KeyValue>) -> Self {
        self.attributes = attributes;
        self
    }
    
    pub fn with_attribute(mut self, key: impl Into<String>, value: impl Into<AttributeValue>) -> Self {
        self.attributes.push(KeyValue::new(key.into(), value.into()));
        self
    }
    
    pub fn with_link(mut self, context: SpanContext, attributes: Vec<KeyValue>) -> Self {
        self.links.push(Link::new(context, attributes));
        self
    }
    
    pub fn start(self) -> Span {
        let mut builder = self.tracer
            .span_builder(self.name)
            .with_kind(self.kind);
            
        for attr in self.attributes {
            builder = builder.with_attributes(vec![attr]);
        }
        
        for link in self.links {
            builder = builder.with_links(vec![link]);
        }
        
        builder.start(&self.tracer)
    }
}
```

## Context Propagation

### HTTP Context Injection/Extraction
```rust
use opentelemetry::propagation::{Injector, Extractor, TextMapPropagator};
use http::{HeaderMap, HeaderValue};

pub struct HeaderInjector<'a> {
    headers: &'a mut HeaderMap,
}

impl<'a> Injector for HeaderInjector<'a> {
    fn set(&mut self, key: &str, value: String) {
        if let Ok(header_value) = HeaderValue::from_str(&value) {
            self.headers.insert(key, header_value);
        }
    }
}

pub struct HeaderExtractor<'a> {
    headers: &'a HeaderMap,
}

impl<'a> Extractor for HeaderExtractor<'a> {
    fn get(&self, key: &str) -> Option<&str> {
        self.headers
            .get(key)
            .and_then(|v| v.to_str().ok())
    }
    
    fn keys(&self) -> Vec<&str> {
        self.headers
            .keys()
            .filter_map(|k| k.as_str())
            .collect()
    }
}

pub fn inject_context(headers: &mut HeaderMap) {
    let propagator = global::get_text_map_propagator(|prop| prop.clone());
    propagator.inject_context(
        &Context::current(),
        &mut HeaderInjector { headers },
    );
}

pub fn extract_context(headers: &HeaderMap) -> Context {
    let propagator = global::get_text_map_propagator(|prop| prop.clone());
    propagator.extract(&HeaderExtractor { headers })
}
```

### Async Context Management
```rust
use opentelemetry::Context;
use tokio::task;

pub async fn with_span_async<F, T>(span: Span, f: F) -> T
where
    F: Future<Output = T>,
{
    let cx = Context::current_with_span(span);
    let _guard = cx.attach();
    f.await
}

pub fn spawn_with_trace_context<F>(f: F) -> task::JoinHandle<F::Output>
where
    F: Future + Send + 'static,
    F::Output: Send + 'static,
{
    let context = Context::current();
    
    task::spawn(async move {
        let _guard = context.attach();
        f.await
    })
}
```

## Agent Tracing

### Agent Activity Spans
```rust
pub struct AgentTracer {
    tracer: Box<dyn Tracer>,
    agent_id: String,
    agent_type: String,
}

impl AgentTracer {
    pub fn trace_task_execution<F, T>(
        &self,
        task_id: &str,
        task_type: &str,
        f: F,
    ) -> Result<T>
    where
        F: FnOnce() -> Result<T>,
    {
        let mut span = self.tracer
            .span_builder(format!("agent.task.{}", task_type))
            .with_kind(SpanKind::Internal)
            .with_attributes(vec![
                KeyValue::new("agent.id", self.agent_id.clone()),
                KeyValue::new("agent.type", self.agent_type.clone()),
                KeyValue::new("task.id", task_id.to_string()),
                KeyValue::new("task.type", task_type.to_string()),
            ])
            .start(&self.tracer);
            
        let _guard = Context::current_with_span(span.clone()).attach();
        
        match f() {
            Ok(result) => {
                span.set_status(StatusCode::Ok, "Task completed successfully");
                Ok(result)
            }
            Err(e) => {
                span.record_error(&e);
                span.set_status(StatusCode::Error, format!("Task failed: {}", e));
                Err(e)
            }
        }
    }
    
    pub fn trace_agent_communication(
        &self,
        target_agent: &str,
        message_type: &str,
    ) -> Span {
        self.tracer
            .span_builder("agent.communication")
            .with_kind(SpanKind::Producer)
            .with_attributes(vec![
                KeyValue::new("agent.source", self.agent_id.clone()),
                KeyValue::new("agent.target", target_agent.to_string()),
                KeyValue::new("message.type", message_type.to_string()),
            ])
            .start(&self.tracer)
    }
}
```

### Multi-Agent Trace Correlation
```rust
pub struct SwarmTracer {
    tracer: Box<dyn Tracer>,
    swarm_id: String,
}

impl SwarmTracer {
    pub fn create_swarm_trace(&self, objective: &str) -> (Span, SpanContext) {
        let span = self.tracer
            .span_builder("swarm.execution")
            .with_kind(SpanKind::Internal)
            .with_attributes(vec![
                KeyValue::new("swarm.id", self.swarm_id.clone()),
                KeyValue::new("swarm.objective", objective.to_string()),
                KeyValue::new("swarm.start_time", Utc::now().to_rfc3339()),
            ])
            .start(&self.tracer);
            
        let context = span.span_context().clone();
        (span, context)
    }
    
    pub fn link_agent_spans(&self, agent_spans: Vec<SpanContext>) -> Span {
        let mut builder = self.tracer
            .span_builder("swarm.coordination")
            .with_kind(SpanKind::Internal);
            
        for (i, span_context) in agent_spans.iter().enumerate() {
            builder = builder.with_links(vec![
                Link::new(
                    span_context.clone(),
                    vec![KeyValue::new("agent.index", i as i64)],
                ),
            ]);
        }
        
        builder.start(&self.tracer)
    }
}
```

## Sampling Strategies

### Adaptive Sampling
```rust
use std::sync::atomic::{AtomicU64, Ordering};

pub struct AdaptiveSampler {
    base_rate: f64,
    current_rate: Arc<AtomicU64>,
    error_rate_threshold: f64,
    performance_threshold: Duration,
}

impl AdaptiveSampler {
    pub fn should_sample(&self, span_context: &SpanContext, attributes: &[KeyValue]) -> bool {
        // Always sample errors
        if attributes.iter().any(|kv| kv.key.as_str() == "error" && kv.value.as_str() == "true") {
            return true;
        }
        
        // Always sample slow operations
        if let Some(duration) = attributes.iter()
            .find(|kv| kv.key.as_str() == "duration.ms")
            .and_then(|kv| kv.value.as_str().parse::<u64>().ok())
        {
            if Duration::from_millis(duration) > self.performance_threshold {
                return true;
            }
        }
        
        // Random sampling based on current rate
        let rate = f64::from_bits(self.current_rate.load(Ordering::Relaxed));
        rand::random::<f64>() < rate
    }
    
    pub fn adjust_rate(&self, metrics: &SamplingMetrics) {
        let current = f64::from_bits(self.current_rate.load(Ordering::Relaxed));
        
        let new_rate = if metrics.error_rate > self.error_rate_threshold {
            // Increase sampling during high error rates
            (current * 2.0).min(1.0)
        } else if metrics.throughput > 10000 {
            // Decrease sampling during high load
            (current * 0.5).max(0.001)
        } else {
            // Return to base rate
            (current * 0.9 + self.base_rate * 0.1)
        };
        
        self.current_rate.store(new_rate.to_bits(), Ordering::Relaxed);
    }
}
```

### Priority-Based Sampling
```rust
pub struct PrioritySampler {
    rules: Vec<SamplingRule>,
}

struct SamplingRule {
    predicate: Box<dyn Fn(&[KeyValue]) -> bool + Send + Sync>,
    sample_rate: f64,
    priority: u32,
}

impl PrioritySampler {
    pub fn should_sample(&self, attributes: &[KeyValue]) -> bool {
        // Sort rules by priority and check each
        let mut rules = self.rules.clone();
        rules.sort_by_key(|r| r.priority);
        
        for rule in rules {
            if (rule.predicate)(attributes) {
                return rand::random::<f64>() < rule.sample_rate;
            }
        }
        
        // Default: don't sample
        false
    }
    
    pub fn add_rule<F>(&mut self, predicate: F, sample_rate: f64, priority: u32)
    where
        F: Fn(&[KeyValue]) -> bool + Send + Sync + 'static,
    {
        self.rules.push(SamplingRule {
            predicate: Box::new(predicate),
            sample_rate,
            priority,
        });
    }
}

// Example rules
pub fn critical_operations(attrs: &[KeyValue]) -> bool {
    attrs.iter().any(|kv| kv.key.as_str() == "operation.critical" && kv.value.as_str() == "true")
}

pub fn user_facing_requests(attrs: &[KeyValue]) -> bool {
    attrs.iter().any(|kv| kv.key.as_str() == "request.type" && kv.value.as_str() == "user")
}
```

## Trace Analysis

### Span Metrics Processor
```rust
pub struct SpanMetricsProcessor {
    metrics: Arc<RwLock<SpanMetrics>>,
    histogram_buckets: Vec<f64>,
}

#[derive(Default)]
struct SpanMetrics {
    operation_counts: HashMap<String, u64>,
    operation_durations: HashMap<String, Vec<f64>>,
    error_counts: HashMap<String, u64>,
}

impl SpanProcessor for SpanMetricsProcessor {
    fn on_start(&self, _span: &mut Span, _cx: &Context) {
        // No action needed on start
    }
    
    fn on_end(&self, span: SpanData) {
        let mut metrics = self.metrics.write().unwrap();
        
        let operation = span.name.clone();
        let duration = span.end_time
            .duration_since(span.start_time)
            .unwrap()
            .as_secs_f64();
        
        // Count operations
        *metrics.operation_counts.entry(operation.clone()).or_insert(0) += 1;
        
        // Track durations
        metrics.operation_durations
            .entry(operation.clone())
            .or_insert_with(Vec::new)
            .push(duration);
        
        // Count errors
        if span.status_code == StatusCode::Error {
            *metrics.error_counts.entry(operation).or_insert(0) += 1;
        }
    }
    
    fn force_flush(&self) -> BoxFuture<'static, ExportResult> {
        Box::pin(async { Ok(()) })
    }
    
    fn shutdown(&self) -> BoxFuture<'static, ExportResult> {
        self.export_metrics();
        Box::pin(async { Ok(()) })
    }
}
```

### Critical Path Analysis
```rust
pub struct TraceAnalyzer {
    spans: HashMap<SpanId, SpanData>,
}

impl TraceAnalyzer {
    pub fn find_critical_path(&self, trace_id: TraceId) -> Vec<SpanId> {
        let trace_spans: Vec<_> = self.spans
            .values()
            .filter(|s| s.span_context.trace_id() == trace_id)
            .collect();
            
        if trace_spans.is_empty() {
            return Vec::new();
        }
        
        // Find root span
        let root = trace_spans.iter()
            .find(|s| s.parent_span_id == SpanId::INVALID)
            .expect("No root span found");
            
        // Build span tree
        let tree = self.build_span_tree(&trace_spans);
        
        // Find critical path (longest duration path)
        self.find_longest_path(&tree, root.span_context.span_id())
    }
    
    fn build_span_tree(&self, spans: &[&SpanData]) -> HashMap<SpanId, Vec<SpanId>> {
        let mut tree = HashMap::new();
        
        for span in spans {
            if span.parent_span_id != SpanId::INVALID {
                tree.entry(span.parent_span_id)
                    .or_insert_with(Vec::new)
                    .push(span.span_context.span_id());
            }
        }
        
        tree
    }
    
    fn find_longest_path(
        &self,
        tree: &HashMap<SpanId, Vec<SpanId>>,
        start: SpanId,
    ) -> Vec<SpanId> {
        let mut longest_path = vec![start];
        let mut max_duration = Duration::ZERO;
        
        if let Some(children) = tree.get(&start) {
            for &child in children {
                let child_path = self.find_longest_path(tree, child);
                let child_duration = self.calculate_path_duration(&child_path);
                
                if child_duration > max_duration {
                    max_duration = child_duration;
                    longest_path = vec![start];
                    longest_path.extend(child_path);
                }
            }
        }
        
        longest_path
    }
    
    fn calculate_path_duration(&self, path: &[SpanId]) -> Duration {
        path.iter()
            .filter_map(|id| self.spans.get(id))
            .map(|span| {
                span.end_time
                    .duration_since(span.start_time)
                    .unwrap_or_default()
            })
            .sum()
    }
}
```

## Trace Storage Optimization

### Span Batching
```rust
pub struct SpanBatcher {
    batch_size: usize,
    batch_timeout: Duration,
    pending: Arc<Mutex<Vec<SpanData>>>,
    exporter: Box<dyn SpanExporter>,
}

impl SpanBatcher {
    pub async fn add_span(&self, span: SpanData) -> Result<()> {
        let should_flush = {
            let mut pending = self.pending.lock().await;
            pending.push(span);
            pending.len() >= self.batch_size
        };
        
        if should_flush {
            self.flush().await?;
        }
        
        Ok(())
    }
    
    pub async fn flush(&self) -> Result<()> {
        let spans = {
            let mut pending = self.pending.lock().await;
            std::mem::take(&mut *pending)
        };
        
        if !spans.is_empty() {
            self.exporter.export(spans).await?;
        }
        
        Ok(())
    }
    
    pub async fn run_periodic_flush(self: Arc<Self>) {
        let mut interval = tokio::time::interval(self.batch_timeout);
        
        loop {
            interval.tick().await;
            
            if let Err(e) = self.flush().await {
                log::error!("Failed to flush spans: {}", e);
            }
        }
    }
}
```

### Trace Compression
```rust
use flate2::write::GzEncoder;
use flate2::Compression;

pub struct CompressedSpanExporter {
    inner: Box<dyn SpanExporter>,
    compression_threshold: usize,
}

impl SpanExporter for CompressedSpanExporter {
    fn export(&mut self, batch: Vec<SpanData>) -> BoxFuture<'static, ExportResult> {
        let serialized = bincode::serialize(&batch).unwrap();
        
        let compressed = if serialized.len() > self.compression_threshold {
            let mut encoder = GzEncoder::new(Vec::new(), Compression::fast());
            encoder.write_all(&serialized).unwrap();
            encoder.finish().unwrap()
        } else {
            serialized
        };
        
        // Send compressed data
        self.inner.export_compressed(compressed)
    }
}
```

## Trace Visualization

### Trace Timeline Generator
```rust
pub struct TraceTimeline {
    spans: Vec<SpanData>,
}

impl TraceTimeline {
    pub fn generate_timeline(&self) -> String {
        let mut timeline = String::new();
        
        // Sort spans by start time
        let mut sorted_spans = self.spans.clone();
        sorted_spans.sort_by_key(|s| s.start_time);
        
        // Find trace boundaries
        let start_time = sorted_spans.first().map(|s| s.start_time).unwrap();
        let end_time = sorted_spans.iter()
            .map(|s| s.end_time)
            .max()
            .unwrap();
            
        let total_duration = end_time.duration_since(start_time).unwrap();
        
        // Generate timeline
        timeline.push_str(&format!("Trace Timeline ({}ms total)\n", 
            total_duration.as_millis()));
        timeline.push_str("═" * 80);
        timeline.push('\n');
        
        for span in &sorted_spans {
            let offset = span.start_time.duration_since(start_time).unwrap();
            let duration = span.end_time.duration_since(span.start_time).unwrap();
            
            let offset_chars = (offset.as_millis() * 60 / total_duration.as_millis()) as usize;
            let duration_chars = ((duration.as_millis() * 60 / total_duration.as_millis()) as usize).max(1);
            
            timeline.push_str(&" ".repeat(offset_chars));
            timeline.push_str(&"█".repeat(duration_chars));
            timeline.push_str(&format!(" {} ({}ms)\n", span.name, duration.as_millis()));
        }
        
        timeline
    }
}
```

## Integration Examples

### Axum Middleware
```rust
use axum::{
    middleware::{self, Next},
    response::Response,
    extract::MatchedPath,
    http::Request,
};

pub async fn tracing_middleware<B>(
    req: Request<B>,
    next: Next<B>,
) -> Result<Response, StatusCode> {
    let tracer = global::tracer("http_server");
    
    // Extract trace context from headers
    let parent_context = extract_context(req.headers());
    let _guard = parent_context.attach();
    
    // Start span
    let path = req
        .extensions()
        .get::<MatchedPath>()
        .map(|p| p.as_str())
        .unwrap_or("unknown");
        
    let mut span = tracer
        .span_builder(format!("{} {}", req.method(), path))
        .with_kind(SpanKind::Server)
        .with_attributes(vec![
            KeyValue::new("http.method", req.method().to_string()),
            KeyValue::new("http.target", path.to_string()),
            KeyValue::new("http.scheme", "http"),
        ])
        .start(&tracer);
        
    let _span_guard = Context::current_with_span(span.clone()).attach();
    
    // Process request
    let response = next.run(req).await;
    
    // Record response
    span.set_attribute(KeyValue::new("http.status_code", response.status().as_u16() as i64));
    
    if !response.status().is_success() {
        span.set_status(StatusCode::Error, "HTTP error");
    }
    
    Ok(response)
}
```

### Database Query Tracing
```rust
pub trait TracedDatabase {
    async fn query_with_trace<T, F>(&self, query: &str, f: F) -> Result<T>
    where
        F: FnOnce() -> BoxFuture<'static, Result<T>>;
}

impl TracedDatabase for DatabaseConnection {
    async fn query_with_trace<T, F>(&self, query: &str, f: F) -> Result<T>
    where
        F: FnOnce() -> BoxFuture<'static, Result<T>>,
    {
        let tracer = global::tracer("database");
        
        let mut span = tracer
            .span_builder("db.query")
            .with_kind(SpanKind::Client)
            .with_attributes(vec![
                KeyValue::new("db.system", "postgresql"),
                KeyValue::new("db.statement", truncate_query(query)),
                KeyValue::new("db.operation", extract_operation(query)),
            ])
            .start(&tracer);
            
        let _guard = Context::current_with_span(span.clone()).attach();
        
        match f().await {
            Ok(result) => {
                span.set_status(StatusCode::Ok, "Query successful");
                Ok(result)
            }
            Err(e) => {
                span.record_error(&e);
                span.set_status(StatusCode::Error, "Query failed");
                Err(e)
            }
        }
    }
}
```