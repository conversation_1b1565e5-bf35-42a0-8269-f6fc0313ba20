# Caching Infrastructure Implementation Details

## Overview
Claude-Flow implements multiple caching layers to optimize performance, reduce API calls, and manage memory efficiently.

## TTL Map - Time-Based Caching

### Core Architecture
```typescript
interface TTLItem<T> {
  value: T;
  expiry: number;        // Expiration timestamp
  createdAt: number;     // Creation timestamp
  accessCount: number;   // Number of accesses
  lastAccessedAt: number;// Last access timestamp
}
```

### Key Features

#### 1. Automatic Expiration
- Items expire after configured TTL
- Background cleanup process
- On-access expiration check
- Configurable cleanup intervals

#### 2. LRU Eviction
```typescript
private evictLRU(): void {
  let lruKey: K | undefined;
  let lruTime = Infinity;
  
  // Find least recently used item
  for (const [key, item] of this.items) {
    if (item.lastAccessedAt < lruTime) {
      lruTime = item.lastAccessedAt;
      lruKey = key;
    }
  }
  
  if (lruKey !== undefined) {
    this.items.delete(lruKey);
    this.stats.evictions++;
  }
}
```

#### 3. Access Pattern Tracking
- Access count per item
- Last access timestamp
- Hit/miss rate calculation
- Performance statistics

#### 4. Flexible TTL Management
- Default TTL configuration
- Per-item TTL override
- TTL refresh on access (touch)
- Remaining TTL queries

### Advanced Operations

#### Size-Limited Cache
```typescript
if (this.maxSize && this.items.size >= this.maxSize && !this.items.has(key)) {
  this.evictLRU();  // Make room for new item
}
```

#### Batch Operations
```typescript
// Get all valid entries
entries(): Array<[K, V]> {
  const now = Date.now();
  return Array.from(this.items)
    .filter(([_, item]) => now <= item.expiry)
    .map(([key, item]) => [key, item.value]);
}
```

#### Statistics and Monitoring
```typescript
getStats() {
  return {
    hits: this.stats.hits,
    misses: this.stats.misses,
    evictions: this.stats.evictions,
    expirations: this.stats.expirations,
    size: this.items.size,
    hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
  };
}
```

## Memory Cache - LRU Implementation

### Architecture
```typescript
interface CacheEntry {
  data: MemoryEntry;
  size: number;         // Calculated size in bytes
  lastAccessed: number; // For LRU eviction
  dirty: boolean;       // Needs persistence
}
```

### Memory Management

#### 1. Size Calculation
```typescript
private calculateSize(entry: MemoryEntry): number {
  let size = 0;
  
  // String fields (UTF-16)
  size += entry.id.length * 2;
  size += entry.content.length * 2;
  
  // Arrays and objects
  size += JSON.stringify(entry.context).length * 2;
  
  // Fixed overhead
  size += 100;
  
  return size;
}
```

#### 2. Eviction Strategy
- Size-based eviction when approaching limit
- LRU (Least Recently Used) algorithm
- Dirty entry protection
- Batch eviction for efficiency

#### 3. Prefix-Based Queries
```typescript
getByPrefix(prefix: string): MemoryEntry[] {
  const results: MemoryEntry[] = [];
  
  for (const [id, entry] of this.cache) {
    if (id.startsWith(prefix)) {
      entry.lastAccessed = Date.now();
      results.push(entry.data);
    }
  }
  
  return results;
}
```

### Dirty Tracking System

#### Purpose
- Track modified entries
- Batch persistence operations
- Prevent data loss
- Optimize write operations

#### Implementation
```typescript
getDirtyEntries(): MemoryEntry[] {
  return Array.from(this.cache.values())
    .filter(entry => entry.dirty)
    .map(entry => entry.data);
}

markClean(ids: string[]): void {
  for (const id of ids) {
    const entry = this.cache.get(id);
    if (entry) {
      entry.dirty = false;
    }
  }
}
```

## Optimized Executor Cache Integration

### Result Caching
```typescript
// Cache key generation
private getTaskCacheKey(task: TaskDefinition): string {
  return `${task.type}-${task.description}-${JSON.stringify(task.metadata || {})}`;
}

// Cache check before execution
if (this.config.caching?.enabled) {
  const cached = this.resultCache.get(taskKey);
  if (cached) {
    this.metrics.cacheHits++;
    return cached;
  }
  this.metrics.cacheMisses++;
}

// Cache successful results
if (this.config.caching?.enabled && executionResult.success) {
  this.resultCache.set(taskKey, taskResult);
}
```

### Cache Configuration
```typescript
interface CachingConfig {
  enabled: boolean;
  ttl: number;        // Time to live in ms
  maxSize: number;    // Maximum entries
}
```

## Circular Buffer - Fixed-Size History

### Purpose
- Maintain execution history
- Fixed memory footprint
- Fast append operations
- Efficient snapshot creation

### Implementation
```typescript
export class CircularBuffer<T> {
  private buffer: (T | undefined)[];
  private head = 0;
  private tail = 0;
  private count = 0;
  
  push(item: T): void {
    this.buffer[this.tail] = item;
    this.tail = (this.tail + 1) % this.capacity;
    
    if (this.count < this.capacity) {
      this.count++;
    } else {
      this.head = (this.head + 1) % this.capacity;
    }
  }
  
  getAll(): T[] {
    const result: T[] = [];
    let index = this.head;
    
    for (let i = 0; i < this.count; i++) {
      const item = this.buffer[index];
      if (item !== undefined) {
        result.push(item);
      }
      index = (index + 1) % this.capacity;
    }
    
    return result;
  }
}
```

## Performance Optimizations

### 1. Background Cleanup
```typescript
private startCleanup(): void {
  this.cleanupTimer = setInterval(() => {
    this.cleanup();
  }, this.cleanupInterval);
}

private cleanup(): void {
  const now = Date.now();
  let cleaned = 0;
  
  for (const [key, item] of this.items) {
    if (now > item.expiry) {
      this.items.delete(key);
      cleaned++;
      this.stats.expirations++;
      
      if (this.onExpire) {
        this.onExpire(key, item.value);
      }
    }
  }
}
```

### 2. Efficient Size Management
- Pre-calculated sizes
- Incremental size updates
- Batch eviction operations
- Memory-aware thresholds

### 3. Access Optimization
- O(1) get/set operations
- Lazy expiration checks
- Efficient prefix searches
- Minimal memory overhead

## Cache Hierarchies

### Multi-Level Caching
```typescript
class MultiLevelCache {
  private l1Cache: TTLMap<string, any>;    // Fast, small
  private l2Cache: MemoryCache;             // Larger, slower
  private l3Cache: PersistentCache;         // Disk-based
  
  async get(key: string): Promise<any> {
    // Check L1
    let value = this.l1Cache.get(key);
    if (value) return value;
    
    // Check L2
    value = this.l2Cache.get(key);
    if (value) {
      this.l1Cache.set(key, value, 300000); // 5 min in L1
      return value;
    }
    
    // Check L3
    value = await this.l3Cache.get(key);
    if (value) {
      this.l2Cache.set(key, value);
      this.l1Cache.set(key, value, 300000);
      return value;
    }
    
    return null;
  }
}
```

### Cache Coherency
- Write-through strategy
- Invalidation propagation
- Version tracking
- Conflict resolution

## Monitoring and Maintenance

### Health Metrics
```typescript
interface CacheHealth {
  hitRate: number;
  evictionRate: number;
  avgAccessTime: number;
  memoryUsage: number;
  fragmentation: number;
}
```

### Maintenance Operations
```typescript
performMaintenance(): void {
  // Log current metrics
  const metrics = this.getMetrics();
  this.logger.debug('Cache maintenance', metrics);
  
  // Defragmentation if needed
  if (this.getFragmentation() > 0.3) {
    this.defragment();
  }
  
  // Preload frequently accessed items
  this.preloadHotItems();
  
  // Update access statistics
  this.updateAccessPatterns();
}
```

## Best Practices

### 1. TTL Configuration
- Set TTL based on data volatility
- Use shorter TTL for frequently changing data
- Implement TTL refresh for active items
- Monitor expiration rates

### 2. Size Management
- Configure max size based on available memory
- Implement gradual eviction (watermarks)
- Monitor eviction rates
- Use appropriate eviction policies

### 3. Cache Warming
- Preload critical data on startup
- Implement predictive caching
- Use background refresh for hot items
- Monitor cold start performance

### 4. Error Handling
- Graceful degradation on cache miss
- Fallback to source on errors
- Log cache failures
- Implement cache bypass for debugging