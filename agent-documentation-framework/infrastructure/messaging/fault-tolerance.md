# Messaging Fault Tolerance

## Connection Resilience

### Automatic Reconnection Strategy
```rust
use backoff::{ExponentialBackoff, SystemClock};

pub struct ResilientConnection {
    servers: Vec<String>,
    current_server: AtomicUsize,
    connection: Arc<RwLock<Option<Connection>>>,
    reconnect_notify: Arc<Notify>,
    metrics: Arc<ConnectionMetrics>,
}

impl ResilientConnection {
    pub async fn connect(&self) -> Result<()> {
        let mut backoff = ExponentialBackoff::<SystemClock>::default();
        backoff.max_elapsed_time = None; // Retry forever
        
        loop {
            let server_idx = self.current_server.load(Ordering::Relaxed);
            let server = &self.servers[server_idx % self.servers.len()];
            
            match self.try_connect(server).await {
                Ok(conn) => {
                    *self.connection.write().await = Some(conn);
                    self.metrics.record_connection_success();
                    self.reconnect_notify.notify_waiters();
                    return Ok(());
                }
                Err(e) => {
                    self.metrics.record_connection_failure(&e);
                    log::warn!("Failed to connect to {}: {}", server, e);
                    
                    // Try next server
                    self.current_server.fetch_add(1, Ordering::Relaxed);
                    
                    // Wait with backoff
                    if let Some(duration) = backoff.next_backoff() {
                        tokio::time::sleep(duration).await;
                    }
                }
            }
        }
    }
    
    async fn try_connect(&self, server: &str) -> Result<Connection> {
        let opts = Options::new()
            .error_callback(|e| {
                log::error!("NATS error: {}", e);
            })
            .disconnect_callback(|| {
                log::warn!("NATS disconnected");
            })
            .reconnect_callback(|| {
                log::info!("NATS reconnected");
            })
            .lame_duck_callback(|| {
                log::warn!("NATS server entering lame duck mode");
            });
            
        timeout(Duration::from_secs(10), nats::aio::connect_with_options(server, opts)).await?
    }
    
    pub async fn with_connection<F, R>(&self, f: F) -> Result<R>
    where
        F: FnOnce(&Connection) -> BoxFuture<'_, Result<R>>,
    {
        loop {
            // Get current connection
            let conn_guard = self.connection.read().await;
            
            if let Some(conn) = conn_guard.as_ref() {
                match f(conn).await {
                    Ok(result) => return Ok(result),
                    Err(e) if is_connection_error(&e) => {
                        drop(conn_guard);
                        
                        // Trigger reconnection
                        *self.connection.write().await = None;
                        self.connect().await?;
                        continue;
                    }
                    Err(e) => return Err(e),
                }
            } else {
                drop(conn_guard);
                
                // Wait for reconnection
                self.reconnect_notify.notified().await;
            }
        }
    }
}
```

### Circuit Breaker for Publishers
```rust
pub struct CircuitBreakerPublisher {
    inner: Arc<Connection>,
    state: Arc<RwLock<CircuitState>>,
    config: CircuitConfig,
    metrics: Arc<CircuitMetrics>,
}

#[derive(Clone)]
struct CircuitConfig {
    failure_threshold: u32,
    success_threshold: u32,
    timeout: Duration,
    half_open_max_calls: u32,
}

#[derive(Debug, Clone)]
enum CircuitState {
    Closed,
    Open { opened_at: Instant },
    HalfOpen { 
        remaining_calls: u32,
        successes: u32,
    },
}

impl CircuitBreakerPublisher {
    pub async fn publish(&self, subject: &str, data: Vec<u8>) -> Result<()> {
        let state = self.state.read().await.clone();
        
        match state {
            CircuitState::Closed => {
                self.try_publish(subject, data).await
            }
            CircuitState::Open { opened_at } => {
                if opened_at.elapsed() >= self.config.timeout {
                    // Transition to half-open
                    *self.state.write().await = CircuitState::HalfOpen {
                        remaining_calls: self.config.half_open_max_calls,
                        successes: 0,
                    };
                    self.try_publish(subject, data).await
                } else {
                    self.metrics.record_circuit_open();
                    Err(anyhow!("Circuit breaker is open"))
                }
            }
            CircuitState::HalfOpen { remaining_calls, .. } => {
                if remaining_calls > 0 {
                    self.try_publish(subject, data).await
                } else {
                    Err(anyhow!("Circuit breaker half-open limit reached"))
                }
            }
        }
    }
    
    async fn try_publish(&self, subject: &str, data: Vec<u8>) -> Result<()> {
        let result = timeout(
            Duration::from_secs(5),
            self.inner.publish(subject, data.clone())
        ).await;
        
        match result {
            Ok(Ok(_)) => {
                self.on_success().await;
                Ok(())
            }
            Ok(Err(e)) | Err(_) => {
                self.on_failure().await;
                Err(anyhow!("Publish failed"))
            }
        }
    }
    
    async fn on_success(&self) {
        let mut state = self.state.write().await;
        
        match *state {
            CircuitState::HalfOpen { remaining_calls, successes } => {
                let new_successes = successes + 1;
                
                if new_successes >= self.config.success_threshold {
                    *state = CircuitState::Closed;
                    self.metrics.record_circuit_closed();
                } else {
                    *state = CircuitState::HalfOpen {
                        remaining_calls: remaining_calls - 1,
                        successes: new_successes,
                    };
                }
            }
            _ => {}
        }
    }
    
    async fn on_failure(&self) {
        let mut state = self.state.write().await;
        
        match *state {
            CircuitState::Closed => {
                *state = CircuitState::Open {
                    opened_at: Instant::now(),
                };
                self.metrics.record_circuit_opened();
            }
            CircuitState::HalfOpen { .. } => {
                *state = CircuitState::Open {
                    opened_at: Instant::now(),
                };
                self.metrics.record_circuit_opened();
            }
            _ => {}
        }
    }
}
```

## Message Durability

### Persistent Message Queue
```rust
pub struct DurableMessageQueue {
    jetstream: Arc<jetstream::Context>,
    local_spool: Arc<RocksDB>,
    inflight: Arc<DashMap<String, InflightMessage>>,
}

struct InflightMessage {
    id: String,
    data: Vec<u8>,
    subject: String,
    attempts: u32,
    next_retry: Instant,
}

impl DurableMessageQueue {
    pub async fn send(&self, subject: &str, data: Vec<u8>) -> Result<String> {
        let msg_id = Uuid::new_v4().to_string();
        
        // First, persist locally
        self.persist_message(&msg_id, subject, &data)?;
        
        // Then try to send
        match self.try_send_immediate(subject, &data, &msg_id).await {
            Ok(_) => {
                // Success - remove from local storage
                self.local_spool.delete(&msg_id)?;
                Ok(msg_id)
            }
            Err(e) => {
                // Failed - keep in local storage for retry
                log::warn!("Failed to send message {}: {}", msg_id, e);
                
                // Add to inflight for retry
                self.inflight.insert(msg_id.clone(), InflightMessage {
                    id: msg_id.clone(),
                    data,
                    subject: subject.to_string(),
                    attempts: 1,
                    next_retry: Instant::now() + Duration::from_secs(1),
                });
                
                Ok(msg_id)
            }
        }
    }
    
    fn persist_message(&self, id: &str, subject: &str, data: &[u8]) -> Result<()> {
        let record = MessageRecord {
            id: id.to_string(),
            subject: subject.to_string(),
            data: data.to_vec(),
            created_at: Utc::now(),
        };
        
        let serialized = bincode::serialize(&record)?;
        self.local_spool.put(id, serialized)?;
        
        Ok(())
    }
    
    pub async fn run_retry_loop(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(1));
        
        loop {
            interval.tick().await;
            
            let now = Instant::now();
            let mut to_retry = Vec::new();
            
            // Find messages ready for retry
            for entry in self.inflight.iter() {
                if entry.next_retry <= now {
                    to_retry.push(entry.key().clone());
                }
            }
            
            // Retry messages
            for msg_id in to_retry {
                if let Some((_, msg)) = self.inflight.remove(&msg_id) {
                    self.retry_message(msg).await;
                }
            }
        }
    }
    
    async fn retry_message(&self, mut msg: InflightMessage) {
        match self.try_send_immediate(&msg.subject, &msg.data, &msg.id).await {
            Ok(_) => {
                // Success - remove from local storage
                let _ = self.local_spool.delete(&msg.id);
                log::info!("Successfully sent message {} after {} attempts", 
                    msg.id, msg.attempts);
            }
            Err(e) => {
                msg.attempts += 1;
                
                if msg.attempts > 10 {
                    // Max retries exceeded - move to DLQ
                    log::error!("Message {} exceeded max retries", msg.id);
                    self.move_to_dlq(msg).await;
                } else {
                    // Schedule next retry with exponential backoff
                    msg.next_retry = Instant::now() + 
                        Duration::from_secs(2u64.pow(msg.attempts.min(6)));
                    
                    self.inflight.insert(msg.id.clone(), msg);
                }
            }
        }
    }
}
```

### Transaction Log Pattern
```rust
pub struct TransactionalMessaging {
    log: Arc<TransactionLog>,
    publisher: Arc<Connection>,
}

pub struct TransactionLog {
    db: Arc<RocksDB>,
    current_tx: Arc<Mutex<Option<Transaction>>>,
}

pub struct Transaction {
    id: String,
    operations: Vec<Operation>,
    status: TransactionStatus,
}

enum Operation {
    Publish { subject: String, data: Vec<u8> },
    Request { subject: String, data: Vec<u8>, timeout: Duration },
}

impl TransactionalMessaging {
    pub async fn begin_transaction(&self) -> Result<TransactionHandle> {
        let tx_id = Uuid::new_v4().to_string();
        let tx = Transaction {
            id: tx_id.clone(),
            operations: Vec::new(),
            status: TransactionStatus::Active,
        };
        
        self.log.start_transaction(tx).await?;
        
        Ok(TransactionHandle {
            id: tx_id,
            messaging: self.clone(),
        })
    }
}

pub struct TransactionHandle {
    id: String,
    messaging: TransactionalMessaging,
}

impl TransactionHandle {
    pub async fn publish(&mut self, subject: &str, data: Vec<u8>) -> Result<()> {
        self.messaging.log.add_operation(
            &self.id,
            Operation::Publish {
                subject: subject.to_string(),
                data,
            }
        ).await
    }
    
    pub async fn commit(self) -> Result<()> {
        let operations = self.messaging.log.get_operations(&self.id).await?;
        
        // Execute all operations
        for op in operations {
            match op {
                Operation::Publish { subject, data } => {
                    self.messaging.publisher.publish(&subject, data).await?;
                }
                Operation::Request { subject, data, timeout } => {
                    self.messaging.publisher
                        .request_timeout(&subject, data, timeout).await?;
                }
            }
        }
        
        // Mark transaction as committed
        self.messaging.log.commit_transaction(&self.id).await?;
        
        Ok(())
    }
    
    pub async fn rollback(self) -> Result<()> {
        self.messaging.log.rollback_transaction(&self.id).await
    }
}
```

## Cluster High Availability

### Multi-Region Failover
```rust
pub struct MultiRegionMessaging {
    regions: Vec<RegionConfig>,
    active_region: Arc<RwLock<usize>>,
    health_checker: Arc<HealthChecker>,
}

struct RegionConfig {
    name: String,
    servers: Vec<String>,
    priority: u32,
    latency_threshold: Duration,
}

impl MultiRegionMessaging {
    pub async fn connect(&self) -> Result<RegionalConnection> {
        let mut attempts = Vec::new();
        
        // Try regions in priority order
        let mut regions = self.regions.clone();
        regions.sort_by_key(|r| r.priority);
        
        for (idx, region) in regions.iter().enumerate() {
            match self.connect_to_region(region).await {
                Ok(conn) => {
                    *self.active_region.write().await = idx;
                    
                    // Start health monitoring
                    self.health_checker.monitor_region(idx, conn.clone());
                    
                    return Ok(RegionalConnection {
                        connection: conn,
                        region_idx: idx,
                        messaging: self.clone(),
                    });
                }
                Err(e) => {
                    attempts.push((region.name.clone(), e));
                }
            }
        }
        
        Err(anyhow!("Failed to connect to any region: {:?}", attempts))
    }
    
    async fn connect_to_region(&self, region: &RegionConfig) -> Result<Arc<Connection>> {
        let start = Instant::now();
        
        for server in &region.servers {
            match timeout(Duration::from_secs(5), nats::aio::connect(server)).await {
                Ok(Ok(conn)) => {
                    let latency = start.elapsed();
                    
                    if latency > region.latency_threshold {
                        log::warn!("High latency to {}: {:?}", server, latency);
                    }
                    
                    return Ok(Arc::new(conn));
                }
                _ => continue,
            }
        }
        
        Err(anyhow!("Failed to connect to region {}", region.name))
    }
    
    pub async fn handle_region_failure(&self, failed_region: usize) {
        log::warn!("Region {} failed, initiating failover", failed_region);
        
        // Find next available region
        for (idx, region) in self.regions.iter().enumerate() {
            if idx != failed_region {
                if let Ok(conn) = self.connect_to_region(region).await {
                    *self.active_region.write().await = idx;
                    log::info!("Failed over to region {}", region.name);
                    break;
                }
            }
        }
    }
}
```

### Split Brain Prevention
```rust
pub struct ConsensusManager {
    node_id: String,
    peers: Arc<RwLock<HashMap<String, PeerState>>>,
    election_timeout: Duration,
    heartbeat_interval: Duration,
    state: Arc<RwLock<NodeState>>,
}

#[derive(Clone, Copy, PartialEq)]
enum NodeState {
    Follower,
    Candidate,
    Leader,
}

struct PeerState {
    last_heartbeat: Instant,
    connection: Arc<Connection>,
}

impl ConsensusManager {
    pub async fn run(&self) {
        let mut election_timer = tokio::time::interval(self.election_timeout);
        let mut heartbeat_timer = tokio::time::interval(self.heartbeat_interval);
        
        loop {
            tokio::select! {
                _ = election_timer.tick() => {
                    self.check_election_timeout().await;
                }
                _ = heartbeat_timer.tick() => {
                    self.send_heartbeats().await;
                }
            }
        }
    }
    
    async fn check_election_timeout(&self) {
        let state = *self.state.read().await;
        
        if state == NodeState::Follower {
            let peers = self.peers.read().await;
            let now = Instant::now();
            
            // Check if we've received recent heartbeats
            let active_leader = peers.values()
                .any(|peer| now.duration_since(peer.last_heartbeat) < self.election_timeout);
                
            if !active_leader {
                drop(peers);
                self.start_election().await;
            }
        }
    }
    
    async fn start_election(&self) {
        log::info!("Starting leader election");
        
        *self.state.write().await = NodeState::Candidate;
        
        let peers = self.peers.read().await;
        let total_nodes = peers.len() + 1;
        let required_votes = (total_nodes / 2) + 1;
        let mut votes = 1; // Vote for self
        
        // Request votes from peers
        let vote_futures: Vec<_> = peers.values()
            .map(|peer| self.request_vote(&peer.connection))
            .collect();
            
        let results = futures::future::join_all(vote_futures).await;
        
        for result in results {
            if result.unwrap_or(false) {
                votes += 1;
            }
        }
        
        if votes >= required_votes {
            *self.state.write().await = NodeState::Leader;
            log::info!("Elected as leader with {} votes", votes);
        } else {
            *self.state.write().await = NodeState::Follower;
            log::info!("Election failed with {} votes, need {}", votes, required_votes);
        }
    }
    
    async fn request_vote(&self, peer: &Connection) -> Result<bool> {
        let request = VoteRequest {
            node_id: self.node_id.clone(),
            term: self.get_current_term(),
        };
        
        let response = peer
            .request("cluster.vote", serde_json::to_vec(&request)?)
            .await?;
            
        let vote_response: VoteResponse = serde_json::from_slice(&response.data)?;
        Ok(vote_response.vote_granted)
    }
}
```

## Error Recovery Patterns

### Compensating Transactions
```rust
pub struct CompensatingTransactionManager {
    operations: Arc<DashMap<String, CompensatingOperation>>,
}

struct CompensatingOperation {
    forward: Box<dyn Fn() -> BoxFuture<'static, Result<()>> + Send + Sync>,
    compensate: Box<dyn Fn() -> BoxFuture<'static, Result<()>> + Send + Sync>,
    executed: bool,
}

impl CompensatingTransactionManager {
    pub async fn execute_with_compensation<F, C, R>(
        &self,
        operation_id: &str,
        forward: F,
        compensate: C,
    ) -> Result<R>
    where
        F: Fn() -> BoxFuture<'static, Result<R>> + Clone + Send + Sync + 'static,
        C: Fn() -> BoxFuture<'static, Result<()>> + Clone + Send + Sync + 'static,
        R: Send + 'static,
    {
        // Store compensating operation
        self.operations.insert(
            operation_id.to_string(),
            CompensatingOperation {
                forward: Box::new(move || {
                    let f = forward.clone();
                    Box::pin(async move {
                        let _ = f().await;
                        Ok(())
                    })
                }),
                compensate: Box::new(compensate),
                executed: false,
            },
        );
        
        // Execute forward operation
        match forward().await {
            Ok(result) => {
                // Mark as executed
                if let Some(mut op) = self.operations.get_mut(operation_id) {
                    op.executed = true;
                }
                Ok(result)
            }
            Err(e) => {
                // Remove from tracking (wasn't executed)
                self.operations.remove(operation_id);
                Err(e)
            }
        }
    }
    
    pub async fn compensate_all(&self) -> Result<()> {
        let operations: Vec<_> = self.operations.iter()
            .filter(|op| op.executed)
            .map(|op| (op.key().clone(), op.compensate.clone()))
            .collect();
            
        for (id, compensate) in operations.into_iter().rev() {
            log::info!("Compensating operation: {}", id);
            
            if let Err(e) = compensate().await {
                log::error!("Failed to compensate {}: {}", id, e);
            }
            
            self.operations.remove(&id);
        }
        
        Ok(())
    }
}
```

### Idempotent Message Processing
```rust
pub struct IdempotentProcessor {
    processed: Arc<DashMap<String, ProcessedMessage>>,
    ttl: Duration,
}

struct ProcessedMessage {
    result: Vec<u8>,
    processed_at: Instant,
}

impl IdempotentProcessor {
    pub async fn process<F, R>(
        &self,
        message_id: &str,
        processor: F,
    ) -> Result<R>
    where
        F: FnOnce() -> BoxFuture<'static, Result<R>>,
        R: Serialize + DeserializeOwned,
    {
        // Check if already processed
        if let Some(processed) = self.processed.get(message_id) {
            if processed.processed_at.elapsed() < self.ttl {
                // Return cached result
                return Ok(serde_json::from_slice(&processed.result)?);
            } else {
                // Expired, remove it
                drop(processed);
                self.processed.remove(message_id);
            }
        }
        
        // Process message
        let result = processor().await?;
        
        // Cache result
        self.processed.insert(
            message_id.to_string(),
            ProcessedMessage {
                result: serde_json::to_vec(&result)?,
                processed_at: Instant::now(),
            },
        );
        
        // Start cleanup task if needed
        self.cleanup_expired().await;
        
        Ok(result)
    }
    
    async fn cleanup_expired(&self) {
        let now = Instant::now();
        let expired: Vec<_> = self.processed.iter()
            .filter(|entry| now.duration_since(entry.processed_at) > self.ttl)
            .map(|entry| entry.key().clone())
            .collect();
            
        for key in expired {
            self.processed.remove(&key);
        }
    }
}
```

## Health Monitoring

### Comprehensive Health Checks
```rust
pub struct MessagingHealthMonitor {
    checks: Vec<Box<dyn HealthCheck>>,
    status: Arc<RwLock<SystemHealth>>,
}

#[async_trait]
trait HealthCheck: Send + Sync {
    async fn check(&self) -> HealthStatus;
    fn name(&self) -> &str;
}

struct SystemHealth {
    overall: HealthStatus,
    components: HashMap<String, ComponentHealth>,
    last_check: Instant,
}

struct ComponentHealth {
    status: HealthStatus,
    message: Option<String>,
    metrics: HashMap<String, f64>,
}

#[derive(Clone, Copy, PartialEq)]
enum HealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
}

impl MessagingHealthMonitor {
    pub async fn run_health_checks(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(10));
        
        loop {
            interval.tick().await;
            
            let mut component_health = HashMap::new();
            let mut overall = HealthStatus::Healthy;
            
            for check in &self.checks {
                let status = check.check().await;
                
                component_health.insert(
                    check.name().to_string(),
                    ComponentHealth {
                        status,
                        message: None,
                        metrics: HashMap::new(),
                    },
                );
                
                // Update overall status
                match status {
                    HealthStatus::Unhealthy => overall = HealthStatus::Unhealthy,
                    HealthStatus::Degraded if overall == HealthStatus::Healthy => {
                        overall = HealthStatus::Degraded;
                    }
                    _ => {}
                }
            }
            
            // Update system health
            *self.status.write().await = SystemHealth {
                overall,
                components: component_health,
                last_check: Instant::now(),
            };
        }
    }
    
    pub async fn get_health_report(&self) -> HealthReport {
        let health = self.status.read().await;
        
        HealthReport {
            status: health.overall,
            timestamp: Utc::now(),
            components: health.components.clone(),
            uptime: std::process::id(), // Would be actual uptime
        }
    }
}

// Example health check implementation
struct ConnectionHealthCheck {
    connection: Arc<Connection>,
}

#[async_trait]
impl HealthCheck for ConnectionHealthCheck {
    async fn check(&self) -> HealthStatus {
        match timeout(Duration::from_secs(2), self.connection.flush()).await {
            Ok(Ok(_)) => HealthStatus::Healthy,
            Ok(Err(_)) => HealthStatus::Degraded,
            Err(_) => HealthStatus::Unhealthy,
        }
    }
    
    fn name(&self) -> &str {
        "nats_connection"
    }
}
```