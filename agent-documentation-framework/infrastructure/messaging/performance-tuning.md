# Messaging Performance Tuning

## Connection Optimization

### TCP Tuning Parameters
```rust
use nats::Options;
use std::time::Duration;

pub fn create_high_performance_options() -> Options {
    Options::new()
        // Connection settings
        .ping_interval(Duration::from_secs(30))
        .max_reconnects(None) // Infinite reconnects
        .reconnect_buffer_size(256 * 1024 * 1024) // 256MB buffer
        .flush_timeout(Duration::from_millis(100))
        
        // TCP optimizations
        .tcp_nodelay(true) // Disable <PERSON><PERSON>'s algorithm
        .no_echo() // Don't echo our own messages
        
        // TLS optimizations (if using TLS)
        .tls_client_config(
            rustls::ClientConfig::builder()
                .with_safe_defaults()
                .with_custom_certificate_verifier(
                    std::sync::Arc::new(FastCertVerifier::new())
                )
                .with_no_client_auth()
        )
}
```

### Connection Pool Optimization
```rust
pub struct OptimizedConnectionPool {
    connections: Vec<ConnectionSlot>,
    stats: Arc<PoolStats>,
    config: PoolConfig,
}

#[derive(Default)]
struct PoolStats {
    hits: AtomicU64,
    misses: AtomicU64,
    active_connections: AtomicU32,
    total_requests: AtomicU64,
}

struct ConnectionSlot {
    connection: Option<Arc<Connection>>,
    in_use: AtomicBool,
    last_error: RwLock<Option<Instant>>,
    request_count: AtomicU64,
}

impl OptimizedConnectionPool {
    pub async fn acquire(&self) -> Result<PooledConnection> {
        let start = Instant::now();
        self.stats.total_requests.fetch_add(1, Ordering::Relaxed);
        
        // Try to find available connection
        for (idx, slot) in self.connections.iter().enumerate() {
            if !slot.in_use.swap(true, Ordering::Acquire) {
                if let Some(conn) = &slot.connection {
                    self.stats.hits.fetch_add(1, Ordering::Relaxed);
                    slot.request_count.fetch_add(1, Ordering::Relaxed);
                    
                    return Ok(PooledConnection {
                        connection: conn.clone(),
                        pool: self,
                        slot_idx: idx,
                        acquired_at: start,
                    });
                }
            }
        }
        
        self.stats.misses.fetch_add(1, Ordering::Relaxed);
        
        // All connections in use, wait or create new one
        self.wait_or_expand().await
    }
    
    async fn wait_or_expand(&self) -> Result<PooledConnection> {
        let active = self.stats.active_connections.load(Ordering::Relaxed);
        
        if active < self.config.max_size as u32 {
            // Create new connection
            self.create_new_connection().await
        } else {
            // Wait for available connection
            self.wait_for_connection().await
        }
    }
}
```

## Message Batching Strategies

### Smart Batching with Compression
```rust
pub struct SmartBatcher {
    batch_size: usize,
    batch_bytes: usize,
    compression_threshold: usize,
    timeout: Duration,
    pending: Arc<Mutex<PendingBatch>>,
}

struct PendingBatch {
    messages: Vec<BatchMessage>,
    total_bytes: usize,
    created_at: Instant,
}

impl SmartBatcher {
    pub async fn add_message(&self, subject: String, data: Vec<u8>) -> Result<()> {
        let msg_size = data.len();
        let mut batch = self.pending.lock().await;
        
        // Check if adding this message would exceed limits
        if batch.messages.len() >= self.batch_size || 
           batch.total_bytes + msg_size > self.batch_bytes ||
           batch.created_at.elapsed() > self.timeout {
            // Flush current batch
            let current_batch = std::mem::replace(&mut *batch, PendingBatch::default());
            drop(batch);
            
            self.flush_batch(current_batch).await?;
            
            // Add to new batch
            let mut batch = self.pending.lock().await;
            batch.messages.push(BatchMessage { subject, data });
            batch.total_bytes += msg_size;
        } else {
            batch.messages.push(BatchMessage { subject, data });
            batch.total_bytes += msg_size;
        }
        
        Ok(())
    }
    
    async fn flush_batch(&self, batch: PendingBatch) -> Result<()> {
        if batch.messages.is_empty() {
            return Ok(());
        }
        
        // Decide on compression
        let payload = if batch.total_bytes > self.compression_threshold {
            self.compress_batch(&batch)?
        } else {
            self.serialize_batch(&batch)?
        };
        
        // Send as single message
        self.send_batch_message(payload).await
    }
    
    fn compress_batch(&self, batch: &PendingBatch) -> Result<Vec<u8>> {
        use flate2::write::GzEncoder;
        use flate2::Compression;
        
        let serialized = bincode::serialize(&batch.messages)?;
        let mut encoder = GzEncoder::new(Vec::new(), Compression::fast());
        encoder.write_all(&serialized)?;
        
        Ok(encoder.finish()?)
    }
}
```

### Adaptive Batching
```rust
pub struct AdaptiveBatcher {
    stats: Arc<BatchingStats>,
    config: Arc<RwLock<BatchConfig>>,
}

#[derive(Default)]
struct BatchingStats {
    messages_per_second: AtomicU64,
    average_message_size: AtomicU64,
    batch_efficiency: AtomicU64, // 0-100
}

impl AdaptiveBatcher {
    pub async fn optimize_config(&self) {
        let stats = &self.stats;
        let mps = stats.messages_per_second.load(Ordering::Relaxed);
        let avg_size = stats.average_message_size.load(Ordering::Relaxed);
        let efficiency = stats.batch_efficiency.load(Ordering::Relaxed);
        
        let mut config = self.config.write().await;
        
        // High message rate - increase batch size
        if mps > 10000 {
            config.batch_size = (config.batch_size as f64 * 1.2) as usize;
            config.timeout = config.timeout.mul_f64(0.8);
        }
        // Low message rate - decrease batch size
        else if mps < 100 {
            config.batch_size = (config.batch_size as f64 * 0.8).max(10.0) as usize;
            config.timeout = config.timeout.mul_f64(1.2);
        }
        
        // Large messages - reduce batch count
        if avg_size > 1024 * 100 { // 100KB
            config.batch_size = config.batch_size.min(10);
        }
        
        // Low efficiency - adjust timeout
        if efficiency < 70 {
            config.timeout = Duration::from_millis(
                (config.timeout.as_millis() as f64 * 0.9) as u64
            );
        }
    }
}
```

## Subject Hierarchy Optimization

### Efficient Subject Routing
```rust
pub struct SubjectRouter {
    trie: Arc<RwLock<SubjectTrie>>,
    cache: Arc<Mutex<LruCache<String, Vec<HandlerId>>>>,
}

struct SubjectTrie {
    root: TrieNode,
}

struct TrieNode {
    children: HashMap<String, TrieNode>,
    handlers: Vec<HandlerId>,
    wildcard_child: Option<Box<TrieNode>>,
    deep_wildcard_child: Option<Box<TrieNode>>,
}

impl SubjectRouter {
    pub async fn route(&self, subject: &str) -> Vec<HandlerId> {
        // Check cache first
        if let Some(handlers) = self.cache.lock().await.get(subject) {
            return handlers.clone();
        }
        
        // Route through trie
        let trie = self.trie.read().await;
        let handlers = trie.find_handlers(subject);
        
        // Update cache
        self.cache.lock().await.put(subject.to_string(), handlers.clone());
        
        handlers
    }
    
    pub async fn add_subscription(&self, pattern: &str, handler_id: HandlerId) {
        let mut trie = self.trie.write().await;
        trie.insert(pattern, handler_id);
        
        // Clear cache as routing has changed
        self.cache.lock().await.clear();
    }
}

impl SubjectTrie {
    fn find_handlers(&self, subject: &str) -> Vec<HandlerId> {
        let tokens: Vec<&str> = subject.split('.').collect();
        let mut handlers = Vec::new();
        
        self.collect_handlers(&self.root, &tokens, 0, &mut handlers);
        handlers.dedup();
        handlers
    }
    
    fn collect_handlers(
        &self,
        node: &TrieNode,
        tokens: &[&str],
        index: usize,
        handlers: &mut Vec<HandlerId>,
    ) {
        // Add handlers at this node
        handlers.extend(&node.handlers);
        
        if index >= tokens.len() {
            return;
        }
        
        // Exact match
        if let Some(child) = node.children.get(tokens[index]) {
            self.collect_handlers(child, tokens, index + 1, handlers);
        }
        
        // Wildcard match (*)
        if let Some(wildcard) = &node.wildcard_child {
            self.collect_handlers(wildcard, tokens, index + 1, handlers);
        }
        
        // Deep wildcard match (>)
        if let Some(deep_wildcard) = &node.deep_wildcard_child {
            handlers.extend(&deep_wildcard.handlers);
        }
    }
}
```

## Zero-Copy Message Processing

### Zero-Copy Deserialization
```rust
use bytes::{Bytes, BytesMut};
use zerocopy::{AsBytes, FromBytes, Unaligned};

#[repr(C, packed)]
#[derive(AsBytes, FromBytes, Unaligned)]
pub struct MessageHeader {
    version: u8,
    flags: u8,
    message_type: u16,
    payload_length: u32,
    timestamp: u64,
    correlation_id: [u8; 16],
}

pub struct ZeroCopyMessage {
    raw: Bytes,
}

impl ZeroCopyMessage {
    pub fn parse(data: Bytes) -> Result<Self> {
        if data.len() < std::mem::size_of::<MessageHeader>() {
            return Err(anyhow!("Message too small"));
        }
        
        Ok(Self { raw: data })
    }
    
    pub fn header(&self) -> &MessageHeader {
        MessageHeader::ref_from_prefix(&self.raw).unwrap().0
    }
    
    pub fn payload(&self) -> &[u8] {
        let header_size = std::mem::size_of::<MessageHeader>();
        &self.raw[header_size..]
    }
    
    pub fn zero_copy_string(&self, offset: usize, len: usize) -> Result<&str> {
        let payload = self.payload();
        
        if offset + len > payload.len() {
            return Err(anyhow!("String out of bounds"));
        }
        
        std::str::from_utf8(&payload[offset..offset + len])
            .map_err(|e| anyhow!("Invalid UTF-8: {}", e))
    }
}
```

### Memory Pool for Messages
```rust
use crossbeam::queue::ArrayQueue;

pub struct MessagePool {
    small_pool: Arc<ArrayQueue<BytesMut>>, // < 1KB
    medium_pool: Arc<ArrayQueue<BytesMut>>, // < 64KB
    large_pool: Arc<ArrayQueue<BytesMut>>, // < 1MB
}

impl MessagePool {
    pub fn new() -> Self {
        Self {
            small_pool: Arc::new(ArrayQueue::new(1000)),
            medium_pool: Arc::new(ArrayQueue::new(100)),
            large_pool: Arc::new(ArrayQueue::new(10)),
        }
    }
    
    pub fn acquire(&self, size: usize) -> PooledBuffer {
        let (pool, capacity) = if size <= 1024 {
            (&self.small_pool, 1024)
        } else if size <= 64 * 1024 {
            (&self.medium_pool, 64 * 1024)
        } else {
            (&self.large_pool, 1024 * 1024)
        };
        
        let buffer = pool.pop()
            .unwrap_or_else(|| BytesMut::with_capacity(capacity));
            
        PooledBuffer {
            buffer,
            pool: pool.clone(),
            capacity,
        }
    }
}

pub struct PooledBuffer {
    buffer: BytesMut,
    pool: Arc<ArrayQueue<BytesMut>>,
    capacity: usize,
}

impl Drop for PooledBuffer {
    fn drop(&mut self) {
        self.buffer.clear();
        
        // Only return to pool if capacity hasn't grown too much
        if self.buffer.capacity() <= self.capacity * 2 {
            let _ = self.pool.push(self.buffer.split());
        }
    }
}
```

## Parallel Message Processing

### Work Stealing Queue
```rust
use crossbeam::deque::{Injector, Stealer, Worker};

pub struct ParallelProcessor {
    global_queue: Arc<Injector<Message>>,
    workers: Vec<ProcessorWorker>,
}

struct ProcessorWorker {
    local_queue: Worker<Message>,
    stealers: Vec<Stealer<Message>>,
    global: Arc<Injector<Message>>,
}

impl ParallelProcessor {
    pub fn new(num_workers: usize) -> Self {
        let global_queue = Arc::new(Injector::new());
        let mut workers = Vec::with_capacity(num_workers);
        let mut stealers = Vec::with_capacity(num_workers);
        
        // Create workers
        for _ in 0..num_workers {
            let worker = Worker::new_fifo();
            stealers.push(worker.stealer());
            workers.push(ProcessorWorker {
                local_queue: worker,
                stealers: vec![],
                global: global_queue.clone(),
            });
        }
        
        // Give each worker access to others' stealers
        for (i, worker) in workers.iter_mut().enumerate() {
            worker.stealers = stealers.iter()
                .enumerate()
                .filter(|(j, _)| *j != i)
                .map(|(_, s)| s.clone())
                .collect();
        }
        
        Self { global_queue, workers }
    }
    
    pub fn submit(&self, message: Message) {
        self.global_queue.push(message);
    }
    
    pub async fn run_worker(&self, worker_id: usize) {
        let worker = &self.workers[worker_id];
        let backoff = Backoff::new();
        
        loop {
            // Try local queue first
            if let Some(msg) = worker.local_queue.pop() {
                self.process_message(msg).await;
                continue;
            }
            
            // Try stealing from global queue
            if let Ok(msg) = worker.global.steal() {
                self.process_message(msg).await;
                continue;
            }
            
            // Try stealing from other workers
            let stealers = &worker.stealers;
            if let Some(msg) = stealers.iter()
                .map(|s| s.steal())
                .find_map(|s| s.success()) {
                self.process_message(msg).await;
                continue;
            }
            
            // No work available, backoff
            backoff.snooze();
        }
    }
    
    async fn process_message(&self, msg: Message) {
        // Process message...
    }
}
```

## NATS JetStream Optimization

### Stream Configuration for Performance
```rust
pub async fn create_optimized_stream(
    js: &jetstream::Context,
    name: &str,
    subjects: Vec<String>,
) -> Result<()> {
    js.add_stream(&StreamConfig {
        name: name.to_string(),
        subjects,
        
        // Performance settings
        num_replicas: 3,
        storage: StorageType::Memory, // Use memory for speed
        discard: DiscardPolicy::Old,
        
        // Optimize for throughput
        max_msgs_per_subject: 0, // Unlimited
        max_bytes: 10 * 1024 * 1024 * 1024, // 10GB
        max_age: Duration::from_secs(3600), // 1 hour
        
        // Compression for large messages
        compression: Some(StoreCompression::S2),
        
        // Allow direct access
        allow_direct: true,
        mirror_direct: true,
        
        ..Default::default()
    }).await?;
    
    Ok(())
}
```

### Consumer Optimization
```rust
pub async fn create_optimized_consumer(
    js: &jetstream::Context,
    stream: &str,
    name: &str,
) -> Result<()> {
    js.add_consumer(stream, &ConsumerConfig {
        durable_name: Some(name.to_string()),
        
        // Performance settings
        ack_policy: AckPolicy::None, // No acks for max speed
        max_ack_pending: 10000,
        max_deliver: 1, // No retries
        
        // Batch settings
        max_batch: 1000,
        max_bytes: 1024 * 1024, // 1MB batches
        
        // Direct access
        direct: true,
        
        // Efficient delivery
        deliver_policy: DeliverPolicy::New,
        opt_start_time: None,
        
        ..Default::default()
    }).await?;
    
    Ok(())
}
```

## Monitoring and Metrics

### Performance Metrics Collection
```rust
pub struct MessagingMetrics {
    // Latency histograms
    publish_latency: Histogram,
    subscribe_latency: Histogram,
    
    // Throughput counters
    messages_published: Counter,
    messages_received: Counter,
    bytes_sent: Counter,
    bytes_received: Counter,
    
    // Connection metrics
    active_connections: Gauge,
    reconnections: Counter,
    
    // Error tracking
    publish_errors: Counter,
    subscribe_errors: Counter,
}

impl MessagingMetrics {
    pub fn record_publish(&self, start: Instant, bytes: usize, success: bool) {
        let duration = start.elapsed();
        
        self.publish_latency.record(duration.as_secs_f64());
        
        if success {
            self.messages_published.increment(1);
            self.bytes_sent.increment(bytes as u64);
        } else {
            self.publish_errors.increment(1);
        }
    }
    
    pub fn export_prometheus(&self) -> String {
        let mut output = String::new();
        
        // Latency metrics
        output.push_str(&format!(
            "# HELP nats_publish_latency_seconds Publish latency distribution\n\
             # TYPE nats_publish_latency_seconds histogram\n\
             {}\n",
            self.publish_latency.export()
        ));
        
        // Throughput metrics
        output.push_str(&format!(
            "# HELP nats_messages_published_total Total messages published\n\
             # TYPE nats_messages_published_total counter\n\
             nats_messages_published_total {}\n",
            self.messages_published.get()
        ));
        
        output
    }
}
```

### Performance Profiling
```rust
use pprof::{ProfilerGuard, ProfilerGuardBuilder};

pub struct MessageProfiler {
    profiler: Option<ProfilerGuard<'static>>,
    sample_rate: u32,
}

impl MessageProfiler {
    pub fn start_profiling(&mut self) -> Result<()> {
        self.profiler = Some(
            ProfilerGuardBuilder::default()
                .frequency(self.sample_rate)
                .blocklist(&["libc", "libpthread"])
                .build()?
        );
        
        Ok(())
    }
    
    pub fn stop_profiling(&mut self) -> Result<Vec<u8>> {
        if let Some(profiler) = self.profiler.take() {
            let report = profiler.report().build()?;
            
            let mut buf = Vec::new();
            report.flamegraph(&mut buf)?;
            
            Ok(buf)
        } else {
            Err(anyhow!("Profiler not running"))
        }
    }
}
```

## Best Practices Summary

### Do's
1. **Use connection pooling** - Reuse connections across operations
2. **Batch messages** - Reduce network overhead
3. **Enable compression** - For messages > 1KB
4. **Use appropriate subjects** - Leverage wildcards efficiently
5. **Monitor performance** - Track metrics continuously

### Don'ts
1. **Don't create connections per message** - Expensive operation
2. **Don't use deep wildcards carelessly** - Can impact routing performance
3. **Don't ignore backpressure** - Implement flow control
4. **Don't serialize large objects** - Use references and streaming
5. **Don't neglect error handling** - Failed messages need attention