# Data Patterns

## Repository Pattern

### Generic Repository Trait
```rust
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[async_trait]
pub trait Repository<T, ID>: Send + Sync
where
    T: Send + Sync,
    ID: Send + Sync,
{
    type Error: std::error::Error + Send + Sync;
    
    async fn save(&self, entity: &T) -> Result<T, Self::Error>;
    async fn find_by_id(&self, id: &ID) -> Result<Option<T>, Self::Error>;
    async fn find_all(&self) -> Result<Vec<T>, Self::Error>;
    async fn update(&self, entity: &T) -> Result<T, Self::Error>;
    async fn delete(&self, id: &ID) -> Result<bool, Self::Error>;
    async fn exists(&self, id: &ID) -> Result<bool, Self::Error>;
}

#[async_trait]
pub trait QueryableRepository<T>: Repository<T, String>
where
    T: Send + Sync,
{
    async fn find_by_criteria(&self, criteria: QueryCriteria) -> Result<Vec<T>, Self::Error>;
    async fn count_by_criteria(&self, criteria: QueryCriteria) -> Result<i64, Self::Error>;
    async fn find_paged(
        &self,
        criteria: QueryCriteria,
        page: PageRequest,
    ) -> Result<Page<T>, Self::Error>;
}
```

### Agent Repository Implementation
```rust
use sqlx::{PgPool, Row};

pub struct AgentRepository {
    pool: Arc<PgPool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Agent {
    pub id: String,
    pub agent_type: String,
    pub status: AgentStatus,
    pub current_task_id: Option<String>,
    pub capabilities: Vec<String>,
    pub metadata: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum AgentStatus {
    Idle,
    Busy,
    Paused,
    Failed,
    Terminated,
}

#[async_trait]
impl Repository<Agent, String> for AgentRepository {
    type Error = sqlx::Error;
    
    async fn save(&self, agent: &Agent) -> Result<Agent, Self::Error> {
        let mut tx = self.pool.begin().await?;
        
        let saved_agent = sqlx::query_as!(
            Agent,
            r#"
            INSERT INTO agents (id, agent_type, status, current_task_id, capabilities, metadata, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            ON CONFLICT (id) DO UPDATE SET
                agent_type = EXCLUDED.agent_type,
                status = EXCLUDED.status,
                current_task_id = EXCLUDED.current_task_id,
                capabilities = EXCLUDED.capabilities,
                metadata = EXCLUDED.metadata,
                updated_at = EXCLUDED.updated_at
            RETURNING *
            "#,
            agent.id,
            agent.agent_type,
            agent.status as AgentStatus,
            agent.current_task_id,
            &agent.capabilities,
            agent.metadata,
            agent.created_at,
            agent.updated_at
        )
        .fetch_one(&mut *tx)
        .await?;
        
        tx.commit().await?;
        Ok(saved_agent)
    }
    
    async fn find_by_id(&self, id: &String) -> Result<Option<Agent>, Self::Error> {
        let agent = sqlx::query_as!(
            Agent,
            "SELECT * FROM agents WHERE id = $1",
            id
        )
        .fetch_optional(&*self.pool)
        .await?;
        
        Ok(agent)
    }
    
    async fn find_all(&self) -> Result<Vec<Agent>, Self::Error> {
        let agents = sqlx::query_as!(
            Agent,
            "SELECT * FROM agents ORDER BY created_at DESC"
        )
        .fetch_all(&*self.pool)
        .await?;
        
        Ok(agents)
    }
    
    async fn update(&self, agent: &Agent) -> Result<Agent, Self::Error> {
        let updated_agent = sqlx::query_as!(
            Agent,
            r#"
            UPDATE agents 
            SET agent_type = $2, status = $3, current_task_id = $4, 
                capabilities = $5, metadata = $6, updated_at = $7
            WHERE id = $1
            RETURNING *
            "#,
            agent.id,
            agent.agent_type,
            agent.status as AgentStatus,
            agent.current_task_id,
            &agent.capabilities,
            agent.metadata,
            Utc::now()
        )
        .fetch_one(&*self.pool)
        .await?;
        
        Ok(updated_agent)
    }
    
    async fn delete(&self, id: &String) -> Result<bool, Self::Error> {
        let result = sqlx::query!(
            "DELETE FROM agents WHERE id = $1",
            id
        )
        .execute(&*self.pool)
        .await?;
        
        Ok(result.rows_affected() > 0)
    }
    
    async fn exists(&self, id: &String) -> Result<bool, Self::Error> {
        let count = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM agents WHERE id = $1",
            id
        )
        .fetch_one(&*self.pool)
        .await?;
        
        Ok(count.unwrap_or(0) > 0)
    }
}
```

## Unit of Work Pattern

### Transaction Management
```rust
use std::sync::Arc;
use tokio::sync::Mutex;

pub struct UnitOfWork {
    pool: Arc<PgPool>,
    transaction: Option<Arc<Mutex<sqlx::Transaction<'static, sqlx::Postgres>>>>,
    repositories: HashMap<TypeId, Box<dyn Any + Send + Sync>>,
}

impl UnitOfWork {
    pub async fn new(pool: Arc<PgPool>) -> Result<Self> {
        let tx = pool.begin().await?;
        
        Ok(Self {
            pool,
            transaction: Some(Arc::new(Mutex::new(tx))),
            repositories: HashMap::new(),
        })
    }
    
    pub fn get_repository<T: Repository<E, ID> + 'static, E, ID>(&mut self) -> &T
    where
        T: Send + Sync,
        E: Send + Sync,
        ID: Send + Sync,
    {
        let type_id = TypeId::of::<T>();
        
        self.repositories
            .entry(type_id)
            .or_insert_with(|| {
                Box::new(T::new(self.transaction.as_ref().unwrap().clone()))
            })
            .downcast_ref::<T>()
            .unwrap()
    }
    
    pub async fn commit(mut self) -> Result<()> {
        if let Some(tx) = self.transaction.take() {
            let tx = Arc::try_unwrap(tx)
                .map_err(|_| anyhow!("Transaction still in use"))?
                .into_inner();
                
            tx.commit().await?;
        }
        
        Ok(())
    }
    
    pub async fn rollback(mut self) -> Result<()> {
        if let Some(tx) = self.transaction.take() {
            let tx = Arc::try_unwrap(tx)
                .map_err(|_| anyhow!("Transaction still in use"))?
                .into_inner();
                
            tx.rollback().await?;
        }
        
        Ok(())
    }
}
```

### Transactional Operations
```rust
pub struct TaskService {
    pool: Arc<PgPool>,
}

impl TaskService {
    pub async fn create_task_with_assignment(
        &self,
        task: CreateTaskRequest,
        agent_id: String,
    ) -> Result<(Task, Agent)> {
        let mut uow = UnitOfWork::new(self.pool.clone()).await?;
        
        // Get repositories
        let task_repo = uow.get_repository::<TaskRepository, Task, String>();
        let agent_repo = uow.get_repository::<AgentRepository, Agent, String>();
        
        // Create task
        let new_task = Task {
            id: Uuid::new_v4().to_string(),
            title: task.title,
            description: task.description,
            status: TaskStatus::Pending,
            assigned_agent_id: Some(agent_id.clone()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            metadata: task.metadata,
        };
        
        let saved_task = task_repo.save(&new_task).await?;
        
        // Update agent status
        let mut agent = agent_repo
            .find_by_id(&agent_id)
            .await?
            .ok_or_else(|| anyhow!("Agent not found"))?;
            
        agent.status = AgentStatus::Busy;
        agent.current_task_id = Some(saved_task.id.clone());
        agent.updated_at = Utc::now();
        
        let updated_agent = agent_repo.update(&agent).await?;
        
        // Commit transaction
        uow.commit().await?;
        
        Ok((saved_task, updated_agent))
    }
}
```

## Event Sourcing Pattern

### Event Store Implementation
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct Event {
    pub id: String,
    pub aggregate_id: String,
    pub aggregate_type: String,
    pub event_type: String,
    pub event_version: i32,
    pub event_data: serde_json::Value,
    pub metadata: serde_json::Value,
    pub occurred_at: DateTime<Utc>,
}

pub trait AggregateEvent: Serialize + DeserializeOwned {
    fn event_type(&self) -> &'static str;
    fn aggregate_type() -> &'static str;
}

pub struct EventStore {
    pool: Arc<PgPool>,
}

impl EventStore {
    pub async fn append_events(
        &self,
        aggregate_id: &str,
        events: Vec<Box<dyn AggregateEvent>>,
        expected_version: Option<i32>,
    ) -> Result<Vec<Event>> {
        let mut tx = self.pool.begin().await?;
        
        // Check expected version
        if let Some(expected) = expected_version {
            let current_version = self.get_current_version(&mut tx, aggregate_id).await?;
            if current_version != expected {
                return Err(anyhow!(
                    "Concurrency conflict: expected version {}, got {}",
                    expected,
                    current_version
                ));
            }
        }
        
        let mut saved_events = Vec::new();
        
        for event in events {
            let event_record = Event {
                id: Uuid::new_v4().to_string(),
                aggregate_id: aggregate_id.to_string(),
                aggregate_type: event.aggregate_type().to_string(),
                event_type: event.event_type().to_string(),
                event_version: self.get_next_version(&mut tx, aggregate_id).await?,
                event_data: serde_json::to_value(&event)?,
                metadata: json!({}),
                occurred_at: Utc::now(),
            };
            
            let saved_event = sqlx::query_as!(
                Event,
                r#"
                INSERT INTO events (id, aggregate_id, aggregate_type, event_type, event_version, event_data, metadata, occurred_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING *
                "#,
                event_record.id,
                event_record.aggregate_id,
                event_record.aggregate_type,
                event_record.event_type,
                event_record.event_version,
                event_record.event_data,
                event_record.metadata,
                event_record.occurred_at
            )
            .fetch_one(&mut *tx)
            .await?;
            
            saved_events.push(saved_event);
        }
        
        tx.commit().await?;
        Ok(saved_events)
    }
    
    pub async fn get_events(
        &self,
        aggregate_id: &str,
        from_version: Option<i32>,
    ) -> Result<Vec<Event>> {
        let from_version = from_version.unwrap_or(0);
        
        let events = sqlx::query_as!(
            Event,
            r#"
            SELECT * FROM events 
            WHERE aggregate_id = $1 AND event_version > $2
            ORDER BY event_version ASC
            "#,
            aggregate_id,
            from_version
        )
        .fetch_all(&*self.pool)
        .await?;
        
        Ok(events)
    }
    
    async fn get_current_version(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        aggregate_id: &str,
    ) -> Result<i32> {
        let version = sqlx::query_scalar!(
            "SELECT COALESCE(MAX(event_version), 0) FROM events WHERE aggregate_id = $1",
            aggregate_id
        )
        .fetch_one(&mut **tx)
        .await?;
        
        Ok(version.unwrap_or(0))
    }
    
    async fn get_next_version(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        aggregate_id: &str,
    ) -> Result<i32> {
        let current = self.get_current_version(tx, aggregate_id).await?;
        Ok(current + 1)
    }
}
```

### Aggregate Root Pattern
```rust
pub trait AggregateRoot {
    type Event: AggregateEvent;
    type Error: std::error::Error;
    
    fn aggregate_id(&self) -> &str;
    fn version(&self) -> i32;
    fn apply_event(&mut self, event: &Self::Event) -> Result<(), Self::Error>;
    fn get_pending_events(&self) -> &[Self::Event];
    fn mark_events_committed(&mut self);
}

#[derive(Debug)]
pub struct TaskAggregate {
    id: String,
    version: i32,
    title: String,
    description: String,
    status: TaskStatus,
    assigned_agent_id: Option<String>,
    pending_events: Vec<TaskEvent>,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum TaskEvent {
    TaskCreated {
        title: String,
        description: String,
    },
    TaskAssigned {
        agent_id: String,
    },
    TaskStarted {
        started_at: DateTime<Utc>,
    },
    TaskCompleted {
        completed_at: DateTime<Utc>,
        result: serde_json::Value,
    },
    TaskFailed {
        failed_at: DateTime<Utc>,
        error: String,
    },
}

impl AggregateEvent for TaskEvent {
    fn event_type(&self) -> &'static str {
        match self {
            TaskEvent::TaskCreated { .. } => "TaskCreated",
            TaskEvent::TaskAssigned { .. } => "TaskAssigned",
            TaskEvent::TaskStarted { .. } => "TaskStarted",
            TaskEvent::TaskCompleted { .. } => "TaskCompleted",
            TaskEvent::TaskFailed { .. } => "TaskFailed",
        }
    }
    
    fn aggregate_type() -> &'static str {
        "Task"
    }
}

impl AggregateRoot for TaskAggregate {
    type Event = TaskEvent;
    type Error = anyhow::Error;
    
    fn aggregate_id(&self) -> &str {
        &self.id
    }
    
    fn version(&self) -> i32 {
        self.version
    }
    
    fn apply_event(&mut self, event: &Self::Event) -> Result<(), Self::Error> {
        match event {
            TaskEvent::TaskCreated { title, description } => {
                self.title = title.clone();
                self.description = description.clone();
                self.status = TaskStatus::Pending;
            }
            TaskEvent::TaskAssigned { agent_id } => {
                self.assigned_agent_id = Some(agent_id.clone());
            }
            TaskEvent::TaskStarted { .. } => {
                if self.status != TaskStatus::Pending {
                    return Err(anyhow!("Task must be pending to start"));
                }
                self.status = TaskStatus::InProgress;
            }
            TaskEvent::TaskCompleted { .. } => {
                if self.status != TaskStatus::InProgress {
                    return Err(anyhow!("Task must be in progress to complete"));
                }
                self.status = TaskStatus::Completed;
            }
            TaskEvent::TaskFailed { .. } => {
                self.status = TaskStatus::Failed;
            }
        }
        
        self.version += 1;
        Ok(())
    }
    
    fn get_pending_events(&self) -> &[Self::Event] {
        &self.pending_events
    }
    
    fn mark_events_committed(&mut self) {
        self.pending_events.clear();
    }
}
```

## CQRS Pattern

### Command and Query Separation
```rust
// Command side - Write model
pub struct TaskCommandHandler {
    event_store: Arc<EventStore>,
    aggregate_cache: Arc<RwLock<HashMap<String, TaskAggregate>>>,
}

impl TaskCommandHandler {
    pub async fn handle_create_task(&self, command: CreateTaskCommand) -> Result<String> {
        let task_id = Uuid::new_v4().to_string();
        let mut aggregate = TaskAggregate::new(task_id.clone());
        
        aggregate.create(command.title, command.description)?;
        
        // Save events
        let events = aggregate.get_pending_events()
            .iter()
            .map(|e| Box::new(e.clone()) as Box<dyn AggregateEvent>)
            .collect();
            
        self.event_store
            .append_events(&task_id, events, None)
            .await?;
            
        aggregate.mark_events_committed();
        
        // Cache the aggregate
        self.aggregate_cache
            .write()
            .await
            .insert(task_id.clone(), aggregate);
            
        Ok(task_id)
    }
    
    pub async fn handle_assign_task(&self, command: AssignTaskCommand) -> Result<()> {
        let mut aggregate = self.get_aggregate(&command.task_id).await?;
        
        aggregate.assign_to_agent(command.agent_id)?;
        
        let events = aggregate.get_pending_events()
            .iter()
            .map(|e| Box::new(e.clone()) as Box<dyn AggregateEvent>)
            .collect();
            
        self.event_store
            .append_events(&command.task_id, events, Some(aggregate.version() - 1))
            .await?;
            
        aggregate.mark_events_committed();
        
        Ok(())
    }
}

// Query side - Read model
pub struct TaskQueryHandler {
    pool: Arc<PgPool>,
}

#[derive(Debug, Serialize)]
pub struct TaskView {
    pub id: String,
    pub title: String,
    pub description: String,
    pub status: String,
    pub assigned_agent_name: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl TaskQueryHandler {
    pub async fn get_task(&self, id: &str) -> Result<Option<TaskView>> {
        let task = sqlx::query_as!(
            TaskView,
            r#"
            SELECT 
                t.id,
                t.title,
                t.description,
                t.status,
                a.name as assigned_agent_name,
                t.created_at,
                t.updated_at
            FROM task_views t
            LEFT JOIN agent_views a ON t.assigned_agent_id = a.id
            WHERE t.id = $1
            "#,
            id
        )
        .fetch_optional(&*self.pool)
        .await?;
        
        Ok(task)
    }
    
    pub async fn get_tasks_by_status(&self, status: &str) -> Result<Vec<TaskView>> {
        let tasks = sqlx::query_as!(
            TaskView,
            r#"
            SELECT 
                t.id,
                t.title,
                t.description,
                t.status,
                a.name as assigned_agent_name,
                t.created_at,
                t.updated_at
            FROM task_views t
            LEFT JOIN agent_views a ON t.assigned_agent_id = a.id
            WHERE t.status = $1
            ORDER BY t.created_at DESC
            "#,
            status
        )
        .fetch_all(&*self.pool)
        .await?;
        
        Ok(tasks)
    }
}
```

## Query Object Pattern

### Flexible Query Builder
```rust
#[derive(Default, Clone)]
pub struct QueryCriteria {
    conditions: Vec<Condition>,
    sort_orders: Vec<SortOrder>,
    limit: Option<i64>,
    offset: Option<i64>,
}

#[derive(Clone)]
pub struct Condition {
    field: String,
    operator: Operator,
    value: QueryValue,
}

#[derive(Clone)]
pub enum Operator {
    Equals,
    NotEquals,
    GreaterThan,
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    Like,
    In,
    IsNull,
    IsNotNull,
}

#[derive(Clone)]
pub enum QueryValue {
    String(String),
    Integer(i64),
    Float(f64),
    Boolean(bool),
    StringArray(Vec<String>),
    IntegerArray(Vec<i64>),
    Null,
}

impl QueryCriteria {
    pub fn new() -> Self {
        Default::default()
    }
    
    pub fn where_eq(mut self, field: &str, value: impl Into<QueryValue>) -> Self {
        self.conditions.push(Condition {
            field: field.to_string(),
            operator: Operator::Equals,
            value: value.into(),
        });
        self
    }
    
    pub fn where_in(mut self, field: &str, values: Vec<String>) -> Self {
        self.conditions.push(Condition {
            field: field.to_string(),
            operator: Operator::In,
            value: QueryValue::StringArray(values),
        });
        self
    }
    
    pub fn order_by(mut self, field: &str, direction: SortDirection) -> Self {
        self.sort_orders.push(SortOrder {
            field: field.to_string(),
            direction,
        });
        self
    }
    
    pub fn limit(mut self, limit: i64) -> Self {
        self.limit = Some(limit);
        self
    }
    
    pub fn offset(mut self, offset: i64) -> Self {
        self.offset = Some(offset);
        self
    }
}

pub struct QueryBuilder {
    table: String,
    select_fields: Vec<String>,
}

impl QueryBuilder {
    pub fn build_select(&self, criteria: &QueryCriteria) -> (String, Vec<QueryValue>) {
        let mut sql = format!("SELECT {} FROM {}", 
            self.select_fields.join(", "),
            self.table
        );
        
        let mut params = Vec::new();
        let mut param_count = 0;
        
        // WHERE clause
        if !criteria.conditions.is_empty() {
            sql.push_str(" WHERE ");
            let where_clauses: Vec<String> = criteria.conditions
                .iter()
                .map(|condition| {
                    param_count += 1;
                    params.push(condition.value.clone());
                    
                    match condition.operator {
                        Operator::Equals => format!("{} = ${}", condition.field, param_count),
                        Operator::GreaterThan => format!("{} > ${}", condition.field, param_count),
                        Operator::Like => format!("{} LIKE ${}", condition.field, param_count),
                        Operator::In => {
                            // Handle IN operator specially
                            if let QueryValue::StringArray(ref values) = condition.value {
                                let placeholders: Vec<String> = values.iter()
                                    .enumerate()
                                    .map(|(i, _)| format!("${}", param_count + i))
                                    .collect();
                                format!("{} IN ({})", condition.field, placeholders.join(", "))
                            } else {
                                format!("{} IN (${})", condition.field, param_count)
                            }
                        }
                        _ => format!("{} = ${}", condition.field, param_count),
                    }
                })
                .collect();
                
            sql.push_str(&where_clauses.join(" AND "));
        }
        
        // ORDER BY clause
        if !criteria.sort_orders.is_empty() {
            sql.push_str(" ORDER BY ");
            let order_clauses: Vec<String> = criteria.sort_orders
                .iter()
                .map(|order| format!("{} {}", order.field, order.direction))
                .collect();
            sql.push_str(&order_clauses.join(", "));
        }
        
        // LIMIT and OFFSET
        if let Some(limit) = criteria.limit {
            sql.push_str(&format!(" LIMIT {}", limit));
        }
        
        if let Some(offset) = criteria.offset {
            sql.push_str(&format!(" OFFSET {}", offset));
        }
        
        (sql, params)
    }
}
```

## Active Record Pattern

### Simple CRUD Operations
```rust
use sqlx::FromRow;

#[derive(Debug, FromRow, Serialize, Deserialize)]
pub struct Task {
    pub id: String,
    pub title: String,
    pub description: String,
    pub status: TaskStatus,
    pub assigned_agent_id: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Task {
    pub async fn find(pool: &PgPool, id: &str) -> Result<Option<Self>> {
        let task = sqlx::query_as!(
            Task,
            "SELECT * FROM tasks WHERE id = $1",
            id
        )
        .fetch_optional(pool)
        .await?;
        
        Ok(task)
    }
    
    pub async fn find_all(pool: &PgPool) -> Result<Vec<Self>> {
        let tasks = sqlx::query_as!(
            Task,
            "SELECT * FROM tasks ORDER BY created_at DESC"
        )
        .fetch_all(pool)
        .await?;
        
        Ok(tasks)
    }
    
    pub async fn find_by_status(pool: &PgPool, status: TaskStatus) -> Result<Vec<Self>> {
        let tasks = sqlx::query_as!(
            Task,
            "SELECT * FROM tasks WHERE status = $1 ORDER BY created_at DESC",
            status as TaskStatus
        )
        .fetch_all(pool)
        .await?;
        
        Ok(tasks)
    }
    
    pub async fn save(&mut self, pool: &PgPool) -> Result<()> {
        if self.created_at == DateTime::<Utc>::MIN_UTC {
            // New record
            self.id = Uuid::new_v4().to_string();
            self.created_at = Utc::now();
            self.updated_at = Utc::now();
            
            sqlx::query!(
                r#"
                INSERT INTO tasks (id, title, description, status, assigned_agent_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                "#,
                self.id,
                self.title,
                self.description,
                self.status as TaskStatus,
                self.assigned_agent_id,
                self.created_at,
                self.updated_at
            )
            .execute(pool)
            .await?;
        } else {
            // Update existing record
            self.updated_at = Utc::now();
            
            sqlx::query!(
                r#"
                UPDATE tasks 
                SET title = $2, description = $3, status = $4, 
                    assigned_agent_id = $5, updated_at = $6
                WHERE id = $1
                "#,
                self.id,
                self.title,
                self.description,
                self.status as TaskStatus,
                self.assigned_agent_id,
                self.updated_at
            )
            .execute(pool)
            .await?;
        }
        
        Ok(())
    }
    
    pub async fn delete(&self, pool: &PgPool) -> Result<()> {
        sqlx::query!(
            "DELETE FROM tasks WHERE id = $1",
            self.id
        )
        .execute(pool)
        .await?;
        
        Ok(())
    }
    
    // Business logic methods
    pub fn assign_to_agent(&mut self, agent_id: String) -> Result<()> {
        if self.status != TaskStatus::Pending {
            return Err(anyhow!("Task must be pending to assign"));
        }
        
        self.assigned_agent_id = Some(agent_id);
        Ok(())
    }
    
    pub fn start(&mut self) -> Result<()> {
        if self.assigned_agent_id.is_none() {
            return Err(anyhow!("Task must be assigned before starting"));
        }
        
        if self.status != TaskStatus::Pending {
            return Err(anyhow!("Task must be pending to start"));
        }
        
        self.status = TaskStatus::InProgress;
        Ok(())
    }
    
    pub fn complete(&mut self) -> Result<()> {
        if self.status != TaskStatus::InProgress {
            return Err(anyhow!("Task must be in progress to complete"));
        }
        
        self.status = TaskStatus::Completed;
        Ok(())
    }
}
```