# Configuration Source Hierarchy

## Configuration Provider Architecture

### Multi-Source Configuration Manager
```rust
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::RwLock;

pub struct ConfigurationManager {
    providers: Vec<Box<dyn ConfigProvider>>,
    cache: Arc<RwLock<ConfigCache>>,
    watchers: Vec<Box<dyn ConfigWatcher>>,
    validator: Arc<dyn ConfigValidator>,
}

#[async_trait]
pub trait ConfigProvider: Send + Sync {
    async fn load(&self) -> Result<ConfigValue>;
    fn priority(&self) -> u32;
    fn name(&self) -> &str;
    fn supports_watching(&self) -> bool { false }
}

#[async_trait]
pub trait ConfigWatcher: Send + Sync {
    async fn watch(&self) -> Result<ConfigChangeStream>;
    fn provider_name(&self) -> &str;
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ConfigValue {
    String(String),
    Integer(i64),
    Float(f64),
    <PERSON><PERSON>an(bool),
    Array(Vec<ConfigValue>),
    Object(HashMap<String, ConfigValue>),
    Null,
}

struct ConfigCache {
    values: HashMap<String, CachedValue>,
    last_updated: std::time::Instant,
}

struct CachedValue {
    value: ConfigValue,
    source: String,
    priority: u32,
    expires_at: Option<std::time::Instant>,
}

impl ConfigurationManager {
    pub fn new() -> Self {
        Self {
            providers: Vec::new(),
            cache: Arc::new(RwLock::new(ConfigCache {
                values: HashMap::new(),
                last_updated: std::time::Instant::now(),
            })),
            watchers: Vec::new(),
            validator: Arc::new(DefaultConfigValidator),
        }
    }
    
    pub fn add_provider(&mut self, provider: Box<dyn ConfigProvider>) {
        self.providers.push(provider);
        // Sort by priority (higher priority first)
        self.providers.sort_by(|a, b| b.priority().cmp(&a.priority()));
    }
    
    pub async fn load_configuration(&self) -> Result<()> {
        let mut merged_config = HashMap::new();
        
        // Load from all providers in priority order
        for provider in &self.providers {
            log::debug!("Loading configuration from provider: {}", provider.name());
            
            match provider.load().await {
                Ok(config_value) => {
                    self.merge_config_value(&mut merged_config, config_value, provider)?;
                }
                Err(e) => {
                    log::warn!("Failed to load from provider {}: {}", provider.name(), e);
                    // Continue with other providers
                }
            }
        }
        
        // Validate merged configuration
        self.validator.validate(&merged_config).await?;
        
        // Update cache
        let mut cache = self.cache.write().await;
        cache.values.clear();
        
        for (key, value) in merged_config {
            cache.values.insert(key, value);
        }
        
        cache.last_updated = std::time::Instant::now();
        
        log::info!("Configuration loaded successfully from {} providers", self.providers.len());
        Ok(())
    }
    
    fn merge_config_value(
        &self,
        target: &mut HashMap<String, CachedValue>,
        value: ConfigValue,
        provider: &Box<dyn ConfigProvider>,
    ) -> Result<()> {
        self.flatten_config_value("", &value, target, provider)
    }
    
    fn flatten_config_value(
        &self,
        prefix: &str,
        value: &ConfigValue,
        target: &mut HashMap<String, CachedValue>,
        provider: &Box<dyn ConfigProvider>,
    ) -> Result<()> {
        match value {
            ConfigValue::Object(map) => {
                for (key, nested_value) in map {
                    let new_prefix = if prefix.is_empty() {
                        key.clone()
                    } else {
                        format!("{}.{}", prefix, key)
                    };
                    self.flatten_config_value(&new_prefix, nested_value, target, provider)?;
                }
            }
            _ => {
                // Only override if higher or equal priority
                if let Some(existing) = target.get(prefix) {
                    if provider.priority() < existing.priority {
                        return Ok(());
                    }
                }
                
                target.insert(prefix.to_string(), CachedValue {
                    value: value.clone(),
                    source: provider.name().to_string(),
                    priority: provider.priority(),
                    expires_at: None, // TODO: Implement TTL per provider
                });
            }
        }
        
        Ok(())
    }
    
    pub async fn get<T>(&self, key: &str) -> Result<Option<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        let cache = self.cache.read().await;
        
        if let Some(cached_value) = cache.values.get(key) {
            // Check if value has expired
            if let Some(expires_at) = cached_value.expires_at {
                if std::time::Instant::now() > expires_at {
                    drop(cache);
                    // Refresh configuration and retry
                    self.refresh_key(key).await?;
                    return self.get(key).await;
                }
            }
            
            let value = self.deserialize_config_value(&cached_value.value)?;
            Ok(Some(value))
        } else {
            Ok(None)
        }
    }
    
    pub async fn get_with_default<T>(&self, key: &str, default: T) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        match self.get(key).await? {
            Some(value) => Ok(value),
            None => Ok(default),
        }
    }
    
    async fn refresh_key(&self, key: &str) -> Result<()> {
        // Refresh specific key from all providers
        for provider in &self.providers {
            match provider.load().await {
                Ok(config_value) => {
                    let mut temp_map = HashMap::new();
                    self.merge_config_value(&mut temp_map, config_value, provider)?;
                    
                    if let Some(new_value) = temp_map.get(key) {
                        let mut cache = self.cache.write().await;
                        cache.values.insert(key.to_string(), new_value.clone());
                        break;
                    }
                }
                Err(e) => {
                    log::warn!("Failed to refresh from provider {}: {}", provider.name(), e);
                }
            }
        }
        
        Ok(())
    }
    
    fn deserialize_config_value<T>(&self, value: &ConfigValue) -> Result<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        let json_value = self.config_value_to_json(value)?;
        serde_json::from_value(json_value).map_err(Into::into)
    }
    
    fn config_value_to_json(&self, value: &ConfigValue) -> Result<serde_json::Value> {
        let json = match value {
            ConfigValue::String(s) => serde_json::Value::String(s.clone()),
            ConfigValue::Integer(i) => serde_json::Value::Number((*i).into()),
            ConfigValue::Float(f) => serde_json::Value::Number(
                serde_json::Number::from_f64(*f)
                    .ok_or_else(|| anyhow!("Invalid float value"))?
            ),
            ConfigValue::Boolean(b) => serde_json::Value::Bool(*b),
            ConfigValue::Array(arr) => {
                let json_arr: Result<Vec<_>> = arr
                    .iter()
                    .map(|v| self.config_value_to_json(v))
                    .collect();
                serde_json::Value::Array(json_arr?)
            }
            ConfigValue::Object(obj) => {
                let json_obj: Result<serde_json::Map<String, serde_json::Value>> = obj
                    .iter()
                    .map(|(k, v)| Ok((k.clone(), self.config_value_to_json(v)?)))
                    .collect();
                serde_json::Value::Object(json_obj?)
            }
            ConfigValue::Null => serde_json::Value::Null,
        };
        
        Ok(json)
    }
}
```

## Environment Variable Provider

### Environment Variable Configuration
```rust
use std::env;

pub struct EnvironmentProvider {
    prefix: Option<String>,
    case_sensitive: bool,
    priority: u32,
}

impl EnvironmentProvider {
    pub fn new() -> Self {
        Self {
            prefix: None,
            case_sensitive: false,
            priority: 80, // High priority
        }
    }
    
    pub fn with_prefix(mut self, prefix: String) -> Self {
        self.prefix = Some(prefix);
        self
    }
    
    pub fn case_sensitive(mut self, sensitive: bool) -> Self {
        self.case_sensitive = sensitive;
        self
    }
}

#[async_trait]
impl ConfigProvider for EnvironmentProvider {
    async fn load(&self) -> Result<ConfigValue> {
        let mut config = HashMap::new();
        
        for (key, value) in env::vars() {
            let processed_key = self.process_env_key(&key);
            
            if let Some(ref prefix) = self.prefix {
                if !processed_key.starts_with(&format!("{}_", prefix.to_uppercase())) {
                    continue;
                }
            }
            
            let config_key = self.env_key_to_config_key(&processed_key);
            let config_value = self.parse_env_value(&value);
            
            self.set_nested_value(&mut config, &config_key, config_value);
        }
        
        Ok(ConfigValue::Object(config))
    }
    
    fn priority(&self) -> u32 {
        self.priority
    }
    
    fn name(&self) -> &str {
        "environment"
    }
}

impl EnvironmentProvider {
    fn process_env_key(&self, key: &str) -> String {
        if self.case_sensitive {
            key.to_string()
        } else {
            key.to_uppercase()
        }
    }
    
    fn env_key_to_config_key(&self, env_key: &str) -> String {
        let mut key = env_key.to_lowercase();
        
        // Remove prefix if present
        if let Some(ref prefix) = self.prefix {
            let prefix_with_underscore = format!("{}_", prefix.to_lowercase());
            if key.starts_with(&prefix_with_underscore) {
                key = key[prefix_with_underscore.len()..].to_string();
            }
        }
        
        // Convert underscores to dots for nested structure
        key.replace('_', ".")
    }
    
    fn parse_env_value(&self, value: &str) -> ConfigValue {
        // Try to parse as different types
        
        // Boolean
        match value.to_lowercase().as_str() {
            "true" | "yes" | "1" | "on" => return ConfigValue::Boolean(true),
            "false" | "no" | "0" | "off" => return ConfigValue::Boolean(false),
            _ => {}
        }
        
        // Integer
        if let Ok(int_val) = value.parse::<i64>() {
            return ConfigValue::Integer(int_val);
        }
        
        // Float
        if let Ok(float_val) = value.parse::<f64>() {
            return ConfigValue::Float(float_val);
        }
        
        // Array (comma-separated)
        if value.contains(',') {
            let items: Vec<ConfigValue> = value
                .split(',')
                .map(|s| self.parse_env_value(s.trim()))
                .collect();
            return ConfigValue::Array(items);
        }
        
        // Default to string
        ConfigValue::String(value.to_string())
    }
    
    fn set_nested_value(
        &self,
        config: &mut HashMap<String, ConfigValue>,
        key: &str,
        value: ConfigValue,
    ) {
        let parts: Vec<&str> = key.split('.').collect();
        
        if parts.len() == 1 {
            config.insert(key.to_string(), value);
            return;
        }
        
        let mut current = config;
        
        for (i, part) in parts.iter().enumerate() {
            if i == parts.len() - 1 {
                // Last part - set the value
                current.insert(part.to_string(), value.clone());
            } else {
                // Intermediate part - ensure object exists
                let entry = current
                    .entry(part.to_string())
                    .or_insert_with(|| ConfigValue::Object(HashMap::new()));
                    
                match entry {
                    ConfigValue::Object(ref mut map) => {
                        current = map;
                    }
                    _ => {
                        // Override non-object with object
                        *entry = ConfigValue::Object(HashMap::new());
                        if let ConfigValue::Object(ref mut map) = entry {
                            current = map;
                        }
                    }
                }
            }
        }
    }
}
```

## File-Based Configuration

### TOML Configuration Provider
```rust
use tokio::fs;

pub struct TomlFileProvider {
    file_path: PathBuf,
    priority: u32,
    watch_for_changes: bool,
}

impl TomlFileProvider {
    pub fn new(file_path: PathBuf) -> Self {
        Self {
            file_path,
            priority: 60, // Medium priority
            watch_for_changes: true,
        }
    }
    
    pub fn with_priority(mut self, priority: u32) -> Self {
        self.priority = priority;
        self
    }
    
    pub fn without_watching(mut self) -> Self {
        self.watch_for_changes = false;
        self
    }
}

#[async_trait]
impl ConfigProvider for TomlFileProvider {
    async fn load(&self) -> Result<ConfigValue> {
        if !self.file_path.exists() {
            return Ok(ConfigValue::Object(HashMap::new()));
        }
        
        let content = fs::read_to_string(&self.file_path).await?;
        let toml_value: toml::Value = toml::from_str(&content)?;
        
        Ok(self.toml_to_config_value(toml_value))
    }
    
    fn priority(&self) -> u32 {
        self.priority
    }
    
    fn name(&self) -> &str {
        "toml_file"
    }
    
    fn supports_watching(&self) -> bool {
        self.watch_for_changes
    }
}

impl TomlFileProvider {
    fn toml_to_config_value(&self, value: toml::Value) -> ConfigValue {
        match value {
            toml::Value::String(s) => ConfigValue::String(s),
            toml::Value::Integer(i) => ConfigValue::Integer(i),
            toml::Value::Float(f) => ConfigValue::Float(f),
            toml::Value::Boolean(b) => ConfigValue::Boolean(b),
            toml::Value::Array(arr) => {
                let config_arr = arr
                    .into_iter()
                    .map(|v| self.toml_to_config_value(v))
                    .collect();
                ConfigValue::Array(config_arr)
            }
            toml::Value::Table(table) => {
                let mut config_obj = HashMap::new();
                for (key, value) in table {
                    config_obj.insert(key, self.toml_to_config_value(value));
                }
                ConfigValue::Object(config_obj)
            }
            toml::Value::Datetime(dt) => ConfigValue::String(dt.to_string()),
        }
    }
}
```

## Command Line Arguments

### CLI Argument Provider
```rust
use clap::{Arg, ArgMatches, Command};

pub struct CliArgumentProvider {
    matches: ArgMatches,
    priority: u32,
}

impl CliArgumentProvider {
    pub fn from_args() -> Result<Self> {
        let app = Self::build_cli_app();
        let matches = app.try_get_matches()?;
        
        Ok(Self {
            matches,
            priority: 100, // Highest priority
        })
    }
    
    fn build_cli_app() -> Command {
        Command::new("rust-agent-system")
            .version(env!("CARGO_PKG_VERSION"))
            .about("Multi-agent system with Rust backend")
            .arg(
                Arg::new("config")
                    .long("config")
                    .short('c')
                    .value_name("FILE")
                    .help("Configuration file path")
                    .num_args(1)
            )
            .arg(
                Arg::new("log-level")
                    .long("log-level")
                    .value_name("LEVEL")
                    .help("Log level (trace, debug, info, warn, error)")
                    .num_args(1)
            )
            .arg(
                Arg::new("port")
                    .long("port")
                    .short('p')
                    .value_name("PORT")
                    .help("Server port")
                    .num_args(1)
            )
            .arg(
                Arg::new("host")
                    .long("host")
                    .value_name("HOST")
                    .help("Server host")
                    .num_args(1)
            )
            .arg(
                Arg::new("workers")
                    .long("workers")
                    .value_name("NUM")
                    .help("Number of worker threads")
                    .num_args(1)
            )
    }
}

#[async_trait]
impl ConfigProvider for CliArgumentProvider {
    async fn load(&self) -> Result<ConfigValue> {
        let mut config = HashMap::new();
        
        // Map CLI arguments to configuration keys
        if let Some(config_file) = self.matches.get_one::<String>("config") {
            config.insert("config.file".to_string(), ConfigValue::String(config_file.clone()));
        }
        
        if let Some(log_level) = self.matches.get_one::<String>("log-level") {
            config.insert("logging.level".to_string(), ConfigValue::String(log_level.clone()));
        }
        
        if let Some(port) = self.matches.get_one::<String>("port") {
            let port_num: i64 = port.parse()?;
            config.insert("server.port".to_string(), ConfigValue::Integer(port_num));
        }
        
        if let Some(host) = self.matches.get_one::<String>("host") {
            config.insert("server.host".to_string(), ConfigValue::String(host.clone()));
        }
        
        if let Some(workers) = self.matches.get_one::<String>("workers") {
            let worker_count: i64 = workers.parse()?;
            config.insert("server.workers".to_string(), ConfigValue::Integer(worker_count));
        }
        
        Ok(ConfigValue::Object(config))
    }
    
    fn priority(&self) -> u32 {
        self.priority
    }
    
    fn name(&self) -> &str {
        "cli_arguments"
    }
}
```

## Remote Configuration

### Consul Configuration Provider
```rust
use reqwest::Client;
use base64::{Engine as _, engine::general_purpose};

pub struct ConsulProvider {
    client: Client,
    base_url: String,
    key_prefix: String,
    priority: u32,
    token: Option<String>,
}

impl ConsulProvider {
    pub fn new(base_url: String, key_prefix: String) -> Self {
        Self {
            client: Client::new(),
            base_url,
            key_prefix,
            priority: 40, // Lower priority than files
            token: None,
        }
    }
    
    pub fn with_token(mut self, token: String) -> Self {
        self.token = Some(token);
        self
    }
}

#[async_trait]
impl ConfigProvider for ConsulProvider {
    async fn load(&self) -> Result<ConfigValue> {
        let url = format!("{}/v1/kv/{}?recurse=true", self.base_url, self.key_prefix);
        
        let mut request = self.client.get(&url);
        
        if let Some(ref token) = self.token {
            request = request.header("X-Consul-Token", token);
        }
        
        let response = request.send().await?;
        
        if !response.status().is_success() {
            return Err(anyhow!("Consul request failed: {}", response.status()));
        }
        
        let consul_data: Vec<ConsulKVPair> = response.json().await?;
        let mut config = HashMap::new();
        
        for kv_pair in consul_data {
            if let Some(value) = kv_pair.value {
                let decoded_value = general_purpose::STANDARD.decode(value)?;
                let value_str = String::from_utf8(decoded_value)?;
                
                // Remove prefix from key
                let config_key = kv_pair.key
                    .strip_prefix(&format!("{}/", self.key_prefix))
                    .unwrap_or(&kv_pair.key)
                    .replace('/', ".");
                    
                let config_value = self.parse_consul_value(&value_str);
                self.set_nested_config_value(&mut config, &config_key, config_value);
            }
        }
        
        Ok(ConfigValue::Object(config))
    }
    
    fn priority(&self) -> u32 {
        self.priority
    }
    
    fn name(&self) -> &str {
        "consul"
    }
    
    fn supports_watching(&self) -> bool {
        true
    }
}

#[derive(Deserialize)]
struct ConsulKVPair {
    #[serde(rename = "Key")]
    key: String,
    #[serde(rename = "Value")]
    value: Option<String>,
}

impl ConsulProvider {
    fn parse_consul_value(&self, value: &str) -> ConfigValue {
        // Try parsing as JSON first
        if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(value) {
            return self.json_to_config_value(json_value);
        }
        
        // Fall back to string parsing (similar to environment variables)
        match value.to_lowercase().as_str() {
            "true" => ConfigValue::Boolean(true),
            "false" => ConfigValue::Boolean(false),
            _ => {
                if let Ok(int_val) = value.parse::<i64>() {
                    ConfigValue::Integer(int_val)
                } else if let Ok(float_val) = value.parse::<f64>() {
                    ConfigValue::Float(float_val)
                } else {
                    ConfigValue::String(value.to_string())
                }
            }
        }
    }
    
    fn json_to_config_value(&self, value: serde_json::Value) -> ConfigValue {
        match value {
            serde_json::Value::String(s) => ConfigValue::String(s),
            serde_json::Value::Number(n) => {
                if let Some(i) = n.as_i64() {
                    ConfigValue::Integer(i)
                } else if let Some(f) = n.as_f64() {
                    ConfigValue::Float(f)
                } else {
                    ConfigValue::String(n.to_string())
                }
            }
            serde_json::Value::Bool(b) => ConfigValue::Boolean(b),
            serde_json::Value::Array(arr) => {
                let config_arr = arr
                    .into_iter()
                    .map(|v| self.json_to_config_value(v))
                    .collect();
                ConfigValue::Array(config_arr)
            }
            serde_json::Value::Object(obj) => {
                let mut config_obj = HashMap::new();
                for (key, value) in obj {
                    config_obj.insert(key, self.json_to_config_value(value));
                }
                ConfigValue::Object(config_obj)
            }
            serde_json::Value::Null => ConfigValue::Null,
        }
    }
    
    fn set_nested_config_value(
        &self,
        config: &mut HashMap<String, ConfigValue>,
        key: &str,
        value: ConfigValue,
    ) {
        let parts: Vec<&str> = key.split('.').collect();
        
        if parts.len() == 1 {
            config.insert(key.to_string(), value);
            return;
        }
        
        let mut current = config;
        
        for (i, part) in parts.iter().enumerate() {
            if i == parts.len() - 1 {
                current.insert(part.to_string(), value.clone());
            } else {
                let entry = current
                    .entry(part.to_string())
                    .or_insert_with(|| ConfigValue::Object(HashMap::new()));
                    
                match entry {
                    ConfigValue::Object(ref mut map) => {
                        current = map;
                    }
                    _ => {
                        *entry = ConfigValue::Object(HashMap::new());
                        if let ConfigValue::Object(ref mut map) = entry {
                            current = map;
                        }
                    }
                }
            }
        }
    }
}
```

## Configuration Watching

### File System Watcher
```rust
use notify::{Watcher, RecommendedWatcher, RecursiveMode, Event, EventKind};
use tokio::sync::mpsc;

pub struct FileWatcher {
    file_path: PathBuf,
    sender: mpsc::UnboundedSender<ConfigChange>,
}

impl FileWatcher {
    pub fn new(file_path: PathBuf) -> (Self, mpsc::UnboundedReceiver<ConfigChange>) {
        let (sender, receiver) = mpsc::unbounded_channel();
        
        (
            Self { file_path, sender },
            receiver,
        )
    }
    
    pub async fn start_watching(&self) -> Result<()> {
        let sender = self.sender.clone();
        let file_path = self.file_path.clone();
        
        tokio::task::spawn(async move {
            let (tx, mut rx) = mpsc::unbounded_channel();
            
            let mut watcher = RecommendedWatcher::new(
                move |res: notify::Result<Event>| {
                    if let Ok(event) = res {
                        let _ = tx.send(event);
                    }
                },
                notify::Config::default(),
            ).unwrap();
            
            watcher.watch(&file_path, RecursiveMode::NonRecursive).unwrap();
            
            while let Some(event) = rx.recv().await {
                match event.kind {
                    EventKind::Modify(_) | EventKind::Create(_) => {
                        let change = ConfigChange {
                            source: "file_watcher".to_string(),
                            change_type: ConfigChangeType::Modified,
                            keys: vec![], // Would need to diff to get specific keys
                            timestamp: Utc::now(),
                        };
                        
                        if sender.send(change).is_err() {
                            break;
                        }
                    }
                    _ => {}
                }
            }
        });
        
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct ConfigChange {
    pub source: String,
    pub change_type: ConfigChangeType,
    pub keys: Vec<String>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub enum ConfigChangeType {
    Added,
    Modified,
    Removed,
}

#[async_trait]
impl ConfigWatcher for FileWatcher {
    async fn watch(&self) -> Result<ConfigChangeStream> {
        // Implementation would return a stream of changes
        todo!()
    }
    
    fn provider_name(&self) -> &str {
        "file_watcher"
    }
}
```